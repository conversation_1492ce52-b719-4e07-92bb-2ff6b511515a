import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import { 
  debugRevenueCatConfiguration, 
  getProducts, 
  purchaseSubscription,
  restorePurchases,
  checkSubscriptionStatus,
  IAP_PRODUCT_IDS 
} from '../services/appleIAPService';

export const IAPDebugScreen: React.FC = () => {
  const [debugOutput, setDebugOutput] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const addToOutput = (message: string) => {
    setDebugOutput(prev => prev + '\n' + message);
    console.log(message);
  };

  const clearOutput = () => {
    setDebugOutput('');
  };

  const runDebugConfiguration = async () => {
    setLoading(true);
    clearOutput();
    addToOutput('=== STARTING REVENUECAT DEBUG ===');
    
    try {
      await debugRevenueCatConfiguration();
      addToOutput('Debug completed - check console for detailed logs');
    } catch (error) {
      addToOutput(`Debug error: ${error.message}`);
    }
    
    setLoading(false);
  };

  const testGetProducts = async () => {
    setLoading(true);
    addToOutput('=== TESTING GET PRODUCTS ===');
    
    try {
      const products = await getProducts();
      addToOutput(`Found ${products.length} products:`);
      products.forEach(product => {
        addToOutput(`- ${product.product.identifier}: ${product.product.priceString}`);
      });
    } catch (error) {
      addToOutput(`Get products error: ${error.message}`);
    }
    
    setLoading(false);
  };

  const testPurchase = async (productId: string) => {
    setLoading(true);
    addToOutput(`=== TESTING PURCHASE: ${productId} ===`);
    
    try {
      const result = await purchaseSubscription(productId, 'test-user-123');
      addToOutput(`Purchase result: ${JSON.stringify(result)}`);
    } catch (error) {
      addToOutput(`Purchase error: ${error.message}`);
    }
    
    setLoading(false);
  };

  const testRestore = async () => {
    setLoading(true);
    addToOutput('=== TESTING RESTORE PURCHASES ===');
    
    try {
      const result = await restorePurchases('test-user-123');
      addToOutput(`Restore result: ${JSON.stringify(result)}`);
    } catch (error) {
      addToOutput(`Restore error: ${error.message}`);
    }
    
    setLoading(false);
  };

  const testSubscriptionStatus = async () => {
    setLoading(true);
    addToOutput('=== TESTING SUBSCRIPTION STATUS ===');
    
    try {
      const result = await checkSubscriptionStatus('test-user-123');
      addToOutput(`Status result: ${JSON.stringify(result)}`);
    } catch (error) {
      addToOutput(`Status error: ${error.message}`);
    }
    
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>IAP Debug Screen</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.debugButton]} 
          onPress={runDebugConfiguration}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Debug RevenueCat Config</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.testButton]} 
          onPress={testGetProducts}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Get Products</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.purchaseButton]} 
          onPress={() => testPurchase(IAP_PRODUCT_IDS.PRO_MONTHLY)}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Pro Monthly Purchase</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.purchaseButton]} 
          onPress={() => testPurchase(IAP_PRODUCT_IDS.PREMIUM_MONTHLY)}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Premium Monthly Purchase</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.restoreButton]} 
          onPress={testRestore}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Restore Purchases</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.statusButton]} 
          onPress={testSubscriptionStatus}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Test Subscription Status</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearOutput}
        >
          <Text style={styles.buttonText}>Clear Output</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.outputContainer}>
        <Text style={styles.outputText}>{debugOutput || 'No output yet. Run a test to see results.'}</Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  debugButton: {
    backgroundColor: '#007AFF',
  },
  testButton: {
    backgroundColor: '#34C759',
  },
  purchaseButton: {
    backgroundColor: '#FF9500',
  },
  restoreButton: {
    backgroundColor: '#5856D6',
  },
  statusButton: {
    backgroundColor: '#AF52DE',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  outputContainer: {
    flex: 1,
    backgroundColor: '#000',
    borderRadius: 8,
    padding: 10,
  },
  outputText: {
    color: '#00FF00',
    fontFamily: 'monospace',
    fontSize: 12,
  },
});

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Image, TouchableOpacity, TextInput, SafeAreaView, Dimensions, Keyboard, TouchableWithoutFeedback, Switch, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { countPills, PillCountResult, PillPrediction } from '../services/roboflowService';
import { useLanguage } from '../contexts/LanguageContext';
import { Text, Card, Button, Divider } from './ui';
import PillCountDisplay from './PillCountDisplay';
import PillVisualization from './PillVisualization';
import { Colors, Spacing, BorderRadius, Typography } from '../constants/PillLogicDesign';
import { saveMedicationAnalysis } from '../services/storageService';
import { useRouter } from 'expo-router';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { FeatureType } from '../lib/supabase';

interface PillCounterProps {
  imageUri: string;
  onRetake: () => void;
}

export default function PillCounter({ imageUri, onRetake }: PillCounterProps) {
  console.log('PillCounter component mounted with image URI:', imageUri ? imageUri.substring(0, 50) + '...' : 'none');

  const { t } = useLanguage();
  const router = useRouter();
  const {
    checkFeatureAccess,
    trackUsage,
    isPaidUser,
    getRemainingUsage,
    FEATURE_LIMITS
  } = useUsageLimits();

  // States for the different steps
  const [step, setStep] = useState<'preview' | 'results'>('preview');
  const [pillCount, setPillCount] = useState<number | null>(null);
  const [isEstimate, setIsEstimate] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [recordName, setRecordName] = useState('');
  const [saveMessage, setSaveMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [showVisualization, setShowVisualization] = useState<boolean>(false);
  const [predictions, setPredictions] = useState<PillPrediction[]>([]);
  const [visualizedImageUri, setVisualizedImageUri] = useState<string | null>(null);

  // Get remaining usage for pill scan
  const remainingUsage = getRemainingUsage(FeatureType.PILL_SCAN);

  // Log component initialization
  console.log('PillCounter initialized in', step, 'step');
  console.log('Current pill count:', pillCount, 'isEstimate:', isEstimate);
  // Don't log the full image URI as it might contain base64 data

  // Analyze the image when in preview mode
  const handleAnalyze = async () => {
    try {
      // Check if user can use the pill scan feature
      const canUse = await checkFeatureAccess(FeatureType.PILL_SCAN);
      if (!canUse) {
        return;
      }

      setIsLoading(true);
      setError(null);

      console.log('Starting pill counting analysis...');
      console.log('Image URI (truncated):', imageUri ? imageUri.substring(0, 50) + '...' : 'none');

      // Call with visualization
      const result = await countPills(imageUri, true);
      // Log only the relevant parts of the result, not the full object which may contain base64 data
      console.log('Pill count result:', {
        count: result.count,
        isEstimate: result.isEstimate,
        predictionsCount: result.predictions?.length || 0,
        hasVisualization: !!result.visualizedImageUri
      });

      // Track usage after successful analysis
      await trackUsage(FeatureType.PILL_SCAN);

      setPillCount(result.count);
      setIsEstimate(result.isEstimate);
      setPredictions(result.predictions || []);

      // Only show visualization if we have boxes
      if (result.visualizedImageUri) {
        console.log('Visualization image received from API (not logging URI to avoid base64 data in console)');
        setVisualizedImageUri(result.visualizedImageUri);
        setShowVisualization(true);
      } else if (result.predictions && result.predictions.length > 0) {
        // If we have predictions but no visualization image, still show visualization
        console.log('No visualization image, but we have predictions');
        setShowVisualization(true);
      } else {
        setShowVisualization(false);
      }

      console.log('Setting pill count to:', result.count, 'isEstimate:', result.isEstimate);

      // Move to results step
      setStep('results');
      console.log('Moved to results step');
    } catch (err) {
      console.error('Error during pill counting:', err);
      setError(`Failed to count pills: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
      console.log('Loading state set to false');
    }
  };

  // Reanalyze the image
  const handleReanalyze = async () => {
    try {
      // Check if user can use the pill scan feature
      const canUse = await checkFeatureAccess(FeatureType.PILL_SCAN);
      if (!canUse) {
        return;
      }

      setIsLoading(true);
      setError(null);
      console.log('Re-analyzing pill count...');
      console.log('Image URI for reanalysis (truncated):', imageUri ? imageUri.substring(0, 50) + '...' : 'none');

      // Call with visualization
      const result = await countPills(imageUri, true);
      // Log only the relevant parts of the result, not the full object which may contain base64 data
      console.log('Updated pill count:', {
        count: result.count,
        isEstimate: result.isEstimate,
        predictionsCount: result.predictions?.length || 0,
        hasVisualization: !!result.visualizedImageUri
      });

      // Track usage after successful reanalysis
      await trackUsage(FeatureType.PILL_SCAN);

      setPillCount(result.count);
      setIsEstimate(result.isEstimate);
      setPredictions(result.predictions || []);

      // Only show visualization if we have boxes
      if (result.visualizedImageUri) {
        console.log('Reanalyze: Visualization image received from API (not logging URI to avoid base64 data in console)');
        setVisualizedImageUri(result.visualizedImageUri);
        setShowVisualization(true);
      } else if (result.predictions && result.predictions.length > 0) {
        // If we have predictions but no visualization image, still show visualization
        console.log('Reanalyze: No visualization image, but we have predictions');
        setShowVisualization(true);
      } else {
        setShowVisualization(false);
      }

      console.log('Updated pill count to:', result.count, 'isEstimate:', result.isEstimate);
    } catch (err) {
      console.error('Error during pill counting:', err);
      setError(`Failed to count pills: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
      console.log('Reanalysis complete, loading state set to false');
    }
  };

  // Render the preview step
  const renderPreview = () => (
    <View style={styles.previewContainer}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} resizeMode="contain" />
      </View>

      {/* Usage information for free users */}
      {!isPaidUser && (
        <View style={styles.usageInfoContainer}>
          <Text style={styles.usageInfoText}>
            {t('pillScanUsageInfo', {
              remaining: remainingUsage,
              total: FEATURE_LIMITS.free[FeatureType.PILL_SCAN]
            })}
          </Text>
        </View>
      )}

      <View style={styles.actionButtonsContainer}>
        <Button
          title={t('retake')}
          variant="outline"
          icon="camera-outline"
          iconPosition="left"
          onPress={onRetake}
          style={styles.retakeButton}
        />
        <Button
          title={t('countPills')}
          variant="primary"
          icon="calculator-outline"
          iconPosition="left"
          onPress={handleAnalyze}
          loading={isLoading}
          style={styles.analyzeButton}
        />
      </View>
    </View>
  );

  // Render the results step
  const renderResults = () => (
    <View style={styles.resultsContainer}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={true}
      >
        <Card style={styles.resultCard} elevation="md">
          <Text variant="caption" color="tertiary" style={styles.resultLabel}>
            {t('totalPills')}
          </Text>

          {/* Use the dedicated PillCountDisplay component */}
          <PillCountDisplay count={pillCount} isEstimate={isEstimate} />

          {/* Toggle for showing visualization */}
          {predictions.length > 0 && (
            <View style={styles.visualizationToggleContainer}>
              <Text style={styles.visualizationToggleLabel}>
                {t('showDetections')}
              </Text>
              <Switch
                value={showVisualization}
                onValueChange={setShowVisualization}
                trackColor={{ false: Colors.border, true: Colors.docPurple.light }}
                thumbColor={showVisualization ? Colors.docPurple.DEFAULT : Colors.textTertiary}
              />
              {/* Debug button */}
              <TouchableOpacity
                style={styles.debugButton}
                onPress={() => {
                  console.log('DEBUG - Predictions count:', predictions.length);
                  if (predictions.length > 0) {
                    const { x, y, width, height, confidence, class: className } = predictions[0];
                    console.log('DEBUG - First prediction:', {
                      class: className,
                      confidence: (confidence * 100).toFixed(2) + '%',
                      position: { x: x.toFixed(3), y: y.toFixed(3) },
                      size: { width: width.toFixed(3), height: height.toFixed(3) }
                    });
                  }
                  console.log('DEBUG - Image URI (truncated):', imageUri ? imageUri.substring(0, 50) + '...' : 'none');
                }}
              >
                <Text style={styles.debugButtonText}>Debug</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Show visualization if enabled */}
          {showVisualization && (
            <View style={styles.visualizationContainer}>
              {visualizedImageUri ? (
                <Image
                  source={{ uri: visualizedImageUri }}
                  style={styles.visualizationImage}
                  resizeMode="contain"
                />
              ) : predictions.length > 0 ? (
                <PillVisualization
                  imageUri={imageUri}
                  predictions={predictions}
                />
              ) : (
                <Text style={styles.debugText}>
                  No visualization available
                </Text>
              )}
            </View>
          )}

          {/* Usage information for free users */}
          {!isPaidUser && (
            <View style={styles.usageInfoContainer}>
              <Text style={styles.usageInfoText}>
                {t('pillScanUsageInfo', {
                  remaining: remainingUsage,
                  total: FEATURE_LIMITS.free[FeatureType.PILL_SCAN]
                })}
              </Text>
            </View>
          )}

          <View style={styles.buttonContainer}>
            <Button
              title={t('retake')}
              variant="outline"
              icon="camera-outline"
              iconPosition="left"
              onPress={onRetake}
              style={styles.retakeButton}
            />
            <Button
              title={t('reanalyze')}
              variant="primary"
              icon="refresh-outline"
              iconPosition="left"
              onPress={handleReanalyze}
              loading={isLoading}
              style={styles.reanalyzeButton}
              disabled={remainingUsage <= 0 && !isPaidUser}
            />
          </View>
        </Card>

        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.inputContainer}>
            <TextInput
            style={styles.nameInput}
            placeholder={t('recordNameOptional')}
            value={recordName}
            onChangeText={setRecordName}
            onSubmitEditing={Keyboard.dismiss}
            returnKeyType="done"
          />
          <TouchableOpacity style={styles.copyButton}>
            <Ionicons name="copy-outline" size={16} color={Colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </TouchableWithoutFeedback>

      {saveMessage ? (
        <View style={styles.saveMessageContainer}>
          <Text style={styles.saveMessage}>
            {saveMessage}
          </Text>
        </View>
      ) : null}

      <View style={styles.actionButtonsContainer}>
        <Button
          title={t('cancel')}
          variant="outline"
          onPress={() => router.push('/')}
          style={styles.cancelButton}
        />
        <Button
          title={t('saveCount')}
          variant="primary"
          icon="save-outline"
          iconPosition="left"
          loading={isSaving}
          onPress={handleSave}
          style={styles.saveButton}
        />
      </View>
      </ScrollView>
    </View>
  );

  // Handle saving the pill count
  const handleSave = async () => {
    if (pillCount === null) {
      console.log('Cannot save: pill count is null');
      return;
    }

    // Dismiss keyboard immediately
    Keyboard.dismiss();

    try {
      setIsSaving(true);
      console.log('Saving pill count data...');
      console.log('Current pill count:', pillCount, 'isEstimate:', isEstimate);

      // Create a name if none provided
      const name = recordName.trim() || `Pill Count - ${new Date().toLocaleDateString()}`;
      console.log('Record name:', name);

      // Create a medication object with the pill count information
      const medication = {
        name: `${pillCount} ${t(pillCount === 1 ? 'pillDetected' : 'pillsDetected')}${isEstimate ? ` (${t('estimate')})` : ''}`,
        dosage: '',
        purpose: isEstimate ? t('pillCountEstimateNote') : t('pillCountNote'),
        fillDate: new Date().toLocaleDateString()
      };
      console.log('Created medication object:', medication);

      // Save the pill count as a medication analysis with a special type
      console.log('Calling saveMedicationAnalysis with type: pill-count');
      console.log('Using image URI (not logging full URI to avoid base64 data in console)');
      const savedAnalysis = await saveMedicationAnalysis(
        [medication],  // Pass as an array of medications (old format)
        name,          // Analysis name
        imageUri,      // Image URI
        'pill-count'   // Type - important for filtering in the summary screen
      );

      console.log('Pill count saved successfully with ID:', savedAnalysis.id);
      setSaveMessage(t('pillCountSaved'));

      // Show success message and then navigate to summary
      setTimeout(() => {
        setSaveMessage('');
        console.log('Navigating to summary tab');
        router.push('/summary');
      }, 1500);
    } catch (err) {
      console.error('Error saving pill count:', err);
      console.error('Error details:', JSON.stringify(err));
      setSaveMessage(t('errorSavingPillCount'));
    } finally {
      setIsSaving(false);
      console.log('Save operation completed, isSaving set to false');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/')}
        >
          <Ionicons name="arrow-back" size={16} color={Colors.docPurple.DEFAULT} />
          <Text style={styles.backText}>{t('backToHome')}</Text>
        </TouchableOpacity>

        <Text style={styles.title}>{t('pillCounter')}</Text>
      </View>

      <View style={styles.content}>
        {step === 'preview' ? renderPreview() : renderResults()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.md,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  backText: {
    marginLeft: Spacing.xs,
    color: Colors.docPurple.DEFAULT,
    fontSize: 14,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  usageInfoContainer: {
    backgroundColor: Colors.docPurple.light,
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
  },
  usageInfoText: {
    fontSize: 14,
    color: Colors.docPurple.dark,
    textAlign: 'center',
  },

  // Preview step styles
  previewContainer: {
    flex: 1,
    gap: Spacing.md,
  },
  imageContainer: {
    flex: 1,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  retakeButtonContainer: {
    position: 'absolute',
    top: Spacing.md,
    left: Spacing.md,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: BorderRadius.md,
    padding: Spacing.xs,
    flexDirection: 'row',
    alignItems: 'center',
  },
  retakeButtonText: {
    color: 'white',
    marginLeft: Spacing.xs,
    fontSize: 14,
    fontWeight: '500',
  },
  analyzeButton: {
    minWidth: 160,
    backgroundColor: Colors.docPurple.DEFAULT,
  },
  retakeButton: {
    minWidth: 160, // Same as reanalyzeButton
    marginTop: Spacing.md, // Match reanalyzeButton
  },

  // Results step styles
  resultsContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    gap: Spacing.md,
    paddingBottom: 100, // Add padding at the bottom for better scrolling
  },
  resultCard: {
    padding: Spacing.lg,
    alignItems: 'center',
    width: '100%',
    minHeight: 250, // Ensure enough height for the pill count
  },
  resultLabel: {
    fontSize: 16,
    color: Colors.textTertiary,
    marginBottom: Spacing.md,
    fontWeight: '500',
  },
  pillCountContainer: {
    alignItems: 'center',
    marginVertical: Spacing.md,
    width: '100%',
    backgroundColor: Colors.docPurple.light,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
    minHeight: 200,
    justifyContent: 'center',
  },
  pillCountResult: {
    fontSize: 72, // Reduced from 150 to more reasonable size
    fontWeight: '800',
    color: Colors.docPurple.DEFAULT,
    textAlign: 'center',
    marginVertical: Spacing.lg,
  },
  directCountContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    padding: Spacing.lg,
  },
  debugText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  estimateContainer: {
    backgroundColor: Colors.warning,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
  },
  estimateText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  reanalyzeButton: {
    marginTop: Spacing.md,
    minWidth: 160,
    backgroundColor: Colors.docPurple.DEFAULT,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  nameInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    fontSize: 16,
    backgroundColor: Colors.white,
  },
  copyButton: {
    width: 40,
    height: 40,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.white,
  },
  saveMessageContainer: {
    backgroundColor: '#d1fae5',
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  saveMessage: {
    color: '#065f46',
    textAlign: 'center',
    fontSize: 14,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.md,
    marginTop: 'auto',
    marginBottom: 60,
    paddingVertical: Spacing.md,
  },
  cancelButton: {
    minWidth: 120,
  },
  saveButton: {
    minWidth: 160,
    backgroundColor: Colors.docPurple.DEFAULT,
  },
  visualizationToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: Spacing.md,
    gap: Spacing.sm,
  },
  visualizationToggleLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  debugButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  debugButtonText: {
    fontSize: 12,
    color: '#666',
  },
  visualizationContainer: {
    width: '100%',
    marginVertical: Spacing.md,
  },
  visualizationImage: {
    width: '100%',
    height: 500, // Increased from 400 to 500 for better readability
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#f8f8f8',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between', // Changed from center to space-between
    gap: Spacing.md,
    marginTop: Spacing.md,
    width: '100%',
  },
});

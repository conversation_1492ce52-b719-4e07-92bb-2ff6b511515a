// Minimal implementation of the Node.js dgram module for React Native
const { EventEmitter } = require('./events');

class Socket extends EventEmitter {
  constructor(options) {
    super();
    this.type = options && options.type || 'udp4';
    this.connected = false;
    this.address = null;
  }

  bind(port, address, callback) {
    if (typeof port === 'object') {
      callback = address;
      address = null;
      port = port.port;
      address = port.address;
    } else if (typeof address === 'function') {
      callback = address;
      address = null;
    }
    
    this.address = { port, address: address || '0.0.0.0', family: 'IPv4' };
    
    if (callback) {
      this.once('listening', callback);
    }
    
    process.nextTick(() => {
      this.emit('listening');
    });
    
    return this;
  }

  connect(port, address, callback) {
    if (typeof address === 'function') {
      callback = address;
      address = null;
    }
    
    this.connected = true;
    
    if (callback) {
      this.once('connect', callback);
    }
    
    process.nextTick(() => {
      this.emit('connect');
    });
    
    return this;
  }

  disconnect() {
    this.connected = false;
    return this;
  }

  send(msg, offset, length, port, address, callback) {
    if (typeof offset === 'function') {
      callback = offset;
      offset = 0;
      length = msg.length;
      port = null;
      address = null;
    } else if (typeof length === 'function') {
      callback = length;
      length = msg.length - offset;
      port = null;
      address = null;
    } else if (typeof port === 'function') {
      callback = port;
      port = null;
      address = null;
    } else if (typeof address === 'function') {
      callback = address;
      address = null;
    }
    
    if (callback) {
      process.nextTick(() => {
        callback(null);
      });
    }
    
    return this;
  }

  close(callback) {
    if (callback) {
      this.once('close', callback);
    }
    
    process.nextTick(() => {
      this.emit('close');
    });
    
    return this;
  }

  address() {
    return this.address || { port: 0, address: '0.0.0.0', family: 'IPv4' };
  }

  setBroadcast(flag) {
    return this;
  }

  setTTL(ttl) {
    return this;
  }

  setMulticastTTL(ttl) {
    return this;
  }

  setMulticastLoopback(flag) {
    return this;
  }

  setMulticastInterface(multicastInterface) {
    return this;
  }

  addMembership(multicastAddress, multicastInterface) {
    return this;
  }

  dropMembership(multicastAddress, multicastInterface) {
    return this;
  }

  ref() {
    return this;
  }

  unref() {
    return this;
  }
}

function createSocket(options, callback) {
  if (typeof options === 'string') {
    options = { type: options };
  }
  
  const socket = new Socket(options);
  
  if (callback) {
    socket.on('message', callback);
  }
  
  return socket;
}

module.exports = {
  Socket,
  createSocket
};

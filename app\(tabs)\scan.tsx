import React, { useState } from 'react';
import { StyleSheet, SafeAreaView, StatusBar, Platform } from 'react-native';
import CameraViewComponent from '../../components/CameraView';
import ImageAnalysisNew from '../../components/ImageAnalysisNew';
import { Colors } from '../../constants/DesignSystem';

export default function ScanScreen() {
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const handleImageCaptured = (imageUri: string) => {
    setCapturedImage(imageUri);
  };

  const handleRetake = () => {
    setCapturedImage(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {capturedImage ? (
        <ImageAnalysisNew imageUri={capturedImage} onRetake={handleRetake} />
      ) : (
        <CameraViewComponent 
          onImageCaptured={handleImageCaptured} 
          cameraMode="prescription"
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
});

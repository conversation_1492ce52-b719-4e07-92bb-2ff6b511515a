import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AppleReceiptResponse {
  status: number
  receipt?: any
  latest_receipt_info?: any[]
  pending_renewal_info?: any[]
  environment?: string
}

// Apple's receipt validation endpoints
const APPLE_SANDBOX_URL = 'https://sandbox.itunes.apple.com/verifyReceipt'
const APPLE_PRODUCTION_URL = 'https://buy.itunes.apple.com/verifyReceipt'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify this is a cron job request (you can add authentication here)
    const authHeader = req.headers.get('Authorization')
    const cronSecret = Deno.env.get('CRON_SECRET')
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized cron request' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting periodic subscription validation...')

    // Get all active Apple subscriptions that need validation
    // Check subscriptions that haven't been validated in the last 24 hours
    const { data: subscriptions, error: fetchError } = await supabaseClient
      .from('profiles')
      .select('id, apple_transaction_id, apple_product_id, apple_receipt_data, validation_environment, last_receipt_validation, subscription_tier, subscription_status')
      .not('apple_transaction_id', 'is', null)
      .eq('subscription_status', 'active')
      .or('last_receipt_validation.is.null,last_receipt_validation.lt.' + new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (fetchError) {
      console.error('Error fetching subscriptions:', fetchError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch subscriptions' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found ${subscriptions?.length || 0} subscriptions to validate`)

    let validatedCount = 0
    let expiredCount = 0
    let errorCount = 0

    // Process each subscription
    for (const subscription of subscriptions || []) {
      try {
        const validationResult = await validateSubscriptionWithApple(
          subscription.apple_receipt_data,
          subscription.apple_product_id,
          subscription.apple_transaction_id,
          subscription.validation_environment || 'production'
        )

        if (validationResult.success) {
          if (validationResult.isActive) {
            // Subscription is still active, update last validation time
            await supabaseClient
              .from('profiles')
              .update({ 
                last_receipt_validation: new Date().toISOString(),
                subscription_expires_at: validationResult.expiresAt
              })
              .eq('id', subscription.id)
            
            validatedCount++
          } else {
            // Subscription has expired or been cancelled
            await supabaseClient
              .from('profiles')
              .update({
                subscription_status: 'expired',
                subscription_tier: 'free',
                last_receipt_validation: new Date().toISOString()
              })
              .eq('id', subscription.id)
            
            expiredCount++
          }

          // Log the validation result
          await supabaseClient
            .from('purchase_validation_log')
            .insert({
              user_id: subscription.id,
              transaction_id: subscription.apple_transaction_id,
              product_id: subscription.apple_product_id,
              validation_result: validationResult.isActive ? 'success' : 'expired',
              validation_environment: subscription.validation_environment,
              receipt_data_hash: await hashString(subscription.apple_receipt_data)
            })

        } else {
          console.error(`Validation failed for user ${subscription.id}:`, validationResult.error)
          errorCount++

          // Log the failed validation
          await supabaseClient
            .from('purchase_validation_log')
            .insert({
              user_id: subscription.id,
              transaction_id: subscription.apple_transaction_id,
              product_id: subscription.apple_product_id,
              validation_result: 'failed',
              validation_environment: subscription.validation_environment,
              error_message: validationResult.error,
              receipt_data_hash: await hashString(subscription.apple_receipt_data)
            })
        }

        // Add small delay to avoid overwhelming Apple's servers
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`Error processing subscription for user ${subscription.id}:`, error)
        errorCount++
      }
    }

    const result = {
      success: true,
      message: 'Periodic validation completed',
      stats: {
        total_checked: subscriptions?.length || 0,
        validated: validatedCount,
        expired: expiredCount,
        errors: errorCount
      }
    }

    console.log('Periodic validation completed:', result.stats)

    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error in cron job:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        success: false 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function validateSubscriptionWithApple(
  receiptData: string,
  productId: string,
  transactionId: string,
  environment: string
): Promise<{ success: boolean; isActive: boolean; expiresAt?: string; error?: string }> {
  
  const appleSharedSecret = Deno.env.get('APPLE_SHARED_SECRET')
  if (!appleSharedSecret) {
    console.error('APPLE_SHARED_SECRET environment variable is not set')
    return {
      success: false,
      isActive: false,
      error: 'Server configuration error: Apple shared secret not configured'
    }
  }

  const requestBody = {
    'receipt-data': receiptData,
    'password': appleSharedSecret,
    'exclude-old-transactions': false // Include all transactions for subscription status
  }

  try {
    // Always try production first as recommended by Apple, regardless of stored environment
    console.log('Validating subscription receipt with Apple production servers...')
    let response = await callAppleAPI(APPLE_PRODUCTION_URL, requestBody)
    let actualEnvironment = 'production'

    // Status 21007 means "This receipt is from the test environment, but it was sent to the production environment for verification"
    if (response.status === 21007) {
      console.log('Production validation returned 21007, trying sandbox environment...')
      response = await callAppleAPI(APPLE_SANDBOX_URL, requestBody)
      actualEnvironment = 'sandbox'
    }

    // Status 21008 means "This receipt is from the production environment, but it was sent to the test environment for verification"
    else if (response.status === 21008) {
      console.log('Received status 21008 - production receipt sent to test environment')
      // Keep the production response, this is the correct environment
      actualEnvironment = 'production'
    }

    // Check response status
    if (response.status !== 0) {
      const errorMessages: { [key: number]: string } = {
        21000: 'The App Store could not read the JSON object you provided.',
        21002: 'The data in the receipt-data property was malformed or missing.',
        21003: 'The receipt could not be authenticated.',
        21004: 'The shared secret you provided does not match the shared secret on file for your account.',
        21005: 'The receipt server is not currently available.',
        21006: 'This receipt is valid but the subscription has expired.',
        21007: 'This receipt is from the test environment, but it was sent to the production environment for verification.',
        21008: 'This receipt is from the production environment, but it was sent to the test environment for verification.',
        21009: 'Internal data access error.',
        21010: 'The user account cannot be found or has been deleted.'
      }

      const errorMessage = errorMessages[response.status] || `Unknown Apple validation error: ${response.status}`
      console.error('Apple subscription validation failed:', errorMessage)

      return {
        success: false,
        isActive: false,
        error: errorMessage
      }
    }

    // Check latest receipt info for subscription status
    const latestReceiptInfo = response.latest_receipt_info || []
    const pendingRenewalInfo = response.pending_renewal_info || []
    
    // Find the latest transaction for this product
    const relevantTransactions = latestReceiptInfo.filter((transaction: any) => 
      transaction.product_id === productId
    )

    if (relevantTransactions.length === 0) {
      return { 
        success: false, 
        isActive: false,
        error: 'No transactions found for this product' 
      }
    }

    // Get the most recent transaction
    const latestTransaction = relevantTransactions.reduce((latest: any, current: any) => {
      return parseInt(current.expires_date_ms) > parseInt(latest.expires_date_ms) ? current : latest
    })

    // Check if subscription is still active
    const expirationDate = new Date(parseInt(latestTransaction.expires_date_ms))
    const isActive = expirationDate > new Date()

    // Check for cancellation
    const renewalInfo = pendingRenewalInfo.find((info: any) => info.product_id === productId)
    const isCancelled = renewalInfo?.expiration_intent === '1'

    return {
      success: true,
      isActive: isActive && !isCancelled,
      expiresAt: expirationDate.toISOString()
    }

  } catch (error) {
    return {
      success: false,
      isActive: false,
      error: `API call failed: ${error.message}`
    }
  }
}

async function callAppleAPI(url: string, requestBody: any): Promise<AppleReceiptResponse> {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  if (!response.ok) {
    throw new Error(`Apple API request failed: ${response.status}`)
  }

  return await response.json()
}

async function hashString(input: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(input)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

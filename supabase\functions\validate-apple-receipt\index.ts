import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AppleReceiptValidationRequest {
  receiptData: string
  userId: string
  productId: string
  transactionId: string
  purchaseDate: string
}

interface AppleReceiptResponse {
  status: number
  receipt?: any
  latest_receipt_info?: any[]
  pending_renewal_info?: any[]
  environment?: string
}

// Apple's receipt validation endpoints
const APPLE_SANDBOX_URL = 'https://sandbox.itunes.apple.com/verifyReceipt'
const APPLE_PRODUCTION_URL = 'https://buy.itunes.apple.com/verifyReceipt'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const body: AppleReceiptValidationRequest = await req.json()
    const { receiptData, userId, productId, transactionId, purchaseDate } = body

    // Validate required fields
    if (!receiptData || !userId || !productId || !transactionId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Ensure the user can only validate their own receipts
    if (user.id !== userId) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: Cannot validate receipt for another user' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if transaction ID already exists to prevent duplicate processing
    const { data: existingTransaction, error: checkError } = await supabaseClient
      .from('profiles')
      .select('apple_transaction_id')
      .eq('apple_transaction_id', transactionId)
      .single()

    if (existingTransaction && !checkError) {
      return new Response(
        JSON.stringify({ 
          error: 'Transaction already processed',
          success: false 
        }),
        { status: 409, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Log validation attempt for monitoring
    console.log('=== RECEIPT VALIDATION ATTEMPT ===')
    console.log('User ID:', userId)
    console.log('Product ID:', productId)
    console.log('Transaction ID:', transactionId)
    console.log('Receipt Data Length:', receiptData?.length || 0)
    console.log('Timestamp:', new Date().toISOString())

    // Validate receipt with Apple's servers
    const validationResult = await validateReceiptWithApple(receiptData, productId, transactionId)

    // Log validation result for monitoring
    console.log('=== VALIDATION RESULT ===')
    console.log('Success:', validationResult.success)
    console.log('Environment:', validationResult.environment)
    console.log('Error:', validationResult.error || 'None')
    console.log('=====================================')

    if (!validationResult.success) {
      // Log failed validation attempt with more context
      console.error('❌ Receipt validation failed for user:', userId)
      console.error('Product:', productId)
      console.error('Error:', validationResult.error)

      // Store failed validation attempt for analysis
      try {
        await supabaseClient
          .from('validation_logs')
          .insert({
            user_id: userId,
            product_id: productId,
            transaction_id: transactionId,
            success: false,
            error_message: validationResult.error,
            environment: validationResult.environment || 'unknown',
            created_at: new Date().toISOString()
          })
      } catch (logError) {
        console.error('Failed to log validation attempt:', logError)
      }

      return new Response(
        JSON.stringify({
          error: validationResult.error,
          success: false
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Log successful validation
    console.log('✅ Receipt validation successful for user:', userId)
    console.log('Environment used:', validationResult.environment)
    console.log('Product:', productId)

    // Store successful validation for monitoring
    try {
      await supabaseClient
        .from('validation_logs')
        .insert({
          user_id: userId,
          product_id: productId,
          transaction_id: transactionId,
          success: true,
          error_message: null,
          environment: validationResult.environment,
          created_at: new Date().toISOString()
        })
    } catch (logError) {
      console.error('Failed to log successful validation:', logError)
    }

    // Update subscription in database
    const subscriptionTier = getSubscriptionTier(productId)
    const { data, error: updateError } = await supabaseClient.rpc('update_apple_subscription_secure', {
      user_uuid: userId,
      product_id: productId,
      transaction_id: transactionId,
      receipt_data: receiptData,
      subscription_tier: subscriptionTier,
      purchase_date: purchaseDate,
      validation_environment: validationResult.environment
    })

    if (updateError) {
      console.error('❌ Database update error for user:', userId)
      console.error('Error details:', updateError)
      return new Response(
        JSON.stringify({
          error: 'Failed to update subscription',
          success: false
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Receipt validated and subscription updated successfully',
        environment: validationResult.environment
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        success: false 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function validateReceiptWithApple(
  receiptData: string,
  expectedProductId: string,
  expectedTransactionId: string
): Promise<{ success: boolean; error?: string; environment?: string }> {

  const appleSharedSecret = Deno.env.get('APPLE_SHARED_SECRET')
  if (!appleSharedSecret) {
    console.error('APPLE_SHARED_SECRET environment variable is not set')
    return {
      success: false,
      error: 'Server configuration error: Apple shared secret not configured'
    }
  }

  const requestBody = {
    'receipt-data': receiptData,
    'password': appleSharedSecret,
    'exclude-old-transactions': true
  }

  let response: AppleReceiptResponse
  let environment = 'production'

  try {
    // Always try production first as recommended by Apple
    console.log('Validating receipt with Apple production servers...')
    response = await callAppleAPI(APPLE_PRODUCTION_URL, requestBody)

    // Status 21007 means "This receipt is from the test environment, but it was sent to the production environment for verification"
    if (response.status === 21007) {
      console.log('Production validation returned 21007, trying sandbox environment...')
      response = await callAppleAPI(APPLE_SANDBOX_URL, requestBody)
      environment = 'sandbox'
    }

    // Status 21008 means "This receipt is from the production environment, but it was sent to the test environment for verification"
    else if (response.status === 21008) {
      console.log('Received status 21008 - production receipt sent to test environment')
      // Keep the production response, this is the correct environment
      environment = 'production'
    }
  } catch (error) {
    console.error('Error calling Apple API:', error)
    return {
      success: false,
      error: `Failed to connect to Apple servers: ${error.message}`
    }
  }

  // Check response status
  if (response.status !== 0) {
    const errorMessages: { [key: number]: string } = {
      21000: 'The App Store could not read the JSON object you provided.',
      21002: 'The data in the receipt-data property was malformed or missing.',
      21003: 'The receipt could not be authenticated.',
      21004: 'The shared secret you provided does not match the shared secret on file for your account.',
      21005: 'The receipt server is not currently available.',
      21006: 'This receipt is valid but the subscription has expired.',
      21007: 'This receipt is from the test environment, but it was sent to the production environment for verification.',
      21008: 'This receipt is from the production environment, but it was sent to the test environment for verification.',
      21009: 'Internal data access error.',
      21010: 'The user account cannot be found or has been deleted.'
    }

    const errorMessage = errorMessages[response.status] || `Unknown Apple validation error: ${response.status}`
    console.error('Apple validation failed:', errorMessage)

    return {
      success: false,
      error: errorMessage
    }
  }

  // Validate the receipt contains our expected transaction
  const receiptInfo = response.receipt
  const latestReceiptInfo = response.latest_receipt_info || []
  
  // Check in-app purchases in the receipt
  const inAppPurchases = receiptInfo?.in_app || []
  const allTransactions = [...inAppPurchases, ...latestReceiptInfo]
  
  const matchingTransaction = allTransactions.find((transaction: any) => 
    transaction.transaction_id === expectedTransactionId &&
    transaction.product_id === expectedProductId
  )

  if (!matchingTransaction) {
    return { 
      success: false, 
      error: 'Transaction not found in validated receipt' 
    }
  }

  // Additional validation: check if subscription is still active
  if (matchingTransaction.expires_date_ms) {
    const expirationDate = new Date(parseInt(matchingTransaction.expires_date_ms))
    if (expirationDate < new Date()) {
      return { 
        success: false, 
        error: 'Subscription has expired' 
      }
    }
  }

  return { success: true, environment }
}

async function callAppleAPI(url: string, requestBody: any): Promise<AppleReceiptResponse> {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  if (!response.ok) {
    throw new Error(`Apple API request failed: ${response.status}`)
  }

  return await response.json()
}

function getSubscriptionTier(productId: string): string {
  const tierMap: { [key: string]: string } = {
    'com.pilllogic.app.pro.monthly': 'pro', // Legacy Product ID (keep for existing users)
    'com.pilllogic.app.pro.monthly.v2': 'pro', // New Product ID
    'com.pilllogic.app.premium.monthly': 'premium'
  }

  return tierMap[productId] || 'free'
}

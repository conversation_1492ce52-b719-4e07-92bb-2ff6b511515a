import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, StatusBar } from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing } from '../../constants/PillLogicDesign';
import Header from './Header';

interface AppShellProps {
  children: React.ReactNode;
}

const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();

  // Don't show bottom nav on camera pages
  const hideNav = pathname.includes('/camera') ||
                 pathname.includes('/scan');

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <Header />

      <View style={styles.main}>
        {children}
      </View>

      {!hideNav && (
        <View style={styles.bottomNav}>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => router.push('/')}
          >
            <Ionicons
              name="home-outline"
              size={24}
              color={pathname === '/' ? Colors.docPurple.DEFAULT : Colors.textSecondary}
            />
            <Text style={[
              styles.navText,
              pathname === '/' && styles.activeNavText
            ]}>
              Home
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.scanButton}
            onPress={() => router.push('/feature-select')}
          >
            <View style={styles.scanButtonInner}>
              <Ionicons name="camera-outline" size={28} color={Colors.white} />
            </View>
            <Text style={styles.scanText}>Scan</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => router.push('/summary')}
          >
            <Ionicons
              name="document-text-outline"
              size={24}
              color={pathname === '/summary' ? Colors.docPurple.DEFAULT : Colors.textSecondary}
            />
            <Text style={[
              styles.navText,
              pathname === '/summary' && styles.activeNavText
            ]}>
              Summary
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  main: {
    flex: 1,
    paddingTop: 110, // Increased space for lower header
    paddingBottom: 85, // Increased space for taller bottom nav
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 85, // Increased height
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 20, // More bottom padding for easier tapping
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    minWidth: 80, // Wider touch target
    minHeight: 60, // Taller touch target
  },
  navText: {
    fontSize: 14, // Larger text
    marginTop: 6,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  activeNavText: {
    color: Colors.docPurple.DEFAULT,
    fontWeight: '500',
  },
  scanButton: {
    alignItems: 'center',
    marginTop: -35, // Adjusted for taller nav
  },
  scanButtonInner: {
    width: 70, // Larger button
    height: 70, // Larger button
    borderRadius: 35, // Half of width/height
    backgroundColor: Colors.docPurple.DEFAULT,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  scanText: {
    fontSize: 14, // Larger text
    marginTop: 6,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
});

export default AppShell;

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Image, TouchableOpacity, ActivityIndicator, ScrollView, TextInput, Modal, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { analyzeImage } from '../services/openaiVisionService';
import MedicationTable, { Medication } from './MedicationTable';
import { parseMedicationInfo } from '../utils/medicationParser';
import { useLanguage } from '../contexts/LanguageContext';
import { translateText, translateMedications } from '../services/googleTranslateService';
import { Language } from '../services/languageTypes';
import { saveMedicationAnalysis } from '../services/storageService';
import { useRouter } from 'expo-router';
import { Text, Card, Container, Header, Button, Divider, Badge } from './ui';
import { Colors, Spacing, BorderRadius, Typography } from '../constants/DesignSystem';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { FeatureType } from '../lib/supabase';

interface ImageAnalysisProps {
  imageUri: string;
  onRetake: () => void;
}

export default function ImageAnalysisNew({ imageUri, onRetake }: ImageAnalysisProps) {
  const { language, t } = useLanguage();
  const router = useRouter();
  const { checkFeatureAccess, trackUsage } = useUsageLimits();
  const [analysis, setAnalysis] = useState<string>('');
  const [translatedAnalysis, setTranslatedAnalysis] = useState<string>('');
  const [medications, setMedications] = useState<Medication[]>([]);
  const [translatedMedications, setTranslatedMedications] = useState<Medication[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showRawResponse, setShowRawResponse] = useState<boolean>(false);
  const [saved, setSaved] = useState<boolean>(false);
  const [showNameModal, setShowNameModal] = useState<boolean>(false);
  const [analysisName, setAnalysisName] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'medications' | 'analysis'>('medications');

  // Handle saving the medication analysis
  const handleSave = async () => {
    if (saved) {
      // If already saved, navigate to summary tab
      router.push('/summary');
      return;
    }

    // Show the naming modal
    setShowNameModal(true);
  };

  // Handle the actual saving after naming
  const handleConfirmSave = async () => {
    setShowNameModal(false);
    setIsSaving(true);
    try {
      // Generate a default name if none provided
      const name = analysisName.trim() ||
        (medications.length > 0 ?
          `${medications[0].name} + ${medications.length - 1} more` :
          `Analysis ${new Date().toLocaleDateString()}`);

      // Save the medications to storage with the name
      await saveMedicationAnalysis(medications, name, imageUri);
      setSaved(true);
      // Reset the name for next time
      setAnalysisName('');

      // Show success message and navigate to summary after a short delay
      setTimeout(() => {
        console.log('Navigating to summary tab after save');
        router.push('/summary');
      }, 1000);
    } catch (error) {
      console.error('Error saving medication analysis:', error);
      setError(`Failed to save analysis: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle toggling raw response visibility
  const handleToggleRawResponse = async () => {
    // If we're about to show the raw response and we're not in English
    if (!showRawResponse && language !== Language.ENGLISH && translatedAnalysis === analysis) {
      // We need to translate the raw response first
      setIsTranslating(true);
      try {
        const translated = await translateText(analysis, language);
        setTranslatedAnalysis(translated);
      } catch (error) {
        console.error('Error translating raw response:', error);
      } finally {
        setIsTranslating(false);
      }
    }
    // Toggle the visibility
    setShowRawResponse(!showRawResponse);
  };

  useEffect(() => {
    const performAnalysis = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if user can use the note analysis feature
        const canUse = await checkFeatureAccess(FeatureType.NOTE_ANALYSIS);
        if (!canUse) {
          setIsLoading(false);
          return;
        }

        // Track usage of the note analysis feature
        await trackUsage(FeatureType.NOTE_ANALYSIS);

        // Call the OpenAI Vision API to analyze the image
        const result = await analyzeImage(imageUri);
        setAnalysis(result);

        // Parse the medication information from the response
        const parsedMedications = parseMedicationInfo(result);
        setMedications(parsedMedications);

        // Set content based on language
        if (language === Language.ENGLISH) {
          // For English, use the original content
          setTranslatedAnalysis(result);
          setTranslatedMedications(parsedMedications);
        } else {
          // For other languages, translate immediately
          console.log(`Initial translation to ${language}...`);
          setIsTranslating(true);
          try {
            // Only translate what's needed initially
            // Don't translate the raw analysis text yet unless it's visible
            if (showRawResponse) {
              const translated = await translateText(result, language);
              setTranslatedAnalysis(translated);
            } else {
              // Just set it to the original for now, will translate if user shows raw response
              setTranslatedAnalysis(result);
            }

            // Always translate the medications for the table
            const translatedMeds = await translateMedications(parsedMedications, language);
            console.log('Initial translation complete');
            setTranslatedMedications(translatedMeds);
          } catch (translationError) {
            console.error('Initial translation error:', translationError);
            // Fallback to English if translation fails
            setTranslatedAnalysis(result);
            setTranslatedMedications(parsedMedications);
          } finally {
            setIsTranslating(false);
          }
        }
      } catch (err) {
        console.error('Error during image analysis:', err);
        setError(`Failed to analyze image: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setIsLoading(false);
      }
    };

    if (imageUri) {
      performAnalysis();
    }
  }, [imageUri]);

  // Update translations when language changes
  useEffect(() => {
    const updateTranslations = async () => {
      if (!analysis) return;

      console.log('Language changed to', language, '- translating content');

      // For English, use the original content
      if (language === Language.ENGLISH) {
        setTranslatedAnalysis(analysis);
        setTranslatedMedications(medications);
        return;
      }

      // For other languages, translate immediately
      setIsTranslating(true);
      try {
        // Only translate what's visible to the user
        // If raw response is not shown, don't translate it yet
        let translatedText = analysis;
        if (showRawResponse) {
          console.log(`Translating analysis to ${language}...`);
          translatedText = await translateText(analysis, language);
        }
        setTranslatedAnalysis(translatedText);

        // Always translate the medications for the table
        console.log(`Translating medications to ${language}...`);
        const translatedMeds = await translateMedications(medications, language);
        console.log('Translation complete:', translatedMeds);
        setTranslatedMedications(translatedMeds);
      } catch (translationError) {
        console.error('Translation error:', translationError);
        // Fallback to English if translation fails
        setTranslatedAnalysis(analysis);
        setTranslatedMedications(medications);
      } finally {
        setIsTranslating(false);
      }
    };

    // Run translation immediately when language changes
    updateTranslations();
  }, [language, analysis, medications, showRawResponse]);

  // Render the naming modal
  const renderNamingModal = () => (
    <Modal
      visible={showNameModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowNameModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text variant="h3" weight="semibold" style={styles.modalTitle}>
            {t('nameYourAnalysis')}
          </Text>
          <TextInput
            style={styles.nameInput}
            placeholder={t('enterAnalysisName')}
            value={analysisName}
            onChangeText={setAnalysisName}
            autoFocus
          />
          <View style={styles.modalButtons}>
            <Button
              title={t('cancel')}
              variant="outline"
              size="medium"
              onPress={() => setShowNameModal(false)}
              style={styles.modalButton}
            />
            <Button
              title={t('save')}
              variant="primary"
              size="medium"
              onPress={handleConfirmSave}
              style={styles.modalButton}
              loading={isSaving}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  // Render loading state
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <Text variant="body" color="secondary" style={styles.loadingText}>
        {t('analyzing')}
      </Text>
    </View>
  );

  // Render error state
  const renderError = () => (
    <Card style={styles.errorCard}>
      <Ionicons name="alert-circle" size={48} color={Colors.error} />
      <Text variant="subtitle" weight="medium" color="error" style={styles.errorText}>
        {error}
      </Text>
      <Button
        title={t('tryAgain')}
        variant="outline"
        size="medium"
        onPress={onRetake}
        style={styles.errorButton}
      />
    </Card>
  );

  // Render the medications tab content
  const renderMedicationsTab = () => (
    <>
      {isTranslating ? (
        <Card style={styles.translatingCard}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text variant="body" weight="medium" color="primary" style={styles.translatingText}>
            {t('translating')}
          </Text>
        </Card>
      ) : translatedMedications.length > 0 ? (
        <>
          <Card style={styles.medicationsCard}>
            <Text variant="h3" weight="semibold" style={styles.sectionTitle}>
              {t('extractedMedications')}
            </Text>
            <MedicationTable medications={translatedMedications} />
          </Card>

          <View style={styles.noteContainer}>
            <Text variant="caption" color="tertiary" style={styles.noteText}>
              {t('note')}
            </Text>
          </View>
        </>
      ) : (
        <Card style={styles.emptyCard}>
          <Ionicons name="medical-outline" size={48} color={Colors.textTertiary} />
          <Text variant="subtitle" weight="medium" color="secondary" style={styles.emptyText}>
            {t('noMedicationsFound')}
          </Text>
        </Card>
      )}
    </>
  );

  // Render the analysis tab content
  const renderAnalysisTab = () => (
    <Card style={styles.analysisCard}>
      <Text variant="h3" weight="semibold" style={styles.sectionTitle}>
        {t('rawApiResponse')}
      </Text>
      {isTranslating ? (
        <View style={styles.translatingInlineContainer}>
          <ActivityIndicator size="small" color={Colors.primary} />
          <Text variant="body" color="primary" style={styles.translatingInlineText}>
            {t('translating')}
          </Text>
        </View>
      ) : (
        <Text variant="body" color="secondary" style={styles.analysisText}>
          {translatedAnalysis}
        </Text>
      )}
    </Card>
  );

  return (
    <Container scrollable={false} padded={false}>
      {renderNamingModal()}

      <Header
        title={t('medicationAnalysis')}
        showBackButton
        onBackPress={onRetake}
      />

      <ScrollView style={styles.scrollContent} contentContainerStyle={styles.contentContainer}>
        <Card style={styles.imageCard} elevation="md" padding="none">
          <View style={styles.imageContainer}>
            <Image source={{ uri: imageUri }} style={styles.image} resizeMode="contain" />
            <TouchableOpacity style={styles.retakeButtonOverlay} onPress={onRetake}>
              <Ionicons name="camera-outline" size={20} color="white" />
              <Text style={styles.retakeButtonText}>{t('retake')}</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {isLoading ? (
          renderLoading()
        ) : error ? (
          renderError()
        ) : (
          <>
            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[
                  styles.tabButton,
                  activeTab === 'medications' && styles.activeTabButton,
                ]}
                onPress={() => setActiveTab('medications')}
              >
                <Ionicons
                  name="medical-outline"
                  size={20}
                  color={activeTab === 'medications' ? Colors.primary : Colors.textSecondary}
                  style={styles.tabIcon}
                />
                <Text
                  variant="subtitle"
                  weight={activeTab === 'medications' ? 'semibold' : 'medium'}
                  color={activeTab === 'medications' ? 'primary' : 'secondary'}
                >
                  {t('medications')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.tabButton,
                  activeTab === 'analysis' && styles.activeTabButton,
                ]}
                onPress={() => setActiveTab('analysis')}
              >
                <Ionicons
                  name="document-text-outline"
                  size={20}
                  color={activeTab === 'analysis' ? Colors.primary : Colors.textSecondary}
                  style={styles.tabIcon}
                />
                <Text
                  variant="subtitle"
                  weight={activeTab === 'analysis' ? 'semibold' : 'medium'}
                  color={activeTab === 'analysis' ? 'primary' : 'secondary'}
                >
                  {t('analysis')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.tabContent}>
              {activeTab === 'medications' ? renderMedicationsTab() : renderAnalysisTab()}
            </View>

            <View style={styles.actionContainer}>
              <Button
                title={t('takeAnotherPhoto')}
                variant="outline"
                icon="camera-outline"
                iconPosition="left"
                size="md"
                style={styles.actionButton}
                onPress={onRetake}
              />

              {!isLoading && !error && medications.length > 0 && (
                <Button
                  title={saved ? t('viewInSummary') : t('saveAnalysis')}
                  variant={saved ? 'success' : 'primary'}
                  icon={saved ? "checkmark-circle-outline" : "save-outline"}
                  iconPosition="left"
                  size="md"
                  style={styles.actionButton}
                  onPress={handleSave}
                  loading={isSaving}
                />
              )}
            </View>
          </>
        )}
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
  },
  imageCard: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 250,
  },
  retakeButtonOverlay: {
    position: 'absolute',
    top: Spacing.md,
    left: Spacing.md,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  retakeButtonText: {
    color: 'white',
    marginLeft: Spacing.xs,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
    marginTop: Spacing.lg,
  },
  loadingText: {
    marginTop: Spacing.md,
  },
  errorCard: {
    marginHorizontal: Spacing.md,
    alignItems: 'center',
    padding: Spacing.lg,
  },
  errorText: {
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  errorButton: {
    marginTop: Spacing.md,
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.white,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: Colors.primary,
  },
  tabIcon: {
    marginRight: Spacing.xs,
  },
  tabContent: {
    marginBottom: Spacing.md,
  },
  medicationsCard: {
    marginHorizontal: Spacing.md,
  },
  translatingCard: {
    marginHorizontal: Spacing.md,
    alignItems: 'center',
    padding: Spacing.lg,
  },
  translatingText: {
    marginTop: Spacing.md,
  },
  translatingInlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.sm,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.md,
  },
  translatingInlineText: {
    marginLeft: Spacing.xs,
  },
  sectionTitle: {
    marginBottom: Spacing.md,
  },
  emptyCard: {
    marginHorizontal: Spacing.md,
    alignItems: 'center',
    padding: Spacing.lg,
  },
  emptyText: {
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  analysisCard: {
    marginHorizontal: Spacing.md,
  },
  analysisText: {
    lineHeight: Typography.lineHeight.md,
  },
  noteContainer: {
    marginHorizontal: Spacing.md,
    marginTop: Spacing.sm,
    padding: Spacing.sm,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.sm,
    borderLeftWidth: 3,
    borderLeftColor: Colors.info,
  },
  noteText: {
    fontStyle: 'italic',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: Spacing.md,
    marginTop: Spacing.lg,
    marginBottom: 60,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.lg,
    width: '80%',
    maxWidth: 400,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  nameInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    padding: Spacing.sm,
    marginBottom: Spacing.md,
    fontSize: Typography.fontSize.md,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
});

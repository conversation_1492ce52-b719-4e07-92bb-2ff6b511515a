// Minimal implementation of the Node.js os module for React Native

function arch() {
  return 'x64';
}

function cpus() {
  return Array(4).fill({
    model: 'CPU',
    speed: 2500,
    times: {
      user: 0,
      nice: 0,
      sys: 0,
      idle: 0,
      irq: 0
    }
  });
}

function endianness() {
  return 'LE';
}

function freemem() {
  return 1024 * 1024 * 1024; // 1GB
}

function homedir() {
  return '/home/<USER>';
}

function hostname() {
  return 'localhost';
}

function loadavg() {
  return [0, 0, 0];
}

function networkInterfaces() {
  return {
    lo: [
      {
        address: '127.0.0.1',
        netmask: '*********',
        family: 'IPv4',
        mac: '00:00:00:00:00:00',
        internal: true,
        cidr: '127.0.0.1/8'
      },
      {
        address: '::1',
        netmask: 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff',
        family: 'IPv6',
        mac: '00:00:00:00:00:00',
        internal: true,
        cidr: '::1/128'
      }
    ],
    eth0: [
      {
        address: '***********',
        netmask: '*************',
        family: 'IPv4',
        mac: '00:00:00:00:00:00',
        internal: false,
        cidr: '***********/24'
      }
    ]
  };
}

function platform() {
  return 'darwin';
}

function release() {
  return '10.0.0';
}

function tmpdir() {
  return '/tmp';
}

function totalmem() {
  return 8 * 1024 * 1024 * 1024; // 8GB
}

function type() {
  return 'Darwin';
}

function uptime() {
  return 3600; // 1 hour
}

function userInfo(options) {
  return {
    uid: 1000,
    gid: 1000,
    username: 'user',
    homedir: '/home/<USER>',
    shell: '/bin/bash'
  };
}

module.exports = {
  EOL: '\n',
  arch,
  cpus,
  endianness,
  freemem,
  homedir,
  hostname,
  loadavg,
  networkInterfaces,
  platform,
  release,
  tmpdir,
  totalmem,
  type,
  uptime,
  userInfo,
  constants: {
    UV_UDP_REUSEADDR: 4,
    signals: {
      SIGABRT: 6,
      SIGALRM: 14,
      SIGBUS: 10,
      SIGCHLD: 20,
      SIGCONT: 19,
      SIGFPE: 8,
      SIGHUP: 1,
      SIGILL: 4,
      SIGINT: 2,
      SIGIO: 29,
      SIGIOT: 6,
      SIGKILL: 9,
      SIGPIPE: 13,
      SIGPOLL: 29,
      SIGPROF: 27,
      SIGPWR: 30,
      SIGQUIT: 3,
      SIGSEGV: 11,
      SIGSTKFLT: 16,
      SIGSTOP: 17,
      SIGSYS: 12,
      SIGTERM: 15,
      SIGTRAP: 5,
      SIGTSTP: 18,
      SIGTTIN: 21,
      SIGTTOU: 22,
      SIGUNUSED: 31,
      SIGURG: 23,
      SIGUSR1: 10,
      SIGUSR2: 12,
      SIGVTALRM: 26,
      SIGWINCH: 28,
      SIGXCPU: 24,
      SIGXFSZ: 25
    },
    errno: {
      E2BIG: 7,
      EACCES: 13,
      EADDRINUSE: 98,
      EADDRNOTAVAIL: 99,
      EAFNOSUPPORT: 97,
      EAGAIN: 11,
      EALREADY: 114,
      EBADF: 9,
      EBADMSG: 74,
      EBUSY: 16,
      ECANCELED: 125,
      ECHILD: 10,
      ECONNABORTED: 103,
      ECONNREFUSED: 111,
      ECONNRESET: 104,
      EDEADLK: 35,
      EDESTADDRREQ: 89,
      EDOM: 33,
      EDQUOT: 122,
      EEXIST: 17,
      EFAULT: 14,
      EFBIG: 27,
      EHOSTUNREACH: 113,
      EIDRM: 43,
      EILSEQ: 84,
      EINPROGRESS: 115,
      EINTR: 4,
      EINVAL: 22,
      EIO: 5,
      EISCONN: 106,
      EISDIR: 21,
      ELOOP: 40,
      EMFILE: 24,
      EMLINK: 31,
      EMSGSIZE: 90,
      EMULTIHOP: 72,
      ENAMETOOLONG: 36,
      ENETDOWN: 100,
      ENETRESET: 102,
      ENETUNREACH: 101,
      ENFILE: 23,
      ENOBUFS: 105,
      ENODATA: 61,
      ENODEV: 19,
      ENOENT: 2,
      ENOEXEC: 8,
      ENOLCK: 37,
      ENOLINK: 67,
      ENOMEM: 12,
      ENOMSG: 42,
      ENOPROTOOPT: 92,
      ENOSPC: 28,
      ENOSR: 63,
      ENOSTR: 60,
      ENOSYS: 38,
      ENOTCONN: 107,
      ENOTDIR: 20,
      ENOTEMPTY: 39,
      ENOTSOCK: 88,
      ENOTSUP: 95,
      ENOTTY: 25,
      ENXIO: 6,
      EOPNOTSUPP: 95,
      EOVERFLOW: 75,
      EPERM: 1,
      EPIPE: 32,
      EPROTO: 71,
      EPROTONOSUPPORT: 93,
      EPROTOTYPE: 91,
      ERANGE: 34,
      EROFS: 30,
      ESPIPE: 29,
      ESRCH: 3,
      ESTALE: 116,
      ETIME: 62,
      ETIMEDOUT: 110,
      ETXTBSY: 26,
      EWOULDBLOCK: 11,
      EXDEV: 18
    },
    priority: {
      PRIORITY_LOW: 19,
      PRIORITY_BELOW_NORMAL: 10,
      PRIORITY_NORMAL: 0,
      PRIORITY_ABOVE_NORMAL: -7,
      PRIORITY_HIGH: -14,
      PRIORITY_HIGHEST: -20
    }
  }
};

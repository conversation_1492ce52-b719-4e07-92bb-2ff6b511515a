import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, ActivityIndicator, Alert, useWindowDimensions, RefreshControl, TextInput, Modal } from 'react-native';
import { getMedicationAnalyses, StoredAnalysis, MedicationData, deleteMedicationAnalysis, clearAllMedicationAnalyses, searchMedicationAnalyses } from '../services/storageService';
import { useLanguage } from '../contexts/LanguageContext';
import MedicationTable from './MedicationTable';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import { useFocusEffect } from 'expo-router';
import { translateMedications } from '../services/googleTranslateService';
import { Language } from '../services/languageTypes';

const MedicationSummary = () => {
  const [analyses, setAnalyses] = useState<StoredAnalysis[]>([]);
  const [originalAnalyses, setOriginalAnalyses] = useState<StoredAnalysis[]>([]);
  const [filteredAnalyses, setFilteredAnalyses] = useState<StoredAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searching, setSearching] = useState(false);
  const [translating, setTranslating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAnalysis, setSelectedAnalysis] = useState<StoredAnalysis | null>(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const { t, language } = useLanguage();
  const { width } = useWindowDimensions();
  const isTablet = width > 768;

  // Refresh data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Summary screen focused - refreshing data');
      loadAnalyses();
      return () => {};
    }, [])
  );

  // Initial load
  useEffect(() => {
    loadAnalyses();
  }, []);

  // Reload when language changes
  useEffect(() => {
    console.log('Language changed to', language, '- translating medication data');
    if (originalAnalyses.length > 0) {
      loadAnalyses();
    }
  }, [language]);

  const loadAnalyses = async () => {
    if (!refreshing) setLoading(true);
    try {
      console.log('Loading medication analyses from storage');
      const data = await getMedicationAnalyses();
      console.log(`Found ${data.length} saved analyses`);
      setOriginalAnalyses(data);

      // For English, use original data
      if (language === Language.ENGLISH) {
        setAnalyses(data);

        // Apply any existing search filter
        if (searchQuery) {
          handleSearch(searchQuery, data);
        } else {
          setFilteredAnalyses(data);
        }
      } else {
        // For other languages, translate the medications
        setTranslating(true);
        console.log(`Translating analyses to ${language}...`);

        // Translate each analysis's medications
        const translatedData = await Promise.all(data.map(async (analysis) => {
          const translatedMedications = await translateMedications(analysis.medications, language);
          return {
            ...analysis,
            medications: translatedMedications
          };
        }));

        setAnalyses(translatedData);

        // Apply any existing search filter
        if (searchQuery) {
          handleSearch(searchQuery, translatedData);
        } else {
          setFilteredAnalyses(translatedData);
        }
      }

      // If we have a selected analysis, make sure it's still in the list and update it
      if (selectedAnalysis) {
        const stillExists = data.find(a => a.id === selectedAnalysis.id);
        if (!stillExists) {
          setSelectedAnalysis(null);
          setDetailsVisible(false);
        } else if (language !== Language.ENGLISH) {
          // Update the selected analysis with translated medications
          const translatedMedications = await translateMedications(selectedAnalysis.medications, language);
          setSelectedAnalysis({
            ...selectedAnalysis,
            medications: translatedMedications
          });
        }
      }
    } catch (error) {
      console.error('Error loading analyses:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setSearching(false);
      setTranslating(false);
    }
  };

  const handleSearch = async (query: string, dataToSearch?: StoredAnalysis[]) => {
    setSearchQuery(query);
    setSearching(true);

    try {
      // Use provided data or search in all analyses
      const data = dataToSearch || analyses;

      if (!query.trim()) {
        setFilteredAnalyses(data);
        setSearching(false);
        return;
      }

      // Filter locally for instant results
      const lowerQuery = query.toLowerCase();
      const filtered = data.filter(analysis => {
        // Search by name (safely check for undefined)
        if (analysis.name && analysis.name.toLowerCase().includes(lowerQuery)) return true;

        // Search by date
        if (analysis.date) {
          const date = new Date(analysis.date);
          const dateStr = date.toLocaleDateString();
          if (dateStr.includes(lowerQuery)) return true;
        }

        // Search by medication names (safely check for undefined)
        if (analysis.medications && analysis.medications.length > 0) {
          const hasMedication = analysis.medications.some(med =>
            med && med.name && med.name.toLowerCase().includes(lowerQuery));
          if (hasMedication) return true;
        }

        return false;
      });

      setFilteredAnalyses(filtered);
    } catch (error) {
      console.error('Error searching analyses:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setFilteredAnalyses(analyses);
  };

  const handleDeleteAll = async () => {
    setShowDeleteAllModal(false);
    setLoading(true);
    try {
      await clearAllMedicationAnalyses();
      setAnalyses([]);
      setFilteredAnalyses([]);
      setSelectedAnalysis(null);
      setDetailsVisible(false);
    } catch (error) {
      console.error('Error clearing all analyses:', error);
      Alert.alert(t('error'), t('errorClearingAnalyses'));
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadAnalyses();
  }, []);

  const handleDeleteAnalysis = async (id: string) => {
    Alert.alert(
      t('confirmDelete'),
      t('deleteAnalysisConfirm'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteMedicationAnalysis(id);
              // If the deleted analysis was selected, clear the selection
              if (selectedAnalysis?.id === id) {
                setSelectedAnalysis(null);
              }
              // Refresh the list
              loadAnalyses();
            } catch (error) {
              console.error('Error deleting analysis:', error);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const renderAnalysisItem = ({ item }: { item: StoredAnalysis }) => (
    <TouchableOpacity
      style={[
        styles.analysisItem,
        selectedAnalysis?.id === item.id && styles.selectedAnalysisItem,
      ]}
      onPress={() => {
        setSelectedAnalysis(item);
        setDetailsVisible(true);
      }}
    >
      <View style={styles.analysisHeader}>
        <Text style={styles.analysisName} numberOfLines={1}>{item.name}</Text>
      </View>
      <View style={styles.analysisSubheader}>
        <Text style={styles.analysisDate}>{formatDate(item.date)}</Text>
        <Text style={styles.medicationCount}>
          {item.medications.length} {item.medications.length === 1 ? t('medication') : t('medications')}
        </Text>
      </View>

      {item.imageUri && (
        <Image source={{ uri: item.imageUri }} style={styles.thumbnailImage} />
      )}

      <View style={styles.medicationPreview}>
        {item.medications.slice(0, 2).map((med, index) => (
          <Text key={index} style={styles.previewText} numberOfLines={1}>
            • {med.name} {med.dosage}
          </Text>
        ))}
        {item.medications.length > 2 && (
          <Text style={styles.moreText}>
            {`${t('andMore').replace('{count}', (item.medications.length - 2).toString())}`}
          </Text>
        )}
      </View>

      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteAnalysis(item.id)}
      >
        <Ionicons name="close" size={18} color="#ff4444" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{t('medicationSummary')}</Text>

        <View style={styles.headerButtons}>
          {analyses.length > 0 && (
            <TouchableOpacity
              style={styles.deleteAllButton}
              onPress={() => setShowDeleteAllModal(true)}
            >
              <Ionicons name="trash-outline" size={18} color="#ff4444" />
              <Text style={styles.deleteAllText}>{t('deleteAll')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Bar */}
      {analyses.length > 0 && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder={t('searchAnalyses')}
              value={searchQuery}
              onChangeText={handleSearch}
              clearButtonMode="while-editing"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
                <Ionicons name="close-circle" size={18} color="#999" />
              </TouchableOpacity>
            )}
          </View>
          {searching && <ActivityIndicator size="small" color="#2f95dc" style={styles.searchingIndicator} />}
        </View>
      )}

      {/* Delete All Confirmation Modal */}
      <Modal
        visible={showDeleteAllModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteAllModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('confirmDeleteAll')}</Text>
            <Text style={styles.modalMessage}>{t('deleteAllConfirmMessage')}</Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowDeleteAllModal(false)}
              >
                <Text style={styles.modalButtonText}>{t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.deleteButton]}
                onPress={handleDeleteAll}
              >
                <Text style={[styles.modalButtonText, styles.deleteButtonText]}>{t('deleteAll')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {loading || translating ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2f95dc" />
          <Text style={styles.loadingText}>
            {translating ? t('translating') : t('loadingData')}
          </Text>
          {translating && (
            <Text style={styles.loadingSubtext}>
              {t('translatingMedications')}
            </Text>
          )}
        </View>
      ) : analyses.length === 0 || (filteredAnalyses.length === 0 && searchQuery) ? (
        <View style={styles.emptyContainer}>
          <Ionicons name={searchQuery ? "search" : "medical-outline"} size={50} color="#ccc" />
          <Text style={styles.emptyText}>
            {searchQuery ? t('noSearchResults') : t('noSavedAnalyses')}
          </Text>
          <Text style={styles.emptySubtext}>
            {searchQuery ?
              t('tryDifferentSearch') :
              t('takePhotoToStart')}
          </Text>
          {searchQuery && (
            <TouchableOpacity
              style={styles.clearSearchButton}
              onPress={handleClearSearch}
            >
              <Text style={styles.clearSearchText}>{t('clearSearch')}</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View style={[styles.contentContainer, !isTablet && styles.mobileContentContainer]}>
          <View style={[styles.listContainer, !isTablet && styles.mobileListContainer]}>
            <FlatList
              data={filteredAnalyses}
              renderItem={renderAnalysisItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={['#2f95dc']}
                  tintColor="#2f95dc"
                />
              }
              ListEmptyComponent={searchQuery ? (
                <View style={styles.inlineEmptySearch}>
                  <Text style={styles.inlineEmptyText}>{t('noSearchResults')}</Text>
                  <TouchableOpacity onPress={handleClearSearch}>
                    <Text style={styles.inlineClearSearch}>{t('clearSearch')}</Text>
                  </TouchableOpacity>
                </View>
              ) : null}
            />
          </View>

          {selectedAnalysis && detailsVisible && (
            <View style={[styles.detailContainer, !isTablet && styles.mobileDetailContainer]}>
              <View style={styles.detailHeader}>
                <Text style={styles.detailTitle}>{t('analysisDetails')}</Text>
                <View style={styles.detailHeaderRight}>
                  <Text style={styles.detailDate}>
                    {formatDate(selectedAnalysis.date)}
                  </Text>
                  <TouchableOpacity
                    style={styles.minimizeButton}
                    onPress={() => setDetailsVisible(false)}
                  >
                    <Ionicons name="chevron-down-outline" size={24} color="#666" />
                  </TouchableOpacity>
                </View>
              </View>

              {selectedAnalysis.imageUri && (
                <Image
                  source={{ uri: selectedAnalysis.imageUri }}
                  style={styles.detailImage}
                  resizeMode="contain"
                />
              )}

              <MedicationTable medications={selectedAnalysis.medications} />
            </View>
          )}

          {selectedAnalysis && !detailsVisible && (
            <TouchableOpacity
              style={styles.showDetailsButton}
              onPress={() => setDetailsVisible(true)}
            >
              <Text style={styles.showDetailsText}>{t('showDetails')}</Text>
              <Ionicons name="chevron-up-outline" size={24} color="#2f95dc" />
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  translateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#9b87f5',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#7E69AB',
    marginRight: 8,
  },
  translateButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  deleteAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff0f0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ffcccc',
  },
  deleteAllText: {
    color: '#ff4444',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  clearButton: {
    padding: 6,
  },
  searchingIndicator: {
    marginLeft: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: '600',
    color: '#7E69AB',
  },
  loadingSubtext: {
    marginTop: 5,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    maxWidth: '80%',
  },
  translatingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginVertical: 10,
  },
  translatingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#7E69AB',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  clearSearchButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
  },
  clearSearchText: {
    color: '#7E69AB',
    fontWeight: '500',
  },
  inlineEmptySearch: {
    padding: 20,
    alignItems: 'center',
  },
  inlineEmptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  inlineClearSearch: {
    color: '#7E69AB',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  mobileContentContainer: {
    flexDirection: 'column',
  },
  listContainer: {
    flex: 1,
    marginRight: 16,
    minWidth: 250,
    maxWidth: 400,
  },
  mobileListContainer: {
    maxWidth: '100%',
    marginRight: 0,
    height: 200,
  },
  listContent: {
    paddingBottom: 16,
  },
  analysisItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedAnalysisItem: {
    borderColor: '#7E69AB',
    borderWidth: 2,
  },
  analysisHeader: {
    marginBottom: 4,
  },
  analysisName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  },
  analysisSubheader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  analysisDate: {
    fontSize: 14,
    color: '#000',
    flex: 1,
  },
  medicationCount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#7E69AB',
    marginLeft: 8,
  },
  thumbnailImage: {
    width: '100%',
    height: 80,
    borderRadius: 4,
    marginBottom: 8,
  },
  medicationPreview: {
    marginTop: 4,
  },
  previewText: {
    fontSize: 14,
    color: '#000',
  },
  moreText: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  deleteButton: {
    position: 'absolute',
    top: 6,
    right: 6,
    padding: 4,
    backgroundColor: '#ffeeee',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#ffdddd',
  },
  detailContainer: {
    flex: 2,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    marginTop: 0,
  },
  mobileDetailContainer: {
    flex: 1,
    marginTop: 16,
  },
  detailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  detailHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  minimizeButton: {
    padding: 5,
    marginLeft: 10,
  },
  showDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#9b87f5',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  showDetailsText: {
    fontSize: 16,
    color: 'white',
    marginRight: 8,
    fontWeight: '500',
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  detailDate: {
    fontSize: 14,
    color: '#000',
  },
  detailImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  modalMessage: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#7E69AB',
  },
  deleteButton: {
    backgroundColor: '#ff4444',
  },
  deleteButtonText: {
    color: 'white',
  },
});

export default MedicationSummary;

// Minimal implementation of the Node.js http module for React Native
const EventEmitter = require('./events');

class IncomingMessage extends EventEmitter {
  constructor() {
    super();
    this.headers = {};
    this.statusCode = 200;
    this.statusMessage = 'OK';
  }
}

class ServerResponse extends EventEmitter {
  constructor() {
    super();
    this.statusCode = 200;
    this.headers = {};
  }

  setHeader(name, value) {
    this.headers[name.toLowerCase()] = value;
  }

  getHeader(name) {
    return this.headers[name.toLowerCase()];
  }

  removeHeader(name) {
    delete this.headers[name.toLowerCase()];
  }

  writeHead(statusCode, headers) {
    this.statusCode = statusCode;
    if (headers) {
      Object.keys(headers).forEach(name => {
        this.setHeader(name, headers[name]);
      });
    }
    return this;
  }

  write(chunk) {
    // No-op in this shim
    return true;
  }

  end(data) {
    if (data) {
      this.write(data);
    }
    this.emit('finish');
    return this;
  }
}

class Server extends EventEmitter {
  constructor(requestListener) {
    super();
    if (requestListener) {
      this.on('request', requestListener);
    }
  }

  listen() {
    // No-op in this shim
    return this;
  }

  close(callback) {
    if (callback) {
      callback();
    }
    return this;
  }
}

function createServer(requestListener) {
  return new Server(requestListener);
}

module.exports = {
  createServer,
  Server,
  IncomingMessage,
  ServerResponse,
  // Add other methods as needed
  get: () => { throw new Error('http.get is not implemented in this environment'); },
  request: () => { throw new Error('http.request is not implemented in this environment'); }
};

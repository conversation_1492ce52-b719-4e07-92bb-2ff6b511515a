// Test script for Resend API
const { Resend } = require('resend');

// Use the new API key
const resend = new Resend('re_hUY21vWT_C6AKxToaSavFfvave8uYHt55');

async function testResendAPI() {
  try {
    console.log('Testing Resend API...');
    
    const { data, error } = await resend.emails.send({
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Test Email from PillLogic',
      html: '<p>This is a test email to verify that Resend API is working correctly.</p>'
    });
    
    if (error) {
      console.error('Error sending email:', error);
      return;
    }
    
    console.log('Email sent successfully!');
    console.log('Email ID:', data.id);
  } catch (error) {
    console.error('Exception sending email:', error);
  }
}

// Run the test
testResendAPI();

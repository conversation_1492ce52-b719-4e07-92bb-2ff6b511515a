// Design System Constants

export const Colors = {
  // Primary colors
  primary: '#7E69AB',
  primaryLight: '#9b87f5',
  primaryDark: '#5D4A88',

  // Secondary colors
  secondary: '#6c757d',
  secondaryLight: '#e9ecef',
  secondaryDark: '#495057',

  // Accent colors
  accent: '#ff9800',
  accentLight: '#fff3e0',
  accentDark: '#f57c00',

  // Semantic colors
  success: '#4caf50',
  successLight: '#e8f5e9',
  successDark: '#388e3c',

  warning: '#ff9800',
  warningLight: '#fff3e0',
  warningDark: '#f57c00',

  error: '#f44336',
  errorLight: '#ffebee',
  errorDark: '#d32f2f',

  info: '#7E69AB',
  infoLight: '#9b87f5',
  infoDark: '#5D4A88',

  // Neutral colors
  white: '#ffffff',
  background: '#f8f9fa',
  border: '#e0e0e0',
  shadow: '#000000',

  // Text colors
  textPrimary: '#000000',
  textSecondary: '#6c757d',
  textTertiary: '#adb5bd',
  textLight: '#ffffff',
};

export const Typography = {
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
  },

  fontWeight: {
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },

  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 42,
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 9999,
};

export const Elevation = {
  none: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 8,
  },
  xl: {
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
  },
};

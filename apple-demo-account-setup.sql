-- Apple Review Demo Account Update <PERSON>ript
-- Run this in your Supabase SQL Editor
-- This updates <NAME_EMAIL> account to meet Apple Review requirements

-- STEP 1: First, let's check if the account exists and get its details
-- Run this to verify the account exists:
-- SELECT id, email, created_at FROM auth.users WHERE email = '<EMAIL>';

-- STEP 2: Create/Update subscription to be EXPIRED
-- This is what Apple specifically requested - an account with expired subscription

-- First, delete any existing subscription for this user
DELETE FROM public.subscriptions
WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- Create a new expired subscription
INSERT INTO public.subscriptions (
  user_id,
  status,
  plan_type,
  current_period_start,
  current_period_end,
  created_at,
  updated_at,
  stripe_subscription_id,
  stripe_customer_id
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'expired', -- This is key - Apple wants to test expired subscription flow
  'premium', -- or 'pro' - whatever your premium plan is called
  NOW() - INTERVAL '2 months', -- Started 2 months ago
  NOW() - INTERVAL '1 month',  -- Expired 1 month ago (clearly expired)
  NOW() - INTERVAL '2 months',
  NOW(),
  'sub_demo_apple_4269', -- Fake Stripe subscription ID
  'cus_demo_apple_4269'  -- Fake Stripe customer ID
);

-- STEP 3: Update user metadata to mark as demo account
UPDATE auth.users
SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) || '{"demo_account": true, "name": "Apple Review Demo"}'::jsonb
WHERE email = '<EMAIL>';

-- STEP 4: Add some realistic usage history (makes the account look authentic)
-- Delete existing usage logs first
DELETE FROM public.usage_logs
WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- Add realistic usage data from when subscription was active
INSERT INTO public.usage_logs (
  user_id,
  feature_type,
  usage_count,
  date,
  created_at
) VALUES
  -- Usage from 2 months ago (when subscription was active)
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'medication_scan', 8, CURRENT_DATE - 60, NOW() - INTERVAL '60 days'),
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'pill_count', 5, CURRENT_DATE - 55, NOW() - INTERVAL '55 days'),
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'live_pill_count', 3, CURRENT_DATE - 50, NOW() - INTERVAL '50 days'),
  -- Usage from 1.5 months ago (still active)
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'medication_scan', 6, CURRENT_DATE - 45, NOW() - INTERVAL '45 days'),
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'pill_count', 4, CURRENT_DATE - 40, NOW() - INTERVAL '40 days'),
  -- Limited usage after expiration (showing free tier limits)
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'medication_scan', 2, CURRENT_DATE - 15, NOW() - INTERVAL '15 days'),
  ((SELECT id FROM auth.users WHERE email = '<EMAIL>'), 'pill_count', 1, CURRENT_DATE - 10, NOW() - INTERVAL '10 days');

-- STEP 5: Verification queries - run these to confirm everything is set up correctly
-- Uncomment and run these one by one to verify:

-- Check user exists:
-- SELECT id, email, created_at, raw_user_meta_data FROM auth.users WHERE email = '<EMAIL>';

-- Check subscription is expired:
-- SELECT * FROM public.subscriptions WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- Check usage history:
-- SELECT * FROM public.usage_logs WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>') ORDER BY date DESC;

-- STEP 6: Test the account
-- After running this script, test:
-- 1. <NAME_EMAIL> / AppleReview2025!
-- 2. Verify subscription shows as expired
-- 3. Verify user can access subscription purchase flow
-- 4. Verify app properly handles expired subscription state

import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import HC<PERSON>tchaReact from '@hcaptcha/react-hcaptcha';
import Constants from 'expo-constants';
import HCaptchaMobile from './HCaptchaMobile';

// Define the props interface
interface HCaptchaProps {
  siteKey?: string;
  onVerify: (token: string) => void;
  onExpire?: () => void;
  onError?: (error: Error) => void;
  size?: 'normal' | 'compact' | 'invisible';
  theme?: 'light' | 'dark';
}

// Define the ref interface
export interface HCaptchaRef {
  resetCaptcha: () => void;
  execute: () => void;
}

// Get the hCaptcha site key from app config or use the hardcoded value
// The site key f3a0d298-4616-4274-a5fc-45ae6796f7da is the official test key from hCaptcha
const HCAPTCHA_SITE_KEY = Constants.expoConfig?.extra?.hcaptchaSiteKey || 'f3a0d298-4616-4274-a5fc-45ae6796f7da';

// Log the site key for debugging
console.log('Using hCAPTCHA site key:', HCAPTCHA_SITE_KEY);

// Verify that we have a valid site key
if (!HCAPTCHA_SITE_KEY || HCAPTCHA_SITE_KEY.length < 10) {
  console.error('Invalid hCAPTCHA site key:', HCAPTCHA_SITE_KEY);
}

/**
 * A wrapper component for hCaptcha
 */
const HCaptcha = forwardRef<HCaptchaRef, HCaptchaProps>((props, ref) => {
  const {
    siteKey = HCAPTCHA_SITE_KEY,
    onVerify,
    onExpire,
    onError,
    size = 'normal',
    theme = 'light'
  } = props;

  // Create a ref to the hCaptcha component
  const hCaptchaRef = useRef<any>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    resetCaptcha: () => {
      if (hCaptchaRef.current) {
        hCaptchaRef.current.resetCaptcha();
      }
    },
    execute: () => {
      if (hCaptchaRef.current) {
        hCaptchaRef.current.execute();
      }
    }
  }));

  // Handle verification
  const handleVerify = (token: string) => {
    console.log('hCaptcha verified with token:', token.substring(0, 10) + '...');
    onVerify(token);
  };

  // Handle expiration
  const handleExpire = () => {
    console.log('hCaptcha token expired');
    if (onExpire) {
      onExpire();
    }
  };

  // Handle error
  const handleError = (err: Error) => {
    console.error('hCaptcha error:', err);
    if (onError) {
      onError(err);
    }
  };

  // Render the appropriate hCaptcha component based on platform
  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <HCaptchaReact
          ref={hCaptchaRef}
          sitekey={siteKey}
          onVerify={handleVerify}
          onExpire={handleExpire}
          onError={handleError}
          size={size}
          theme={theme}
        />
      </View>
    );
  } else {
    // For mobile platforms, use our WebView-based implementation
    return (
      <View style={styles.container}>
        <HCaptchaMobile
          ref={hCaptchaRef}
          siteKey={siteKey}
          onVerify={handleVerify}
          onExpire={handleExpire}
          onError={handleError}
          theme={theme}
        />
      </View>
    );
  }
});

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    minHeight: 300, // Ensure enough height for the widget
  },
});

export default HCaptcha;

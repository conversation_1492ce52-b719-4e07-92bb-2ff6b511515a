/**
 * Simple script to test the usage limits functionality
 * 
 * Usage:
 * 1. Make sure you're logged in to your app
 * 2. Run: node scripts/test-usage-limits-simple.js
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// Try to load environment variables from .env file
let supabaseUrl, supabaseAnonKey;
try {
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envLines = envContent.split('\n');
    
    for (const line of envLines) {
      const [key, value] = line.split('=');
      if (key === 'EXPO_PUBLIC_SUPABASE_URL') {
        supabaseUrl = value;
      } else if (key === 'EXPO_PUBLIC_SUPABASE_ANON_KEY') {
        supabaseAnonKey = value;
      }
    }
  }
} catch (error) {
  console.error('Error loading .env file:', error);
}

// Fallback to environment variables if not found in .env
supabaseUrl = supabaseUrl || process.env.EXPO_PUBLIC_SUPABASE_URL;
supabaseAnonKey = supabaseAnonKey || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please enter your Supabase URL and anon key manually:');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('Supabase URL: ', (url) => {
    supabaseUrl = url;
    rl.question('Supabase Anon Key: ', (key) => {
      supabaseAnonKey = key;
      rl.close();
      runTest();
    });
  });
} else {
  runTest();
}

function runTest() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  // Create readline interface for user input
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  // Function to prompt for user input
  const prompt = (question) => new Promise((resolve) => rl.question(question, resolve));
  
  // Main test function
  async function testUsageLimits() {
    try {
      console.log('🔍 Testing usage limits functionality...');
      
      // Check if user is logged in
      const { data, error: sessionError } = await supabase.auth.getSession();
      const session = data?.session;
      
      if (sessionError || !session) {
        console.error('❌ You need to be logged in to test usage limits');
        console.log('Please log in to the app first and then run this script again');
        return;
      }
      
      const userId = session.user.id;
      console.log(`✅ Logged in as user: ${userId}`);
      
      // Get user profile to check subscription tier
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', userId)
        .single();
        
      if (profileError) {
        console.error('❌ Error fetching user profile:', profileError.message);
        return;
      }
      
      console.log(`📊 Subscription tier: ${profile.subscription_tier || 'free'}`);
      
      // Test can_use_feature function
      const featureType = await prompt('Enter feature type to test (pill_scan or live_pill_scan): ');
      
      const { data: canUse, error: canUseError } = await supabase.rpc('can_use_feature', {
        user_uuid: userId,
        feature: featureType
      });
      
      if (canUseError) {
        console.error(`❌ Error checking if user can use feature ${featureType}:`, canUseError.message);
        return;
      }
      
      console.log(`✅ Can use ${featureType}: ${canUse}`);
      
      // Get current usage
      const { data: usageData, error: usageError } = await supabase
        .from('daily_feature_usage')
        .select('*')
        .eq('user_id', userId)
        .eq('feature_type', featureType)
        .eq('usage_date', new Date().toISOString().split('T')[0]);
        
      if (usageError) {
        console.error('❌ Error fetching usage data:', usageError.message);
      } else {
        console.log(`📊 Current usage for ${featureType}:`, usageData.length > 0 ? usageData[0].usage_count : 0);
      }
      
      // Ask if user wants to track usage
      const trackUsage = await prompt('Do you want to track usage for this feature? (yes/no): ');
      
      if (trackUsage.toLowerCase() === 'yes') {
        const { data: tracked, error: trackError } = await supabase.rpc('track_daily_feature_usage', {
          user_uuid: userId,
          feature: featureType
        });
        
        if (trackError) {
          console.error(`❌ Error tracking feature usage for ${featureType}:`, trackError.message);
          return;
        }
        
        console.log(`✅ Usage tracked successfully. Result: ${tracked}`);
        
        // Get updated usage
        const { data: updatedUsage, error: updatedUsageError } = await supabase
          .from('daily_feature_usage')
          .select('*')
          .eq('user_id', userId)
          .eq('feature_type', featureType)
          .eq('usage_date', new Date().toISOString().split('T')[0]);
          
        if (updatedUsageError) {
          console.error('❌ Error fetching updated usage data:', updatedUsageError.message);
        } else {
          console.log(`📊 Updated usage for ${featureType}:`, updatedUsage.length > 0 ? updatedUsage[0].usage_count : 0);
        }
      }
      
      console.log('✅ Test completed successfully!');
    } catch (error) {
      console.error('❌ An error occurred during testing:', error);
    } finally {
      rl.close();
    }
  }
  
  // Run the test
  testUsageLimits();
}

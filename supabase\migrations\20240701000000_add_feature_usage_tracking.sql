-- Create daily_feature_usage table to track daily usage limits
CREATE TABLE IF NOT EXISTS daily_feature_usage (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  feature_type TEXT NOT NULL,
  usage_count INTEGER DEFAULT 0,
  usage_date DATE DEFAULT CURRENT_DATE,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, feature_type, usage_date)
);

-- Create live_session_tracking table to track live pill scan sessions
CREATE TABLE IF NOT EXISTS live_session_tracking (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  session_end TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  duration_seconds INTEGER
);

-- Create RLS policies for daily_feature_usage
ALTER TABLE daily_feature_usage ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own usage
CREATE POLICY "Users can view their own daily feature usage"
  ON daily_feature_usage
  FOR SELECT
  USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own usage
CREATE POLICY "Users can insert their own daily feature usage"
  ON daily_feature_usage
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own usage
CREATE POLICY "Users can update their own daily feature usage"
  ON daily_feature_usage
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Create RLS policies for live_session_tracking
ALTER TABLE live_session_tracking ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own sessions
CREATE POLICY "Users can view their own live sessions"
  ON live_session_tracking
  FOR SELECT
  USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own sessions
CREATE POLICY "Users can insert their own live sessions"
  ON live_session_tracking
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own sessions
CREATE POLICY "Users can update their own live sessions"
  ON live_session_tracking
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Create function to track feature usage
CREATE OR REPLACE FUNCTION public.track_daily_feature_usage(
  user_uuid UUID,
  feature TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  is_paid BOOLEAN;
  current_count INTEGER;
  max_limit INTEGER;
  can_use BOOLEAN;
BEGIN
  -- Check if user is on a paid tier
  SELECT (subscription_tier != 'free' AND subscription_tier IS NOT NULL)
  INTO is_paid
  FROM public.profiles
  WHERE id = user_uuid;

  -- Paid users have no limits
  IF is_paid THEN
    -- Insert or update usage record (for tracking purposes)
    INSERT INTO public.daily_feature_usage (user_id, feature_type, usage_count, usage_date, last_used_at)
    VALUES (user_uuid, feature, 1, CURRENT_DATE, NOW())
    ON CONFLICT (user_id, feature_type, usage_date)
    DO UPDATE SET
      usage_count = daily_feature_usage.usage_count + 1,
      last_used_at = NOW();

    RETURN TRUE;
  END IF;

  -- Get current usage count
  SELECT usage_count INTO current_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid
    AND feature_type = feature
    AND usage_date = CURRENT_DATE;

  -- If no record exists, count is 0
  IF current_count IS NULL THEN
    current_count := 0;
  END IF;

  -- Set max limit based on feature type
  IF feature = 'pill_scan' THEN
    max_limit := 5;
  ELSIF feature = 'live_pill_scan' THEN
    max_limit := 2;
  ELSIF feature = 'note_analysis' THEN
    max_limit := 5;
  ELSE
    max_limit := 10; -- Default for other features
  END IF;

  -- Check if user can use the feature
  can_use := current_count < max_limit;

  -- If user can use the feature, increment the count
  IF can_use THEN
    INSERT INTO public.daily_feature_usage (user_id, feature_type, usage_count, usage_date, last_used_at)
    VALUES (user_uuid, feature, 1, CURRENT_DATE, NOW())
    ON CONFLICT (user_id, feature_type, usage_date)
    DO UPDATE SET
      usage_count = daily_feature_usage.usage_count + 1,
      last_used_at = NOW();
  END IF;

  RETURN can_use;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to start a live session
CREATE OR REPLACE FUNCTION public.start_live_session(
  user_uuid UUID
)
RETURNS UUID AS $$
DECLARE
  is_paid BOOLEAN;
  session_count INTEGER;
  new_session_id UUID;
BEGIN
  -- Check if user is on a paid tier
  SELECT (subscription_tier != 'free' AND subscription_tier IS NOT NULL)
  INTO is_paid
  FROM public.profiles
  WHERE id = user_uuid;

  -- If not paid, check daily session count
  IF NOT is_paid THEN
    SELECT COUNT(*) INTO session_count
    FROM public.daily_feature_usage
    WHERE user_id = user_uuid
      AND feature_type = 'live_pill_scan'
      AND usage_date = CURRENT_DATE;

    -- If limit reached, return NULL
    IF session_count >= 2 THEN
      RETURN NULL;
    END IF;
  END IF;

  -- Create new session
  INSERT INTO public.live_session_tracking (user_id, session_start, is_active)
  VALUES (user_uuid, NOW(), TRUE)
  RETURNING id INTO new_session_id;

  -- Track usage for non-paid users
  IF NOT is_paid THEN
    PERFORM public.track_daily_feature_usage(user_uuid, 'live_pill_scan');
  END IF;

  RETURN new_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to end a live session
CREATE OR REPLACE FUNCTION public.end_live_session(
  session_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  session_start TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get session start time
  SELECT session_start INTO session_start
  FROM public.live_session_tracking
  WHERE id = session_uuid;

  -- Update session
  UPDATE public.live_session_tracking
  SET
    session_end = NOW(),
    is_active = FALSE,
    duration_seconds = EXTRACT(EPOCH FROM (NOW() - session_start))
  WHERE id = session_uuid;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user can use a feature
CREATE OR REPLACE FUNCTION public.can_use_feature(
  user_uuid UUID,
  feature TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  is_paid BOOLEAN;
  current_count INTEGER;
  max_limit INTEGER;
BEGIN
  -- Check if user is on a paid tier
  SELECT (subscription_tier != 'free' AND subscription_tier IS NOT NULL)
  INTO is_paid
  FROM public.profiles
  WHERE id = user_uuid;

  -- Paid users have no limits
  IF is_paid THEN
    RETURN TRUE;
  END IF;

  -- Get current usage count
  SELECT usage_count INTO current_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid
    AND feature_type = feature
    AND usage_date = CURRENT_DATE;

  -- If no record exists, count is 0
  IF current_count IS NULL THEN
    current_count := 0;
  END IF;

  -- Set max limit based on feature type
  IF feature = 'pill_scan' THEN
    max_limit := 5;
  ELSIF feature = 'live_pill_scan' THEN
    max_limit := 2;
  ELSIF feature = 'note_analysis' THEN
    max_limit := 5;
  ELSE
    max_limit := 10; -- Default for other features
  END IF;

  -- Return whether user can use the feature
  RETURN current_count < max_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

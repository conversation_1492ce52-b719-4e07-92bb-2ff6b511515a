import { supabase } from '../lib/supabase';
import { Al<PERSON>, Platform } from 'react-native';
import Constants from 'expo-constants';
import Purchases, {
  PurchasesOffering,
  PurchasesPackage,
  CustomerInfo,
  PurchasesError,
  PURCHASES_ERROR_CODE
} from 'react-native-purchases';

// RevenueCat API Keys - CONFIGURED ✅
const REVENUECAT_API_KEY = {
  ios: 'appl_rAKgiuovQFzkShxoTjRANDozvyQ', // Your actual iOS API key from RevenueCat
  android: 'goog_YOUR_ANDROID_API_KEY_HERE' // Replace with your actual Android API key from RevenueCat (if needed)
};

// Apple In-App Purchase Product IDs
// These need to match the product IDs you create in App Store Connect
export const IAP_PRODUCT_IDS = {
  PRO_MONTHLY: 'com.pilllogic.app.pro.monthly.v2', // Match App Store Connect
  PREMIUM_MONTHLY: 'com.pilllogic.app.premium.monthly', // Match App Store Connect
};

// Product information for display
export const IAP_PRODUCTS = {
  [IAP_PRODUCT_IDS.PRO_MONTHLY]: {
    tier: 'pro',
    title: 'Pro Plan',
    description: 'Unlimited pill scans + 5 live scans per day',
    price: '$4.99/month',
  },
  [IAP_PRODUCT_IDS.PREMIUM_MONTHLY]: {
    tier: 'premium',
    title: 'Premium Plan',
    description: 'Everything unlocked. Unlimited counts. Doctor note analysis.',
    price: '$5.99/month',
  },
};



// Track if RevenueCat is already initialized
let isRevenueCatInitialized = false;

// Track current authenticated user to prevent race conditions
let currentAuthenticatedUser: string | null = null;

// Authentication retry configuration
const AUTH_RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  backoffMultiplier: 2
};

// Robust user authentication with RevenueCat
const authenticateUserWithRevenueCat = async (userId: string, retryCount = 0): Promise<{ success: boolean; isAnonymous: boolean; error?: string }> => {
  try {
    console.log(`IAP: Attempting to authenticate user ${userId} (attempt ${retryCount + 1})`);

    // Check if user is already authenticated
    if (currentAuthenticatedUser === userId) {
      console.log('IAP: User already authenticated');
      return { success: true, isAnonymous: false };
    }

    // Attempt to log in the user
    const customerInfo = await Purchases.logIn(userId);

    // Verify the login was successful
    if (customerInfo && customerInfo.customerInfo) {
      console.log('IAP: User authentication successful');
      currentAuthenticatedUser = userId;
      return { success: true, isAnonymous: false };
    } else {
      throw new Error('Login returned invalid customer info');
    }

  } catch (loginError) {
    console.warn(`IAP: User authentication failed (attempt ${retryCount + 1}):`, loginError);
    console.warn('IAP: Login error details:', {
      message: loginError.message,
      code: loginError.code,
      userInfo: loginError.userInfo,
      stack: loginError.stack
    });

    // If we haven't exceeded retry limit, try again
    if (retryCount < AUTH_RETRY_CONFIG.maxRetries) {
      const delay = AUTH_RETRY_CONFIG.retryDelay * Math.pow(AUTH_RETRY_CONFIG.backoffMultiplier, retryCount);
      console.log(`IAP: Retrying authentication in ${delay}ms...`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return authenticateUserWithRevenueCat(userId, retryCount + 1);
    }

    // All retries failed - check if we can continue with anonymous user
    try {
      console.log('IAP: Attempting to get customer info for anonymous user...');
      const anonymousCustomerInfo = await Purchases.getCustomerInfo();

      if (anonymousCustomerInfo) {
        console.log('IAP: Continuing with anonymous user');
        currentAuthenticatedUser = null; // Mark as anonymous
        return { success: true, isAnonymous: true };
      }
    } catch (anonymousError) {
      console.error('IAP: Failed to get anonymous customer info:', anonymousError);
    }

    return {
      success: false,
      isAnonymous: false,
      error: `Authentication failed after ${AUTH_RETRY_CONFIG.maxRetries + 1} attempts: ${loginError.message}`
    };
  }
};

// Initialize RevenueCat
export const initializeIAP = async (): Promise<boolean> => {
  try {
    // Return early if already initialized
    if (isRevenueCatInitialized) {
      console.log('IAP: RevenueCat already initialized');
      return true;
    }

    console.log('IAP: Attempting to initialize RevenueCat...');
    console.log('IAP: Platform:', Platform.OS);
    console.log('IAP: App ownership:', Constants.appOwnership);

    // Only skip if explicitly running in Expo Go
    const isExpoGo = Constants.appOwnership === 'expo';
    if (isExpoGo) {
      console.log('IAP: Running in Expo Go - RevenueCat not available');
      return false;
    }

    // Configure RevenueCat
    const apiKey = Platform.OS === 'ios' ? REVENUECAT_API_KEY.ios : REVENUECAT_API_KEY.android;

    if (!apiKey || apiKey.includes('YOUR_') || apiKey.length < 10) {
      console.error('IAP: Invalid RevenueCat API key:', apiKey);
      return false;
    }

    console.log('IAP: Configuring RevenueCat with API key...');
    await Purchases.configure({ apiKey });

    // Mark as initialized
    isRevenueCatInitialized = true;

    console.log('IAP: Successfully initialized RevenueCat');
    return true;
  } catch (error) {
    console.error('IAP: Failed to initialize RevenueCat:', error);
    return false;
  }
};

// Reset authentication state (useful for troubleshooting)
export const resetAuthenticationState = (): void => {
  console.log('IAP: Resetting authentication state');
  currentAuthenticatedUser = null;
  isRevenueCatInitialized = false;
};

// Get current authentication status
export const getAuthenticationStatus = (): { isInitialized: boolean; authenticatedUser: string | null } => {
  return {
    isInitialized: isRevenueCatInitialized,
    authenticatedUser: currentAuthenticatedUser
  };
};

// Get available subscription offerings from RevenueCat
export const getProducts = async (): Promise<PurchasesPackage[]> => {
  try {
    const isExpoGo = Constants.appOwnership === 'expo';
    if (isExpoGo) {
      console.log('IAP: Not available in Expo Go');
      return [];
    }

    console.log('IAP: Fetching subscription offerings from RevenueCat...');
    console.log('IAP: RevenueCat initialized status:', isRevenueCatInitialized);

    // Ensure RevenueCat is initialized
    if (!isRevenueCatInitialized) {
      console.log('IAP: RevenueCat not initialized, initializing now...');
      const initSuccess = await initializeIAP();
      if (!initSuccess) {
        console.error('IAP: Failed to initialize RevenueCat');
        return [];
      }
    }

    const offerings = await Purchases.getOfferings();
    console.log('IAP: Retrieved offerings:', {
      current: offerings.current?.identifier,
      all: Object.keys(offerings.all),
      totalOfferings: Object.keys(offerings.all).length
    });

    // Check if we have any offerings at all
    if (Object.keys(offerings.all).length === 0) {
      console.error('IAP: No offerings available from RevenueCat');
      console.error('IAP: This suggests a configuration issue with RevenueCat or App Store Connect');
      return [];
    }

    // Look for the "default" offering specifically (since that's where your products are)
    const defaultOffering = offerings.all['default'];
    if (defaultOffering) {
      console.log('IAP: Found "default" offering with', defaultOffering.availablePackages.length, 'packages');

      const packages = defaultOffering.availablePackages;
      console.log('IAP: Package details:', packages.map(p => ({
        identifier: p.identifier,
        productId: p.product.identifier,
        price: p.product.priceString
      })));

      // Filter packages to match our product IDs
      const filteredPackages = packages.filter(pkg =>
        Object.values(IAP_PRODUCT_IDS).includes(pkg.product.identifier)
      );

      console.log('IAP: Expected product IDs:', Object.values(IAP_PRODUCT_IDS));
      console.log('IAP: Filtered packages matching our products:', filteredPackages.length);

      if (filteredPackages.length > 0) {
        return filteredPackages;
      }
    }

    // Fallback: Check current offering if it exists
    if (offerings.current) {
      const packages = offerings.current.availablePackages;
      console.log('IAP: Current offering packages:', packages.length);

      const filteredPackages = packages.filter(pkg =>
        Object.values(IAP_PRODUCT_IDS).includes(pkg.product.identifier)
      );

      if (filteredPackages.length > 0) {
        return filteredPackages;
      }
    }

    // Final fallback: Check all offerings
    console.log('IAP: Checking all offerings for our products...');
    const allOfferings = Object.values(offerings.all);
    console.log('IAP: Total offerings available:', allOfferings.length);

    for (const offering of allOfferings) {
      console.log('IAP: Checking offering:', offering.identifier);
      const packages = offering.availablePackages;
      const matchingPackages = packages.filter(pkg =>
        Object.values(IAP_PRODUCT_IDS).includes(pkg.product.identifier)
      );

      if (matchingPackages.length > 0) {
        console.log('IAP: Found matching packages in offering:', offering.identifier);
        return matchingPackages;
      }
    }

    console.warn('IAP: No packages found in any offering matching our product IDs');
    return [];
  } catch (error) {
    console.error('IAP: Error fetching offerings:', error);
    return [];
  }
};

// Purchase a subscription using RevenueCat
export const purchaseSubscription = async (
  productId: string,
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Starting subscription purchase for product:', productId);
    console.log('IAP: User ID:', userId);

    // Check if running in Expo Go
    const isExpoGo = Constants.appOwnership === 'expo';
    if (isExpoGo) {
      console.log('IAP: Running in Expo Go');
      return {
        success: false,
        message: 'In-App Purchases are not available in Expo Go. Please use a production build or TestFlight.'
      };
    }

    // Ensure RevenueCat is initialized
    console.log('IAP: Ensuring RevenueCat is initialized...');
    const isInitialized = await initializeIAP();
    if (!isInitialized) {
      console.error('IAP: Failed to initialize RevenueCat');
      return {
        success: false,
        message: 'Failed to connect to App Store. Please check your internet connection and try again.'
      };
    }

    // Authenticate user with RevenueCat using robust authentication
    console.log('IAP: Authenticating user with RevenueCat...');
    console.log('IAP: Environment check:', {
      isExpoGo: Constants.appOwnership === 'expo',
      platform: Platform.OS,
      isTestFlight: Constants.appOwnership === 'standalone'
    });

    const authResult = await authenticateUserWithRevenueCat(userId);

    if (!authResult.success) {
      console.error('IAP: User authentication failed completely:', authResult.error);
      console.error('IAP: RevenueCat initialization status:', isRevenueCatInitialized);
      console.error('IAP: Current authenticated user:', currentAuthenticatedUser);

      return {
        success: false,
        message: `Failed to authenticate with purchase system. Error: ${authResult.error || 'Unknown authentication error'}`
      };
    }

    if (authResult.isAnonymous) {
      console.warn('IAP: Proceeding with anonymous user - purchase will be device-bound');
    } else {
      console.log('IAP: User successfully authenticated with RevenueCat');
    }

    // Get available packages
    console.log('IAP: Getting available packages...');
    const packages = await getProducts();
    console.log('IAP: Retrieved packages count:', packages.length);

    if (packages.length === 0) {
      console.error('IAP: No packages available from RevenueCat');
      console.error('IAP: This could be due to:');
      console.error('IAP: 1. RevenueCat configuration issues');
      console.error('IAP: 2. App Store Connect product configuration');
      console.error('IAP: 3. Network connectivity issues');
      console.error('IAP: 4. Sandbox vs Production environment mismatch');

      // Try to reinitialize RevenueCat and fetch again
      console.log('IAP: Attempting to reinitialize RevenueCat...');
      const reinitSuccess = await initializeRevenueCat();

      if (reinitSuccess) {
        console.log('IAP: RevenueCat reinitialized, trying to fetch packages again...');
        const retryPackages = await getProducts();

        if (retryPackages.length > 0) {
          console.log('IAP: Successfully retrieved packages after reinitializing');
          const retryTargetPackage = retryPackages.find(pkg => pkg.product.identifier === productId);

          if (retryTargetPackage) {
            console.log('IAP: Found target package after retry, proceeding with purchase...');
            // Continue with the purchase using retryTargetPackage
            const { customerInfo, productIdentifier } = await Purchases.purchasePackage(retryTargetPackage);

            console.log('IAP: Purchase completed for product:', productIdentifier);
            console.log('IAP: Customer info after purchase:', {
              originalAppUserId: customerInfo.originalAppUserId,
              activeSubscriptions: customerInfo.activeSubscriptions,
              entitlements: Object.keys(customerInfo.entitlements.active),
              latestExpirationDate: customerInfo.latestExpirationDate,
            });

            // Validate the purchase with our backend
            const validationResult = await validatePurchaseWithBackend(customerInfo, productId, userId);

            if (validationResult.success) {
              return {
                success: true,
                message: 'Subscription activated successfully!'
              };
            } else {
              return {
                success: false,
                message: validationResult.message || 'Purchase completed but validation failed. Please contact support.'
              };
            }
          }
        }
      }

      return {
        success: false,
        message: 'Subscription packages are temporarily unavailable. Please check your internet connection and try again. If the problem persists, please contact support.'
      };
    }

    const targetPackage = packages.find(pkg => pkg.product.identifier === productId);

    if (!targetPackage) {
      console.error('IAP: Subscription package not found:', productId);
      console.error('IAP: Available packages:', packages.map(p => p.product.identifier));
      return {
        success: false,
        message: `The requested subscription (${productId}) is not available. Available options: ${packages.map(p => p.product.identifier).join(', ')}. Please contact support.`
      };
    }

    console.log('IAP: Found subscription package:', targetPackage.product.identifier);

    // Make the purchase
    console.log('IAP: Attempting to purchase package...');
    console.log('IAP: Package details:', {
      identifier: targetPackage.identifier,
      productId: targetPackage.product.identifier,
      price: targetPackage.product.priceString,
      title: targetPackage.product.title
    });

    const { customerInfo, productIdentifier } = await Purchases.purchasePackage(targetPackage);

    console.log('IAP: Purchase completed for product:', productIdentifier);
    console.log('IAP: Customer info after purchase:', {
      originalAppUserId: customerInfo.originalAppUserId,
      activeSubscriptions: customerInfo.activeSubscriptions,
      entitlements: Object.keys(customerInfo.entitlements.active),
      latestExpirationDate: customerInfo.latestExpirationDate,
      allPurchaseDates: customerInfo.allPurchaseDates,
      allExpirationDates: customerInfo.allExpirationDates
    });

    // Check if the subscription is active
    const isSubscriptionActive = Object.keys(customerInfo.activeSubscriptions).length > 0;

    if (isSubscriptionActive) {
      console.log('IAP: Subscription is active');

      // Update subscription in our database
      const updateResult = await updateSubscriptionInDatabase(customerInfo, userId, productId);

      if (updateResult.success) {
        return { success: true, message: 'Subscription activated successfully!' };
      } else {
        return { success: false, message: updateResult.message };
      }
    } else {
      console.warn('IAP: Purchase completed but subscription not active');
      return { success: false, message: 'Purchase completed but subscription could not be activated. Please contact support.' };
    }

  } catch (error) {
    console.error('IAP: Error during subscription purchase:', error);
    console.error('IAP: Error details:', {
      code: error.code,
      message: error.message,
      userInfo: error.userInfo,
      underlyingError: error.underlyingError
    });

    // Handle specific RevenueCat errors with enhanced messaging
    if (error.code === PURCHASES_ERROR_CODE.PURCHASE_CANCELLED) {
      return { success: false, message: 'Purchase was cancelled.' };
    } else if (error.code === PURCHASES_ERROR_CODE.STORE_PROBLEM) {
      return { success: false, message: 'App Store connection problem. Please check your internet connection and try again.' };
    } else if (error.code === PURCHASES_ERROR_CODE.PURCHASE_NOT_ALLOWED) {
      return { success: false, message: 'Purchases are not allowed on this device. Please check your device settings and ensure you\'re signed in to the App Store.' };
    } else if (error.code === PURCHASES_ERROR_CODE.PAYMENT_PENDING) {
      return { success: false, message: 'Payment is pending approval. Please check back later or contact Apple Support if this persists.' };
    } else if (error.code === PURCHASES_ERROR_CODE.PRODUCT_NOT_AVAILABLE_FOR_PURCHASE) {
      return { success: false, message: 'This subscription is not available for purchase. Please ensure you\'re using the latest app version and try again.' };
    } else if (error.code === PURCHASES_ERROR_CODE.PURCHASE_INVALID_ON_THIS_DEVICE) {
      return { success: false, message: 'This purchase is not valid on this device. Please ensure you\'re signed in with the correct Apple ID.' };
    } else if (error.code === PURCHASES_ERROR_CODE.NETWORK_ERROR) {
      return { success: false, message: 'Network connection error. Please check your internet connection and try again.' };
    } else if (error.code === PURCHASES_ERROR_CODE.CONFIGURATION_ERROR) {
      return { success: false, message: 'App configuration error. Please update to the latest app version or contact support.' };
    } else if (error.code === PURCHASES_ERROR_CODE.INVALID_CREDENTIALS) {
      return { success: false, message: 'Authentication failed with purchase system. Please sign out and sign back in to the app, then try again.' };
    } else if (error.code === PURCHASES_ERROR_CODE.UNEXPECTED_BACKEND_RESPONSE_ERROR) {
      return { success: false, message: 'Unexpected response from purchase system. Please try again in a few moments.' };
    } else if (error.code === PURCHASES_ERROR_CODE.INVALID_RECEIPT) {
      return { success: false, message: 'Invalid purchase receipt. Please try the purchase again.' };
    }

    // Provide more specific error message based on the actual error
    console.error('IAP: Purchase error:', error);
    const errorMessage = error.message || 'Unknown error occurred';

    // Check if this might be an authentication-related error
    if (errorMessage.toLowerCase().includes('authentication') ||
        errorMessage.toLowerCase().includes('login') ||
        errorMessage.toLowerCase().includes('credentials') ||
        errorMessage.toLowerCase().includes('invalid user')) {
      return {
        success: false,
        message: 'Authentication failed with purchase system. Please sign out and sign back in to the app, then try again.'
      };
    }

    return {
      success: false,
      message: `Purchase failed: ${errorMessage}. Please ensure you're connected to the internet and signed in to the App Store, then try again.`
    };
  }
};

// Update subscription in database using RevenueCat customer info
const updateSubscriptionInDatabase = async (
  customerInfo: CustomerInfo,
  userId: string,
  productId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Updating subscription in database...');

    // Get the active subscription info
    const activeSubscriptions = customerInfo.activeSubscriptions;
    const entitlements = customerInfo.entitlements.active;

    if (Object.keys(activeSubscriptions).length === 0) {
      return { success: false, message: 'No active subscription found' };
    }

    // Get subscription details - use the actual product ID we purchased
    const subscriptionKey = productId; // Use the product ID we just purchased
    const subscription = customerInfo.allPurchaseDates[subscriptionKey];
    const expirationDate = customerInfo.allExpirationDates[subscriptionKey];

    // Fallback to first active subscription if our product ID isn't found
    if (!expirationDate && Object.keys(activeSubscriptions).length > 0) {
      const fallbackKey = Object.keys(activeSubscriptions)[0];
      const fallbackExpiration = customerInfo.allExpirationDates[fallbackKey];
      console.log('IAP: Using fallback subscription key:', fallbackKey);
      return await updateSubscriptionInDatabase(customerInfo, userId, fallbackKey);
    }

    // Determine subscription tier
    const tier = getProductTier(productId);

    // Validate required data
    if (!tier || tier === 'free') {
      console.error('IAP: Invalid product tier for product:', productId);
      return { success: false, message: 'Invalid subscription product' };
    }

    if (!expirationDate) {
      console.error('IAP: No expiration date found for subscription');
      return { success: false, message: 'Subscription expiration date not found' };
    }

    // Update user profile in Supabase
    const { error } = await supabase
      .from('profiles')
      .update({
        subscription_tier: tier,
        subscription_status: 'active',
        subscription_start_date: new Date().toISOString(),
        subscription_end_date: expirationDate,
        subscription_expires_at: expirationDate,
        apple_transaction_id: Object.keys(activeSubscriptions)[0] || subscriptionKey,
        apple_product_id: productId,
        last_receipt_validation: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('IAP: Database update error:', error);
      console.error('IAP: Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      return { success: false, message: `Failed to update subscription status: ${error.message}` };
    }

    console.log('IAP: Successfully updated subscription in database');
    console.log('IAP: Updated user to tier:', tier, 'expires:', expirationDate);
    return { success: true, message: 'Subscription updated successfully' };

  } catch (error) {
    console.error('IAP: Error updating subscription in database:', error);
    return { success: false, message: 'Failed to update subscription status' };
  }
};

// Restore purchases using RevenueCat
export const restorePurchases = async (
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('IAP: Restoring purchases with RevenueCat...');

    // Check if running in Expo Go
    const isExpoGo = Constants.appOwnership === 'expo';
    if (isExpoGo) {
      return { success: false, message: 'Purchase restoration is not available in Expo Go.' };
    }

    // Ensure RevenueCat is initialized
    const isInitialized = await initializeIAP();
    if (!isInitialized) {
      return { success: false, message: 'Failed to initialize purchase system.' };
    }

    // Authenticate user with RevenueCat using robust authentication
    console.log('IAP: Authenticating user for restore purchases...');
    const authResult = await authenticateUserWithRevenueCat(userId);

    if (!authResult.success) {
      console.error('IAP: User authentication failed for restore:', authResult.error);
      return {
        success: false,
        message: 'Failed to authenticate with purchase system for restore. Please try again.'
      };
    }

    if (authResult.isAnonymous) {
      console.warn('IAP: Restoring purchases with anonymous user - may have limited results');
    }
    const customerInfo = await Purchases.restorePurchases();

    console.log('IAP: Restore completed, customer info:', customerInfo);

    // Check if there are any active subscriptions
    const activeSubscriptions = customerInfo.activeSubscriptions;

    if (Object.keys(activeSubscriptions).length > 0) {
      console.log('IAP: Found active subscriptions:', Object.keys(activeSubscriptions));

      // Update database with restored subscription
      const productId = Object.keys(activeSubscriptions)[0]; // Get first active subscription
      const updateResult = await updateSubscriptionInDatabase(customerInfo, userId, productId);

      if (updateResult.success) {
        return { success: true, message: 'Successfully restored your subscription!' };
      } else {
        return { success: false, message: 'Subscription found but failed to update. Please contact support.' };
      }
    } else {
      console.log('IAP: No active subscriptions found');
      return { success: false, message: 'No active subscriptions found to restore.' };
    }

  } catch (error) {
    console.error('IAP: Restore error:', error);
    return { success: false, message: 'Failed to restore purchases. Please try again.' };
  }
};



// Check subscription status using RevenueCat
export const checkSubscriptionStatus = async (
  userId: string
): Promise<{ subscription_tier: string; status: string; expires_at?: string }> => {
  try {
    console.log('IAP: Checking subscription status for user:', userId);

    // Check if running in Expo Go
    const isExpoGo = Constants.appOwnership === 'expo';
    if (isExpoGo) {
      return { subscription_tier: 'free', status: 'unavailable' };
    }

    // Ensure RevenueCat is initialized
    const isInitialized = await initializeIAP();
    if (!isInitialized) {
      return { subscription_tier: 'free', status: 'error' };
    }

    // Authenticate user and get customer info with error handling
    try {
      console.log('IAP: Authenticating user for status check...');
      const authResult = await authenticateUserWithRevenueCat(userId);

      if (!authResult.success) {
        console.error('IAP: Authentication failed for status check:', authResult.error);
        return { subscription_tier: 'free', status: 'error' };
      }

      if (authResult.isAnonymous) {
        console.warn('IAP: Checking status with anonymous user');
      }

      const customerInfo = await Purchases.getCustomerInfo();

    console.log('IAP: Customer info retrieved:', customerInfo);

    // Check active subscriptions
    const activeSubscriptions = customerInfo.activeSubscriptions;

    if (Object.keys(activeSubscriptions).length > 0) {
      const productId = Object.keys(activeSubscriptions)[0];
      const tier = getProductTier(productId);
      const expirationDate = customerInfo.allExpirationDates[productId];

      return {
        subscription_tier: tier,
        status: 'active',
        expires_at: expirationDate?.toISOString()
      };
    } else {
      return { subscription_tier: 'free', status: 'inactive' };
    }

    } catch (revenueCatError) {
      console.error('IAP: RevenueCat error during status check:', revenueCatError);
      return { subscription_tier: 'free', status: 'error' };
    }

  } catch (error) {
    console.error('IAP: Subscription status check error:', error);
    return { subscription_tier: 'free', status: 'error' };
  }
};

// Helper function to format price for RevenueCat packages
export const formatPrice = (pkg: PurchasesPackage): string => {
  return pkg.product.priceString || pkg.product.price.toString() || 'Price unavailable';
};

// Debug function to check RevenueCat configuration
export const debugRevenueCatConfiguration = async (): Promise<void> => {
  try {
    console.log('=== REVENUECAT DEBUG INFO ===');

    const isExpoGo = Constants.appOwnership === 'expo';
    console.log('IAP DEBUG: Running in Expo Go:', isExpoGo);

    if (isExpoGo) {
      console.log('IAP DEBUG: Cannot debug RevenueCat in Expo Go');
      return;
    }

    const isInitialized = await initializeIAP();
    console.log('IAP DEBUG: RevenueCat initialized:', isInitialized);

    if (!isInitialized) {
      console.log('IAP DEBUG: Cannot proceed - RevenueCat not initialized');
      return;
    }

    const offerings = await Purchases.getOfferings();
    console.log('IAP DEBUG: Current offering exists:', !!offerings.current);
    console.log('IAP DEBUG: Current offering identifier:', offerings.current?.identifier);
    console.log('IAP DEBUG: All offerings count:', Object.keys(offerings.all).length);
    console.log('IAP DEBUG: All offering identifiers:', Object.keys(offerings.all));

    // Check each offering
    for (const [key, offering] of Object.entries(offerings.all)) {
      console.log(`IAP DEBUG: Offering "${key}":`, {
        identifier: offering.identifier,
        packageCount: offering.availablePackages.length,
        packages: offering.availablePackages.map(pkg => ({
          identifier: pkg.identifier,
          productId: pkg.product.identifier,
          price: pkg.product.priceString
        }))
      });
    }

    // Check customer info
    const customerInfo = await Purchases.getCustomerInfo();
    console.log('IAP DEBUG: Customer info:', {
      originalAppUserId: customerInfo.originalAppUserId,
      activeSubscriptions: customerInfo.activeSubscriptions,
      entitlements: Object.keys(customerInfo.entitlements.active)
    });

    console.log('=== END REVENUECAT DEBUG ===');
  } catch (error) {
    console.error('IAP DEBUG: Error during debug:', error);
  }
};



// Helper function to get product tier
export const getProductTier = (productId: string): string => {
  const productInfo = IAP_PRODUCTS[productId];
  return productInfo?.tier || 'free';
};



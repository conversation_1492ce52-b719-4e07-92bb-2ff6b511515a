import FontAwesome from '@expo/vector-icons/FontAwesome';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import 'react-native-reanimated';
import { LogBox } from 'react-native';
import * as WebBrowser from 'expo-web-browser';

import { useColorScheme } from '@/components/useColorScheme';
import { LanguageProvider } from '../contexts/LanguageContext';
import { AuthProvider } from '../contexts/AuthContext';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: '(tabs)',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Set up enhanced console logging
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleInfo = console.info;

// Override console methods to add timestamps and make them more visible
console.log = function(...args) {
  const timestamp = new Date().toISOString();
  originalConsoleLog(`[${timestamp}] LOG:`, ...args);
};

console.error = function(...args) {
  const timestamp = new Date().toISOString();
  originalConsoleError(`[${timestamp}] ERROR:`, ...args);
};

console.warn = function(...args) {
  const timestamp = new Date().toISOString();
  originalConsoleWarn(`[${timestamp}] WARN:`, ...args);
};

console.info = function(...args) {
  const timestamp = new Date().toISOString();
  originalConsoleInfo(`[${timestamp}] INFO:`, ...args);
};

// Ignore specific warnings that might clutter the console
LogBox.ignoreLogs([
  'Animated: `useNativeDriver`',
  'AsyncStorage has been extracted from react-native',
]);

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    ...FontAwesome.font,
  });

  // Complete any pending auth sessions
  useEffect(() => {
    // This is critical for the auth flow to work properly
    const result = WebBrowser.maybeCompleteAuthSession();
    console.log('WebBrowser.maybeCompleteAuthSession result:', result);
  }, []);

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  const colorScheme = useColorScheme();

  return (
    <LanguageProvider>
      <AuthProvider>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="index" />
            <Stack.Screen name="feature-select" />
            <Stack.Screen name="scan" />
            <Stack.Screen name="pills" />
            <Stack.Screen name="summary" />
            <Stack.Screen name="modal" options={{ presentation: 'modal' }} />
            <Stack.Screen name="medication/[id]" />
            <Stack.Screen name="profile" />
            <Stack.Screen name="auth/login" />
            <Stack.Screen name="auth/callback" />
            <Stack.Screen name="livepills" />
            <Stack.Screen name="subscription" />
            <Stack.Screen name="landing" />
          </Stack>
        </ThemeProvider>
      </AuthProvider>
    </LanguageProvider>
  );
}

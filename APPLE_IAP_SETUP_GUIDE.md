# Apple In-App Purchase Setup Guide

## ✅ **What We've Implemented:**

### 1. **Code Changes Complete:**
- ✅ Removed all Stripe dependencies and code
- ✅ Added `expo-in-app-purchases` package
- ✅ Created `services/appleIAPService.ts` with full IAP functionality
- ✅ Updated `app/subscription.tsx` to use Apple IAP instead of Stripe
- ✅ Created database migration for Apple IAP fields
- ✅ Updated usage limits system to work with Apple IAP

### 2. **Product IDs Configured:**
- **Pro Monthly**: `com.pilllogic.app.pro.monthly.v2` - $4.99/month (Updated Product ID)
- **Premium Monthly**: `com.pilllogic.app.premium.monthly` - $5.99/month

---

## 🚀 **Next Steps - App Store Connect Setup:**

### **Step 1: Create In-App Purchase Products**

1. **Go to App Store Connect** → Your App → Features → In-App Purchases
2. **Create New Product** (do this twice, once for each tier):

**For Pro Plan:**
- **Type**: Auto-Renewable Subscription
- **Product ID**: `com.pilllogic.app.pro.monthly.v2`
- **Reference Name**: PillLogic Pro Monthly
- **Subscription Group**: Create new group "PillLogic Subscriptions"
- **Subscription Duration**: 1 Month
- **Price**: $4.99 USD

**For Premium Plan:**
- **Type**: Auto-Renewable Subscription  
- **Product ID**: `com.pilllogic.app.premium.monthly`
- **Reference Name**: PillLogic Premium Monthly
- **Subscription Group**: Same group "PillLogic Subscriptions"
- **Subscription Duration**: 1 Month
- **Price**: $5.99 USD

### **Step 2: Configure Subscription Details**

For each subscription, add:
- **Display Name**: "Pro Plan" / "Premium Plan"
- **Description**: 
  - Pro: "Unlimited pill scans + 5 live scans per day"
  - Premium: "Everything unlocked. Unlimited counts. Doctor note analysis."

### **Step 3: Set Up Subscription Group**

1. **Subscription Group Settings**:
   - Name: "PillLogic Subscriptions"
   - Set Premium as higher level than Pro (for upgrades)

### **Step 4: Add Localizations**

Add localizations for your target markets (at minimum English).

---

## 🧪 **Testing Your Implementation:**

### **Option 1: Sandbox Testing (Recommended)**

1. **Create Sandbox Tester Account** in App Store Connect
2. **Build with EAS Build**: `eas build --platform ios --profile development`
3. **Install on device** and test with sandbox account
4. **Test Purchase Flow**: Try purchasing both Pro and Premium
5. **Test Restore**: Test restoring purchases

### **Option 2: TestFlight Testing**

1. **Submit to TestFlight**: `eas submit --platform ios`
2. **Add internal testers**
3. **Test full purchase flow**

---

## 📋 **Database Migration Required:**

Run this SQL in your Supabase SQL editor:

```sql
-- The migration file is already created at:
-- supabase/migrations/20250713000000_migrate_to_apple_iap.sql

-- You need to run it in Supabase dashboard or via CLI
```

---

## 🔧 **Key Features Implemented:**

### **Apple IAP Service (`services/appleIAPService.ts`):**
- ✅ Initialize IAP connection
- ✅ Fetch products from App Store
- ✅ Handle purchases with receipt validation
- ✅ Restore previous purchases
- ✅ Check subscription status
- ✅ Update database with subscription info

### **Updated Subscription Screen:**
- ✅ Shows real App Store prices
- ✅ iOS-only purchase buttons
- ✅ Restore purchases functionality
- ✅ Proper error handling
- ✅ Links to iOS Settings for subscription management

### **Database Schema:**
- ✅ Added Apple IAP fields to profiles table
- ✅ Created functions for subscription management
- ✅ Maintains compatibility with existing usage limits

---

## ⚠️ **Important Notes:**

1. **iOS Only**: In-App Purchases only work on iOS devices
2. **Production Testing**: Apple IAP requires real App Store environment for full testing
3. **Receipt Validation**: Currently using basic validation - consider implementing server-side validation for production
4. **Subscription Management**: Users manage subscriptions through iOS Settings, not in-app

---

## 🎯 **App Store Approval:**

This implementation should satisfy **Guideline 3.1.1** because:
- ✅ Completely removed Stripe payment system
- ✅ Uses only Apple In-App Purchase for digital subscriptions
- ✅ No external payment links or buttons
- ✅ Follows Apple's subscription guidelines

---

## 🚨 **Before Submitting to App Store:**

1. **Test on real iOS device** with App Store Connect products
2. **Verify subscription tiers** work correctly
3. **Test restore purchases** functionality
4. **Ensure no Stripe references** remain in the app
5. **Run the database migration** in production Supabase

Your app should now be compliant with Apple's In-App Purchase requirements!

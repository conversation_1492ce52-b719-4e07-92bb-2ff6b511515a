import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get validation statistics for the last 24 hours
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()

    // Get overall stats
    const { data: overallStats, error: overallError } = await supabaseClient
      .from('validation_logs')
      .select('success, environment')
      .gte('created_at', twentyFourHoursAgo)

    if (overallError) {
      throw overallError
    }

    // Calculate statistics
    const totalAttempts = overallStats?.length || 0
    const successfulAttempts = overallStats?.filter(log => log.success).length || 0
    const failedAttempts = totalAttempts - successfulAttempts
    const successRate = totalAttempts > 0 ? (successfulAttempts / totalAttempts * 100).toFixed(2) : '0'

    // Environment breakdown
    const productionAttempts = overallStats?.filter(log => log.environment === 'production').length || 0
    const sandboxAttempts = overallStats?.filter(log => log.environment === 'sandbox').length || 0
    const unknownAttempts = overallStats?.filter(log => !log.environment || log.environment === 'unknown').length || 0

    // Get recent failures with details
    const { data: recentFailures, error: failuresError } = await supabaseClient
      .from('validation_logs')
      .select('product_id, error_message, environment, created_at')
      .eq('success', false)
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false })
      .limit(10)

    if (failuresError) {
      throw failuresError
    }

    // Get hourly breakdown for the last 24 hours
    const { data: hourlyStats, error: hourlyError } = await supabaseClient
      .rpc('get_hourly_validation_stats', { hours_back: 24 })

    const response = {
      period: '24 hours',
      timestamp: new Date().toISOString(),
      summary: {
        totalAttempts,
        successfulAttempts,
        failedAttempts,
        successRate: `${successRate}%`
      },
      environmentBreakdown: {
        production: productionAttempts,
        sandbox: sandboxAttempts,
        unknown: unknownAttempts
      },
      recentFailures: recentFailures?.map(failure => ({
        productId: failure.product_id,
        error: failure.error_message,
        environment: failure.environment,
        timestamp: failure.created_at
      })) || [],
      hourlyStats: hourlyStats || [],
      status: totalAttempts === 0 ? 'NO_DATA' : 
              successRate === '100.00' ? 'EXCELLENT' :
              parseFloat(successRate) >= 95 ? 'GOOD' :
              parseFloat(successRate) >= 80 ? 'WARNING' : 'CRITICAL'
    }

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Monitoring error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to fetch validation statistics',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

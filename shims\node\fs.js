// Minimal implementation of the Node.js fs module for React Native
const EventEmitter = require('./events');
const { Buffer } = require('./buffer');

// In-memory file system
const memfs = {
  '/': {
    type: 'directory',
    children: {}
  }
};

function normalizePath(path) {
  if (!path.startsWith('/')) {
    path = '/' + path;
  }
  return path;
}

function getPathComponents(path) {
  path = normalizePath(path);
  return path.split('/').filter(Boolean);
}

function getParentPath(path) {
  const components = getPathComponents(path);
  components.pop();
  return '/' + components.join('/');
}

function getPathObject(path) {
  path = normalizePath(path);
  if (path === '/') return memfs['/'];
  
  const components = getPathComponents(path);
  let current = memfs['/'];
  
  for (const component of components) {
    if (!current.children[component]) {
      return null;
    }
    current = current.children[component];
  }
  
  return current;
}

function createPathObject(path, type, content) {
  path = normalizePath(path);
  if (path === '/') return;
  
  const components = getPathComponents(path);
  const name = components.pop();
  let current = memfs['/'];
  
  for (const component of components) {
    if (!current.children[component]) {
      current.children[component] = {
        type: 'directory',
        children: {}
      };
    }
    current = current.children[component];
  }
  
  current.children[name] = {
    type,
    ...(type === 'directory' ? { children: {} } : { content })
  };
  
  return current.children[name];
}

class FSWatcher extends EventEmitter {
  constructor() {
    super();
  }

  close() {
    this.emit('close');
  }
}

class ReadStream extends EventEmitter {
  constructor(path, options) {
    super();
    this.path = path;
    this.options = options || {};
    this.bytesRead = 0;
    this.pos = 0;
    
    process.nextTick(() => {
      const file = getPathObject(path);
      if (!file || file.type !== 'file') {
        this.emit('error', new Error(`ENOENT: no such file or directory, open '${path}'`));
        return;
      }
      
      const content = file.content;
      this.emit('open');
      this.emit('data', content);
      this.emit('end');
      this.emit('close');
    });
  }

  close(callback) {
    if (callback) process.nextTick(callback);
    this.emit('close');
  }
}

class WriteStream extends EventEmitter {
  constructor(path, options) {
    super();
    this.path = path;
    this.options = options || {};
    this.bytesWritten = 0;
    this.buffer = [];
    
    process.nextTick(() => {
      this.emit('open');
    });
  }

  write(chunk, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = null;
    }
    
    if (typeof chunk === 'string') {
      chunk = Buffer.from(chunk, encoding || 'utf8');
    }
    
    this.buffer.push(chunk);
    this.bytesWritten += chunk.length;
    
    if (callback) process.nextTick(callback);
    return true;
  }

  end(chunk, encoding, callback) {
    if (chunk) {
      this.write(chunk, encoding);
    }
    
    const content = Buffer.concat(this.buffer);
    createPathObject(this.path, 'file', content);
    
    if (callback) process.nextTick(callback);
    this.emit('finish');
    this.emit('close');
  }

  close(callback) {
    if (callback) process.nextTick(callback);
    this.emit('close');
  }
}

// Synchronous functions
function existsSync(path) {
  return getPathObject(path) !== null;
}

function mkdirSync(path, options) {
  createPathObject(path, 'directory');
}

function readdirSync(path, options) {
  const dir = getPathObject(path);
  if (!dir || dir.type !== 'directory') {
    throw new Error(`ENOENT: no such file or directory, scandir '${path}'`);
  }
  return Object.keys(dir.children);
}

function readFileSync(path, options) {
  const file = getPathObject(path);
  if (!file || file.type !== 'file') {
    throw new Error(`ENOENT: no such file or directory, open '${path}'`);
  }
  
  const content = file.content;
  if (options === 'utf8' || (options && options.encoding === 'utf8')) {
    return content.toString('utf8');
  }
  return content;
}

function writeFileSync(path, data, options) {
  if (typeof data === 'string') {
    data = Buffer.from(data, (options && options.encoding) || 'utf8');
  }
  createPathObject(path, 'file', data);
}

function unlinkSync(path) {
  const parentPath = getParentPath(path);
  const parent = getPathObject(parentPath);
  if (!parent || parent.type !== 'directory') {
    throw new Error(`ENOENT: no such file or directory, unlink '${path}'`);
  }
  
  const name = getPathComponents(path).pop();
  delete parent.children[name];
}

function rmdirSync(path) {
  const dir = getPathObject(path);
  if (!dir || dir.type !== 'directory') {
    throw new Error(`ENOENT: no such file or directory, rmdir '${path}'`);
  }
  
  if (Object.keys(dir.children).length > 0) {
    throw new Error(`ENOTEMPTY: directory not empty, rmdir '${path}'`);
  }
  
  unlinkSync(path);
}

function statSync(path) {
  const obj = getPathObject(path);
  if (!obj) {
    throw new Error(`ENOENT: no such file or directory, stat '${path}'`);
  }
  
  return {
    isFile: () => obj.type === 'file',
    isDirectory: () => obj.type === 'directory',
    isSymbolicLink: () => false,
    size: obj.type === 'file' ? obj.content.length : 0,
    mtime: new Date(),
    atime: new Date(),
    ctime: new Date(),
    birthtime: new Date()
  };
}

// Asynchronous functions
function exists(path, callback) {
  process.nextTick(() => {
    callback(getPathObject(path) !== null);
  });
}

function mkdir(path, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    try {
      mkdirSync(path, options);
      callback(null);
    } catch (err) {
      callback(err);
    }
  });
}

function readdir(path, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    try {
      const files = readdirSync(path, options);
      callback(null, files);
    } catch (err) {
      callback(err);
    }
  });
}

function readFile(path, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    try {
      const content = readFileSync(path, options);
      callback(null, content);
    } catch (err) {
      callback(err);
    }
  });
}

function writeFile(path, data, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    try {
      writeFileSync(path, data, options);
      callback(null);
    } catch (err) {
      callback(err);
    }
  });
}

function unlink(path, callback) {
  process.nextTick(() => {
    try {
      unlinkSync(path);
      callback(null);
    } catch (err) {
      callback(err);
    }
  });
}

function rmdir(path, callback) {
  process.nextTick(() => {
    try {
      rmdirSync(path);
      callback(null);
    } catch (err) {
      callback(err);
    }
  });
}

function stat(path, callback) {
  process.nextTick(() => {
    try {
      const stats = statSync(path);
      callback(null, stats);
    } catch (err) {
      callback(err);
    }
  });
}

function createReadStream(path, options) {
  return new ReadStream(path, options);
}

function createWriteStream(path, options) {
  return new WriteStream(path, options);
}

function watch(filename, options, listener) {
  if (typeof options === 'function') {
    listener = options;
    options = {};
  }
  
  const watcher = new FSWatcher();
  if (listener) {
    watcher.on('change', listener);
  }
  
  return watcher;
}

module.exports = {
  existsSync,
  mkdirSync,
  readdirSync,
  readFileSync,
  writeFileSync,
  unlinkSync,
  rmdirSync,
  statSync,
  exists,
  mkdir,
  readdir,
  readFile,
  writeFile,
  unlink,
  rmdir,
  stat,
  createReadStream,
  createWriteStream,
  watch,
  FSWatcher,
  ReadStream,
  WriteStream,
  constants: {
    F_OK: 0,
    R_OK: 4,
    W_OK: 2,
    X_OK: 1
  }
};

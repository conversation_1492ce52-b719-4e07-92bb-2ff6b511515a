import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import AppShell from '../components/layout/AppShell';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';
import { getMedicationAnalyses, StoredAnalysis } from '../services/storageService';

export default function SummaryScreen() {
  const router = useRouter();
  const { t } = useLanguage();
  const [analyses, setAnalyses] = useState<StoredAnalysis[]>([]);
  const [filteredAnalyses, setFilteredAnalyses] = useState<StoredAnalysis[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadAnalyses();
  }, []);

  const loadAnalyses = async () => {
    try {
      setLoading(true);
      console.log('Loading medication analyses from storage');
      const data = await getMedicationAnalyses();
      setAnalyses(data);
      setFilteredAnalyses(data);
    } catch (error) {
      console.error('Error loading analyses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() === '') {
      setFilteredAnalyses(analyses);
    } else {
      const filtered = analyses.filter(
        (item) =>
          item.name.toLowerCase().includes(text.toLowerCase()) ||
          item.medications.some((med) =>
            med.name.toLowerCase().includes(text.toLowerCase())
          )
      );
      setFilteredAnalyses(filtered);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setFilteredAnalyses(analyses);
  };

  const handleViewDetails = (item: StoredAnalysis) => {
    router.push(`/medication/${item.id}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const renderAnalysisItem = ({ item }: { item: StoredAnalysis }) => (
    <TouchableOpacity
      style={styles.analysisCard}
      onPress={() => handleViewDetails(item)}
    >
      <View style={styles.cardHeader}>
        <View style={styles.iconContainer}>
          <Ionicons name="document-text-outline" size={20} color={Colors.docPurple.DEFAULT} />
        </View>
        <View style={styles.headerContent}>
          <Text style={styles.cardTitle}>{item.name}</Text>
          <Text style={styles.cardDate}>{formatDate(item.date)}</Text>
        </View>
      </View>

      <View style={styles.cardContent}>
        <Text style={styles.medicationCount}>
          {item.medications.length} {item.medications.length === 1 ? t('medication') : t('medications')}
        </Text>
        {item.medications.slice(0, 2).map((med, index) => (
          <Text key={index} style={styles.medicationItem}>
            • {med.name} {med.dosage}
          </Text>
        ))}
        {item.medications.length > 2 && (
          <Text style={styles.moreText}>
            {t('andMore').replace('{count}', (item.medications.length - 2).toString())}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="document-text-outline" size={48} color={Colors.textTertiary} />
      <Text style={styles.emptyTitle}>
        {searchQuery ? t('noSearchResults') : t('noSavedAnalyses')}
      </Text>
      <Text style={styles.emptyDescription}>
        {searchQuery ? t('tryDifferentSearch') : t('takePhotoToStart')}
      </Text>
    </View>
  );

  return (
    <AppShell>
      <View style={styles.container}>
        <Text style={styles.title}>{t('medicationSummary')}</Text>

        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search-outline" size={20} color={Colors.textTertiary} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder={t('searchAnalyses')}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={handleClearSearch}>
                <Ionicons name="close-circle" size={18} color={Colors.textTertiary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'all' && styles.activeTab]}
            onPress={() => {
              setActiveTab('all');
              setFilteredAnalyses(analyses);
            }}
          >
            <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
              {t('all')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'meds' && styles.activeTab]}
            onPress={() => {
              setActiveTab('meds');
              const filtered = analyses.filter(item => item.type === 'medication');
              setFilteredAnalyses(filtered);
            }}
          >
            <Text style={[styles.tabText, activeTab === 'meds' && styles.activeTabText]}>
              {t('medications')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'pills' && styles.activeTab]}
            onPress={() => {
              setActiveTab('pills');
              const filtered = analyses.filter(item => item.type === 'pill-count');
              setFilteredAnalyses(filtered);
            }}
          >
            <Text style={[styles.tabText, activeTab === 'pills' && styles.activeTabText]}>
              {t('pillCounts')}
            </Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
            <Text style={styles.loadingText}>{t('loadingData')}</Text>
          </View>
        ) : (
          <FlatList
            data={filteredAnalyses}
            renderItem={renderAnalysisItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyState}
          />
        )}
      </View>
    </AppShell>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: Spacing.md,
    color: Colors.textPrimary,
  },
  searchContainer: {
    marginBottom: Spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  searchIcon: {
    marginRight: Spacing.xs,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: BorderRadius.sm,
  },
  activeTab: {
    backgroundColor: Colors.white,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  activeTabText: {
    color: Colors.docPurple.DEFAULT,
  },
  listContent: {
    paddingBottom: Spacing.xl,
  },
  analysisCard: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.docBlue.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  headerContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  cardDate: {
    fontSize: 12,
    color: Colors.textTertiary,
  },
  cardContent: {
    padding: Spacing.md,
  },
  medicationCount: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.docPurple.DEFAULT,
    marginBottom: Spacing.xs,
  },
  medicationItem: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  moreText: {
    fontSize: 12,
    color: Colors.textTertiary,
    marginTop: Spacing.xs,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.xl,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginTop: Spacing.md,
    marginBottom: Spacing.xs,
  },
  emptyDescription: {
    fontSize: 14,
    color: Colors.textTertiary,
    textAlign: 'center',
  },
});

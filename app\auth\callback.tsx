import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Platform, Alert, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { supabase } from '../../lib/supabase';
import { Colors, Spacing, BorderRadius } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import Constants from 'expo-constants';

export default function AuthCallbackScreen() {
  const router = useRouter();
  const { t } = useLanguage();
  const params = useLocalSearchParams();

  // Detect if running in Expo Go
  const isExpoGo = Constants.appOwnership === 'expo';
  const isIOS = Platform.OS === 'ios';

  // Helper function to safely log URLs with tokens
  const safelyLogUrl = (url: string) => {
    if (!url) return 'No URL';

    // Check if URL has a fragment
    if (url.includes('#')) {
      const baseUrl = url.split('#')[0];
      const fragment = url.split('#')[1];

      // Check if fragment contains tokens
      if (fragment.includes('access_token=')) {
        // Log base URL and indicate tokens are present
        return `${baseUrl}#[access_token and other params present]`;
      }
    }

    return url;
  };

  // No need to call WebBrowser.maybeCompleteAuthSession here
  // It's already called in _layout.tsx

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    // Set a timeout to redirect to login if we're stuck on this screen too long
    const timeoutId = setTimeout(() => {
      console.log('Callback screen timeout - redirecting to login to prevent infinite loading');

      // Show an alert to inform the user
      Alert.alert(
        'Authentication Timeout',
        'The authentication process is taking longer than expected. Would you like to try again?',
        [
          {
            text: 'Try Again',
            onPress: () => router.replace('/auth/login')
          },
          {
            text: 'Continue Waiting',
            onPress: () => console.log('User chose to continue waiting')
          }
        ]
      );
    }, 10000); // 10 seconds timeout - reduced from 15 seconds

    return () => clearTimeout(timeoutId);
  }, [router]);

  useEffect(() => {
    // Set up a listener for deep links
    const handleDeepLink = (event: { url: string }) => {
      console.log('Deep link received in callback screen:', safelyLogUrl(event.url));

      // If the URL contains tokens, process it
      const hasAccessToken = event.url.includes('#') &&
                            event.url.split('#')[1].includes('access_token=');
      const hasCode = event.url.includes('code=');

      if (hasAccessToken || hasCode) {
        console.log('Deep link contains authentication data:',
                    hasAccessToken ? 'access_token present' : 'code present');
        // We'll handle this in the main callback function
      }
    };

    // Add the event listener
    const subscription = Linking.addEventListener('url', handleDeepLink);

    // Handle the OAuth callback
    const handleOAuthCallback = async () => {
      try {
        console.log('Auth callback received with params:', JSON.stringify(params));
        console.log('Environment:', { isExpoGo, isIOS, platform: Platform.OS });

        // Get the full URL if available (for web or development)
        const fullUrl = window?.location?.href || '';
        console.log('Current URL path:', safelyLogUrl(fullUrl));

        // Also check for the initial URL (might contain tokens from a deep link)
        try {
          const initialUrl = await Linking.getInitialURL();
          if (initialUrl) {
            console.log('Initial URL found:', safelyLogUrl(initialUrl));

            // Check if the URL contains tokens
            const hasAccessToken = initialUrl.includes('#') &&
                                  initialUrl.split('#')[1].includes('access_token=');
            const hasCode = initialUrl.includes('code=');

            if (hasAccessToken || hasCode) {
              console.log('Initial URL contains authentication data:',
                          hasAccessToken ? 'access_token present' : 'code present');
            }
          }
        } catch (e) {
          console.error('Error getting initial URL:', e);
        }

        // For debugging - log all params
        Object.keys(params).forEach(key => {
          console.log(`Param ${key}:`, params[key]);
        });

        // Special handling for Expo Go on iOS
        if (isExpoGo && isIOS) {
          console.log('Special handling for Expo Go on iOS in callback screen');

          // Check if we have the special flags
          const isExpoGoIOS = params.expo_go_ios === 'true';
          const isManualRecovery = params.manual_recovery === 'true';

          if (isExpoGoIOS) {
            console.log('Received special Expo Go iOS flag - this is a direct callback from auth flow');

            // First, try to get the session directly
            try {
              console.log('Checking for existing session first...');
              const { data: { session: existingSession } } = await supabase.auth.getSession();

              if (existingSession) {
                console.log('Found existing session in callback - authentication was successful!');
                router.replace('/');
                return;
              }
            } catch (e) {
              console.error('Error checking for existing session:', e);
            }

            if (isManualRecovery) {
              console.log('This is a manual recovery attempt - using simplified approach');

              // Try to get the URL directly from Linking
              try {
                console.log('Checking for any deep links in callback...');
                const callbackUrl = await Linking.getInitialURL();

                if (callbackUrl && callbackUrl.includes('#access_token=')) {
                  console.log('Found access token in callback URL - attempting extraction');

                  // Extract tokens using regex
                  const accessTokenMatch = callbackUrl.match(/access_token=([^&]+)/);
                  const refreshTokenMatch = callbackUrl.match(/refresh_token=([^&]+)/);

                  if (accessTokenMatch && refreshTokenMatch) {
                    const extractedAccessToken = accessTokenMatch[1];
                    const extractedRefreshToken = refreshTokenMatch[1];

                    console.log('Successfully extracted tokens from callback URL');

                    // Try to set the session with these tokens
                    const { data, error } = await supabase.auth.setSession({
                      access_token: extractedAccessToken,
                      refresh_token: extractedRefreshToken
                    });

                    if (error) {
                      console.error('Error setting session with extracted tokens in callback:', error);
                    } else if (data.session) {
                      console.log('Successfully set session with extracted tokens in callback!');

                      // Wait for the session to be fully established
                      await new Promise(resolve => setTimeout(resolve, 1000));

                      // Double-check that the session is still valid
                      try {
                        const { data: { session: verifySession } } = await supabase.auth.getSession();
                        if (verifySession) {
                          console.log('Session verified after setting tokens');
                          router.replace('/');
                          return;
                        } else {
                          console.error('Session verification failed after setting tokens');
                        }
                      } catch (verifyError) {
                        console.error('Error verifying session:', verifyError);
                      }
                    }
                  }
                }
              } catch (extractError) {
                console.error('Error extracting tokens in callback:', extractError);
              }
            }

            // Wait a bit longer to ensure any session has time to be established
            console.log('Waiting for session to be established...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }

          // Try to get the session directly from Supabase
          try {
            console.log('Attempting to check for auth session directly from callback...');
            const { data: { session } } = await supabase.auth.getSession();

            if (session) {
              console.log('Found existing session in callback - authentication was successful!');

              // Force navigation to home screen immediately
              router.replace('/');
              return;
            } else {
              console.log('No session found in callback - checking for tokens...');

              // If this is a direct callback from auth flow, try a more aggressive approach
              if (isExpoGoIOS) {
                console.log('This is a direct callback - trying more aggressive session check');

                // Try to directly extract tokens from the URL
                try {
                  // Get the initial URL again
                  const directUrl = await Linking.getInitialURL();

                  if (directUrl && directUrl.includes('#access_token=')) {
                    console.log('Found access token in direct URL - attempting direct extraction');

                    // Extract tokens using regex
                    const accessTokenMatch = directUrl.match(/access_token=([^&]+)/);
                    const refreshTokenMatch = directUrl.match(/refresh_token=([^&]+)/);

                    if (accessTokenMatch && refreshTokenMatch) {
                      const extractedAccessToken = accessTokenMatch[1];
                      const extractedRefreshToken = refreshTokenMatch[1];

                      console.log('Successfully extracted tokens from URL');

                      // Try to set the session with these tokens
                      const { data, error } = await supabase.auth.setSession({
                        access_token: extractedAccessToken,
                        refresh_token: extractedRefreshToken
                      });

                      if (error) {
                        console.error('Error setting session with extracted tokens:', error);
                      } else if (data.session) {
                        console.log('Successfully set session with extracted tokens!');
                        router.replace('/');
                        return;
                      }
                    }
                  }
                } catch (extractError) {
                  console.error('Error extracting tokens from URL:', extractError);
                }

                // Try a few times with delays
                for (let i = 0; i < 3; i++) {
                  console.log(`Attempt ${i+1} to get session...`);
                  await new Promise(resolve => setTimeout(resolve, 1000));

                  try {
                    const { data: { session: retrySession } } = await supabase.auth.getSession();
                    if (retrySession) {
                      console.log(`Found session on retry attempt ${i+1}!`);
                      router.replace('/');
                      return;
                    }
                  } catch (retryError) {
                    console.error(`Error on retry attempt ${i+1}:`, retryError);
                  }
                }

                // If we still don't have a session, show a message and redirect to login
                console.log('Failed to get session after multiple attempts');
                Alert.alert(
                  'Authentication Status',
                  'We were unable to confirm if you were successfully signed in. Please try again.',
                  [
                    { text: 'Try Again', onPress: () => router.replace('/auth/login') }
                  ]
                );

                // Force redirect to login after a short delay
                setTimeout(() => {
                  console.log('Forcing redirect to login screen');
                  router.replace('/auth/login');
                }, 1000);

                return;
              }
            }
          } catch (e) {
            console.error('Error checking for session in callback:', e);
          }
        }

        // Enhanced token extraction for multiple redirect scenarios
        let accessToken = null;
        let refreshToken = null;
        let authCode = null;

        // Get the initial URL (might contain tokens from a deep link)
        let initialUrl = '';
        try {
          initialUrl = await Linking.getInitialURL() || '';
          if (initialUrl) {
            console.log('Using initial URL for token extraction:', initialUrl);
          }
        } catch (e) {
          console.error('Error getting initial URL:', e);
        }

        // Use all available URLs for extraction
        const urlsToCheck = [fullUrl, initialUrl].filter(Boolean);
        console.log(`Checking ${urlsToCheck.length} URLs for tokens`);

        // Extract from URL fragments (after #)
        for (const url of urlsToCheck) {
          if (url.includes('#')) {
            try {
              const fragmentParams = new URLSearchParams(url.split('#')[1]);
              accessToken = accessToken || fragmentParams.get('access_token');
              refreshToken = refreshToken || fragmentParams.get('refresh_token');
              if (accessToken || refreshToken) {
                console.log(`Extracted tokens from URL fragment in ${url === fullUrl ? 'fullUrl' : 'initialUrl'}`);
                console.log('Token extraction result:', { hasAccessToken: !!accessToken, hasRefreshToken: !!refreshToken });
              }
            } catch (e) {
              console.error(`Error parsing URL fragment from ${url === fullUrl ? 'fullUrl' : 'initialUrl'}:`, e);
            }
          }
        }

        // Extract from URL query params - handle multiple query parameter sections (from multiple redirects)
        for (const url of urlsToCheck) {
          if (url.includes('?')) {
            try {
              const queryParts = url.split('?').slice(1);
              for (const part of queryParts) {
                const cleanPart = part.split('#')[0];
                const queryParams = new URLSearchParams(cleanPart);

                // Check for error parameters
                const error = queryParams.get('error');
                const errorCode = queryParams.get('error_code');
                const errorDescription = queryParams.get('error_description');

                if (error) {
                  console.error(`Auth error in ${url === fullUrl ? 'fullUrl' : 'initialUrl'}:`, { error, errorCode, errorDescription });

                  // If we have a bad_oauth_state error, we should try to recover
                  if (errorCode === 'bad_oauth_state') {
                    console.log('Detected bad_oauth_state error, will try alternative authentication methods');
                    // Continue with extraction - we might still find tokens elsewhere
                  }
                }

                // Check for tokens in this query part
                const partAccessToken = queryParams.get('access_token');
                const partRefreshToken = queryParams.get('refresh_token');
                const partCode = queryParams.get('code');

                // Use the first valid tokens/code we find
                accessToken = accessToken || partAccessToken;
                refreshToken = refreshToken || partRefreshToken;
                authCode = authCode || partCode;

                if (partAccessToken || partRefreshToken || partCode) {
                  console.log(`Extracted from query part in ${url === fullUrl ? 'fullUrl' : 'initialUrl'}: token=${!!partAccessToken}, refresh=${!!partRefreshToken}, code=${!!partCode}`);
                }
              }
            } catch (e) {
              console.error(`Error parsing URL query parts from ${url === fullUrl ? 'fullUrl' : 'initialUrl'}:`, e);
            }
          }
        }

        // Also check params from Expo Router
        accessToken = accessToken || (params.access_token as string);
        refreshToken = refreshToken || (params.refresh_token as string);
        authCode = authCode || (params.code as string);

        console.log('Final extraction results:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          hasAuthCode: !!authCode
        });

        // Try to use tokens if available
        if (accessToken && refreshToken) {
          console.log('Attempting to set session with extracted tokens');

          try {
            const { data, error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (error) {
              console.error('Error setting session:', error);
              // Continue to next authentication method
            } else if (data.session) {
              console.log('Session set successfully with tokens, redirecting to home');

              // Force navigation to home screen immediately
              console.log('Redirecting to home immediately after setting session');
              router.replace('/');
              return;
            }
          } catch (sessionError) {
            console.error('Exception during session setting:', sessionError);
          }
        }

        // Try to use auth code if available
        if (authCode) {
          console.log('Attempting to exchange auth code for session:', authCode);

          try {
            // For Expo auth proxy, we need to manually exchange the code
            console.log('Exchanging code for session, code length:', authCode.length);
            const { data, error } = await supabase.auth.exchangeCodeForSession(authCode);

            if (error) {
              console.error('Error exchanging code for session:', error);
              // Continue to next authentication method
            } else if (data.session) {
              console.log('Session obtained from code exchange, redirecting to home');

              // Force navigation to home screen immediately
              console.log('Redirecting to home immediately after code exchange');
              router.replace('/');
              return;
            } else {
              console.log('No session from code exchange');
            }
          } catch (exchangeError) {
            console.error('Exception during code exchange:', exchangeError);
          }
        }

        // Special handling for Expo Go on iOS
        if (isExpoGo && isIOS) {
          console.log('Running in Expo Go on iOS - trying additional authentication methods');

          // Try to extract tokens from all available URLs in a different way (iOS specific)
          for (const url of urlsToCheck) {
            if (!url) continue;

            try {
              // iOS sometimes handles URLs differently, try a more permissive extraction
              const iosTokenMatch = url.match(/access_token=([^&]+)/);
              const iosRefreshMatch = url.match(/refresh_token=([^&]+)/);

              if (iosTokenMatch && iosRefreshMatch) {
                const iosAccessToken = iosTokenMatch[1];
                const iosRefreshToken = iosRefreshMatch[1];

                console.log(`Extracted tokens using iOS-specific method from ${url === fullUrl ? 'fullUrl' : 'initialUrl'}`);

                try {
                  const { data, error } = await supabase.auth.setSession({
                    access_token: iosAccessToken,
                    refresh_token: iosRefreshToken,
                  });

                  if (!error && data.session) {
                    console.log('Session set successfully with iOS-extracted tokens');
                    router.replace('/');
                    return;
                  }
                } catch (e) {
                  console.error('Error setting session with iOS-extracted tokens:', e);
                }
              }
            } catch (e) {
              console.error(`Error in iOS-specific token extraction from ${url === fullUrl ? 'fullUrl' : 'initialUrl'}:`, e);
            }
          }
        }

        // If we've reached this point, try checking for an existing session
        console.log('Trying to get existing session as fallback');
        try {
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (sessionError) {
            console.error('Error getting session:', sessionError);
          } else if (session) {
            console.log('Existing session found, redirecting to home');
            router.replace('/');
            return;
          } else {
            console.log('No existing session found');
          }
        } catch (sessionCheckError) {
          console.error('Exception during session check:', sessionCheckError);
        }

        // Check if we have a bad_oauth_state error in any of the URLs
        const hasBadOAuthStateError = urlsToCheck.some(url => url && url.includes('error_code=bad_oauth_state'));

        // If we've tried everything and still don't have a session, redirect to login
        console.log('All authentication methods failed, redirecting to login');

        // Special message for Expo Go users
        if (isExpoGo) {
          console.log('Note: Authentication in Expo Go may be less reliable. Consider building a development build for testing.');
        }

        // If we had a bad_oauth_state error, try a direct login attempt
        if (hasBadOAuthStateError) {
          console.log('Authentication failed due to OAuth state mismatch. Attempting direct recovery...');

          try {
            // Try to get a session directly from Supabase
            // This might work if the user was previously logged in
            const { data: { session: recoveredSession } } = await supabase.auth.getSession();

            if (recoveredSession) {
              console.log('Successfully recovered session after bad_oauth_state error!');
              router.replace('/');
              return;
            }

            // If that didn't work, try to initiate a new Google sign-in
            console.log('Could not recover session, showing error message');
            Alert.alert(
              'Authentication Error',
              'There was an issue with the authentication process. Please try again.',
              [
                {
                  text: 'Try Again',
                  onPress: () => router.replace('/auth/login')
                }
              ]
            );
          } catch (recoveryError) {
            console.error('Error during recovery attempt:', recoveryError);
            router.replace('/auth/login');
          }
        } else {
          // Standard redirect
          router.replace('/auth/login');
        }
      } catch (error) {
        console.error('Error handling OAuth callback:', error);
        router.replace('/auth/login');
      }
    };

    // Execute the callback handler
    handleOAuthCallback();

    // Cleanup function
    return () => {
      // Remove the deep link listener
      subscription.remove();
    };
  }, [params, router, isExpoGo, isIOS]);

  // Add state to track elapsed time
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showRefreshButton, setShowRefreshButton] = useState(false);

  // Handle manual refresh - defined before useEffect to avoid dependency issues
  const handleRefresh = async () => {
    console.log('Manual refresh requested by user');

    try {
      // Check if we have a session
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        console.log('Session found during manual refresh, redirecting to home');
        router.replace('/');
      } else {
        console.log('No session found during manual refresh');
        Alert.alert('Authentication Status', 'Still waiting for authentication to complete. Please try again in a few moments.');
      }
    } catch (error) {
      console.error('Error during manual refresh:', error);
    }
  };

  // TEMPORARILY COMMENTED OUT - Add a timer effect to track elapsed time and auto-refresh
  // useEffect(() => {
  //   const timer = setInterval(() => {
  //     setElapsedTime(prev => {
  //       const newTime = prev + 1;

  //       // Show refresh button after 15 seconds
  //       if (newTime >= 15 && !showRefreshButton) {
  //         setShowRefreshButton(true);
  //       }

  //       // Auto-refresh after 20 seconds
  //       if (newTime === 20) {
  //         console.log('Auto-refreshing after 20 seconds');
  //         handleRefresh();
  //       }

  //       return newTime;
  //     });
  //   }, 1000);

  //   return () => clearInterval(timer);
  // }, [showRefreshButton]);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
      <Text style={styles.text}>Authentication in Progress</Text>

      <View style={styles.infoContainer}>
        <Text style={styles.timerText}>
          Time remaining: {Math.max(0, 20 - elapsedTime)} seconds
        </Text>
        <Text style={styles.noteText}>
          <Text style={styles.boldText}>Important:</Text> After the timer completes, please <Text style={styles.boldText}>close the app completely</Text> and reopen it to finish logging in.
        </Text>
        <Text style={styles.instructionText}>
          On iOS: Swipe up from the bottom of the screen (or double-click the home button) and swipe the app up to close it.
          {'\n\n'}
          On Android: Tap the square/recent apps button and swipe the app up to close it.
        </Text>
      </View>

      {showRefreshButton && (
        <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
          <Text style={styles.refreshButtonText}>Check Login Status</Text>
        </TouchableOpacity>
      )}

      {elapsedTime >= 20 && (
        <TouchableOpacity style={styles.closeAppButton} onPress={() => Alert.alert(
          "Login Process",
          "Please close the app now and reopen it to complete the login process.",
          [{ text: "OK", style: "default" }]
        )}>
          <Text style={styles.closeAppButtonText}>Close App Instructions</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Spacing.lg,
  },
  text: {
    marginTop: 20,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  infoContainer: {
    marginTop: Spacing.xl,
    backgroundColor: Colors.docPurple.light,
    padding: Spacing.lg,
    borderRadius: BorderRadius.md,
    width: '100%',
    maxWidth: 400,
  },
  infoText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  timerText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    fontWeight: '500',
  },
  refreshButton: {
    marginTop: Spacing.xl,
    backgroundColor: Colors.docPurple.DEFAULT,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.md,
  },
  refreshButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  closeAppButton: {
    marginTop: Spacing.md,
    backgroundColor: Colors.error,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.md,
  },
  closeAppButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  noteText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
    fontStyle: 'italic',
  },
  instructionText: {
    fontSize: 13,
    color: Colors.textSecondary,
    textAlign: 'left',
    lineHeight: 18,
  },
  boldText: {
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
});

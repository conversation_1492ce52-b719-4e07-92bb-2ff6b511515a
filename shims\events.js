// Minimal implementation of the Node.js events module for React Native

class EventEmitter {
  constructor() {
    this._events = {};
    this._eventsCount = 0;
    this._maxListeners = undefined;
  }

  setMaxListeners(n) {
    this._maxListeners = n;
    return this;
  }

  getMaxListeners() {
    return this._maxListeners === undefined ? 10 : this._maxListeners;
  }

  emit(type, ...args) {
    if (!this._events[type]) return false;

    const handlers = this._events[type];
    if (Array.isArray(handlers)) {
      for (let i = 0; i < handlers.length; i++) {
        try {
          handlers[i].apply(this, args);
        } catch (err) {
          console.error(err);
        }
      }
    } else {
      try {
        handlers.apply(this, args);
      } catch (err) {
        console.error(err);
      }
    }

    return true;
  }

  addListener(type, listener) {
    return this.on(type, listener);
  }

  on(type, listener) {
    if (!this._events[type]) {
      this._events[type] = listener;
      this._eventsCount++;
    } else if (Array.isArray(this._events[type])) {
      this._events[type].push(listener);
    } else {
      this._events[type] = [this._events[type], listener];
    }
    return this;
  }

  once(type, listener) {
    const onceWrapper = (...args) => {
      this.removeListener(type, onceWrapper);
      listener.apply(this, args);
    };
    onceWrapper.listener = listener;
    this.on(type, onceWrapper);
    return this;
  }

  removeListener(type, listener) {
    if (!this._events[type]) return this;

    if (this._events[type] === listener || 
        (this._events[type].listener && this._events[type].listener === listener)) {
      delete this._events[type];
      this._eventsCount--;
    } else if (Array.isArray(this._events[type])) {
      const idx = this._events[type].indexOf(listener);
      if (idx !== -1) {
        this._events[type].splice(idx, 1);
        if (this._events[type].length === 1) {
          this._events[type] = this._events[type][0];
        }
      }
    }

    return this;
  }

  off(type, listener) {
    return this.removeListener(type, listener);
  }

  removeAllListeners(type) {
    if (type) {
      if (this._events[type]) {
        delete this._events[type];
        this._eventsCount--;
      }
    } else {
      this._events = {};
      this._eventsCount = 0;
    }
    return this;
  }

  listeners(type) {
    if (!this._events[type]) return [];
    if (Array.isArray(this._events[type])) {
      return this._events[type].slice();
    } else {
      return [this._events[type]];
    }
  }

  listenerCount(type) {
    if (!this._events[type]) return 0;
    if (Array.isArray(this._events[type])) {
      return this._events[type].length;
    } else {
      return 1;
    }
  }

  eventNames() {
    return Object.keys(this._events);
  }
}

module.exports = EventEmitter;
module.exports.EventEmitter = EventEmitter;
module.exports.once = function once(emitter, name) {
  return new Promise((resolve, reject) => {
    function eventListener(...args) {
      if (errorListener !== undefined) {
        emitter.removeListener('error', errorListener);
      }
      resolve(args.length === 1 ? args[0] : args);
    }
    let errorListener;

    // Adding an error listener is not optional but on error we
    // only reject the promise
    if (name !== 'error') {
      errorListener = (err) => {
        emitter.removeListener(name, eventListener);
        reject(err);
      };
      emitter.once('error', errorListener);
    }

    emitter.once(name, eventListener);
  });
};

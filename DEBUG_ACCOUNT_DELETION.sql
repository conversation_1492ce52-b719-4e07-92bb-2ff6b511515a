-- DEBUG ACCOUNT DELETION
-- Run these queries one by one to debug the account deletion issue

-- ============================================================================
-- STEP 1: Verify the function exists
-- ============================================================================

SELECT 
  routine_name, 
  routine_type,
  security_type,
  routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name = 'delete_user_account';

-- This should return one row with the function details

-- ============================================================================
-- STEP 2: Check what tables exist in your database
-- ============================================================================

SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'profiles',
    'feature_usage', 
    'daily_feature_usage',
    'live_session_tracking',
    'usage_logs',
    'subscriptions',
    'purchase_validation_log'
  )
ORDER BY table_name;

-- This shows which tables actually exist (some might not exist in your schema)

-- ============================================================================
-- STEP 3: Find a test user ID to work with
-- ============================================================================

-- Get a list of users (replace with your test account email)
SELECT id, email, created_at 
FROM auth.users 
WHERE email LIKE '%test%' OR email LIKE '%dummy%'
ORDER BY created_at DESC
LIMIT 5;

-- Copy one of the user IDs from above for testing

-- ============================================================================
-- STEP 4: Test the function with a specific user ID
-- ============================================================================

-- IMPORTANT: Replace 'YOUR_TEST_USER_ID_HERE' with an actual user ID from step 3
-- DO NOT run this with a real account you want to keep!

-- SELECT public.delete_user_account('YOUR_TEST_USER_ID_HERE');

-- Uncomment the line above and replace the ID to test

-- ============================================================================
-- STEP 5: Check what data exists for a user before deletion
-- ============================================================================

-- Replace 'YOUR_TEST_USER_ID_HERE' with the test user ID
-- This shows what data exists for the user before deletion

/*
-- Profiles
SELECT 'profiles' as table_name, count(*) as count 
FROM public.profiles 
WHERE id = 'YOUR_TEST_USER_ID_HERE'

UNION ALL

-- Feature usage
SELECT 'feature_usage' as table_name, count(*) as count 
FROM public.feature_usage 
WHERE user_id = 'YOUR_TEST_USER_ID_HERE'

UNION ALL

-- Daily feature usage  
SELECT 'daily_feature_usage' as table_name, count(*) as count 
FROM public.daily_feature_usage 
WHERE user_id = 'YOUR_TEST_USER_ID_HERE'

UNION ALL

-- Live session tracking
SELECT 'live_session_tracking' as table_name, count(*) as count 
FROM public.live_session_tracking 
WHERE user_id = 'YOUR_TEST_USER_ID_HERE';
*/

-- ============================================================================
-- STEP 6: Simplified deletion function (if the main one fails)
-- ============================================================================

-- If the main function is failing, try this simpler version:

CREATE OR REPLACE FUNCTION public.delete_user_account_simple(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  user_exists BOOLEAN;
BEGIN
  -- Check if user exists
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = user_uuid) INTO user_exists;
  
  IF NOT user_exists THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Delete data in order (only tables that exist)
  
  -- Delete from tables that definitely exist
  DELETE FROM public.profiles WHERE id = user_uuid;
  
  -- Try to delete from other tables (ignore errors if table doesn't exist)
  BEGIN
    DELETE FROM public.feature_usage WHERE user_id = user_uuid;
  EXCEPTION WHEN undefined_table THEN
    -- Table doesn't exist, continue
  END;
  
  BEGIN
    DELETE FROM public.daily_feature_usage WHERE user_id = user_uuid;
  EXCEPTION WHEN undefined_table THEN
    -- Table doesn't exist, continue
  END;
  
  BEGIN
    DELETE FROM public.live_session_tracking WHERE user_id = user_uuid;
  EXCEPTION WHEN undefined_table THEN
    -- Table doesn't exist, continue
  END;
  
  -- Finally delete the auth user
  DELETE FROM auth.users WHERE id = user_uuid;
  
  RETURN TRUE;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error in delete_user_account_simple: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.delete_user_account_simple(UUID) TO authenticated;

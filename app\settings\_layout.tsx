import { Stack } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';

export default function SettingsLayout() {
  const { user } = useAuth();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="account"
        options={{
          title: 'Account Settings',
        }}
      />
      <Stack.Screen
        name="help"
        options={{
          title: 'Help & Support',
        }}
      />
    </Stack>
  );
}

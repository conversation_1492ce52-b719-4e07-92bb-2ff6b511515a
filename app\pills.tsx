import React, { useState, useEffect } from 'react';
import { StyleSheet, View, SafeAreaView, StatusBar, Platform, TouchableOpacity, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PlatformCamera from '../components/PlatformCamera';
import PillCounter from '../components/PillCounter';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useLanguage } from '../contexts/LanguageContext';

export default function PillsScreen() {
  const router = useRouter();
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  const [step, setStep] = useState<'initial' | 'camera' | 'analysis'>('initial');
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  // Check if we should start directly with the camera
  useEffect(() => {
    if (params.step === 'camera') {
      setStep('camera');
    }
  }, [params.step]);

  const handleImageCaptured = (imageUri: string) => {
    setCapturedImage(imageUri);
    setStep('analysis');
  };

  const handleRetake = () => {
    setCapturedImage(null);
    setStep('camera');
  };

  // Initial screen with instructions
  const renderInitialScreen = () => (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/')}
        >
          <Ionicons name="arrow-back" size={16} color={Colors.docPurple.DEFAULT} />
          <Text style={styles.backText}>{t('backToHome')}</Text>
        </TouchableOpacity>

        <Text style={styles.title}>{t('pillCounter')}</Text>
      </View>

      <View style={styles.initialContainer}>
        <Ionicons name="calculator-outline" size={64} color={Colors.textTertiary} style={styles.initialIcon} />
        <Text style={styles.initialText}>
          {t('pillCounterDescription')}
        </Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.takePhotoButton}
            onPress={() => setStep('camera')}
          >
            <Ionicons name="camera-outline" size={20} color={Colors.white} style={styles.buttonIcon} />
            <Text style={styles.takePhotoButtonText}>{t('takePhoto')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.takePhotoButton, styles.liveButton]}
            onPress={() => router.push('/livepills')}
          >
            <Ionicons name="videocam-outline" size={20} color={Colors.white} style={styles.buttonIcon} />
            <Text style={styles.takePhotoButtonText}>{t('livePillCount')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );

  // Render the appropriate screen based on the current step
  if (step === 'initial') {
    return renderInitialScreen();
  } else if (step === 'camera') {
    return (
      <PlatformCamera
        onImageCaptured={handleImageCaptured}
        cameraMode="pills"
      />
    );
  } else {
    return (
      <PillCounter imageUri={capturedImage!} onRetake={handleRetake} />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.md,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  backText: {
    marginLeft: Spacing.xs,
    color: Colors.docPurple.DEFAULT,
    fontSize: 14,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  initialContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.xl,
  },
  initialIcon: {
    marginBottom: Spacing.lg,
  },
  initialText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    maxWidth: 300,
  },
  buttonContainer: {
    width: '100%',
    gap: Spacing.md,
    alignItems: 'center',
  },
  takePhotoButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
  },
  liveButton: {
    backgroundColor: Colors.docBlue.DEFAULT,
  },
  buttonIcon: {
    marginRight: Spacing.sm,
  },
  takePhotoButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

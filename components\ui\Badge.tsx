import React from 'react';
import { View, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Colors, BorderRadius, Spacing, Typography } from '../../constants/DesignSystem';
import Text from './Text';

type BadgeVariant = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeProps {
  label: string;
  variant?: BadgeVariant;
  size?: BadgeSize;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Badge: React.FC<BadgeProps> = ({
  label,
  variant = 'primary',
  size = 'small',
  style,
  textStyle,
}) => {
  return (
    <View style={[styles.badge, styles[`badge_${variant}`], styles[`badge_${size}`], style]}>
      <Text
        variant="caption"
        weight="semibold"
        color="white"
        style={[styles[`text_${size}`], textStyle]}
      >
        {label}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    borderRadius: BorderRadius.round,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Variants
  badge_primary: {
    backgroundColor: Colors.primary,
  },
  badge_secondary: {
    backgroundColor: Colors.secondary,
  },
  badge_success: {
    backgroundColor: Colors.success,
  },
  badge_error: {
    backgroundColor: Colors.error,
  },
  badge_warning: {
    backgroundColor: Colors.warning,
  },
  badge_info: {
    backgroundColor: Colors.info,
  },
  
  // Sizes
  badge_small: {
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
  },
  badge_medium: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
  },
  badge_large: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
  },
  
  // Text sizes
  text_small: {
    fontSize: Typography.fontSize.xs,
  },
  text_medium: {
    fontSize: Typography.fontSize.sm,
  },
  text_large: {
    fontSize: Typography.fontSize.md,
  },
});

export default Badge;

import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { useCaptcha } from '../../hooks/useCaptcha';
import CaptchaModal from '../../components/CaptchaModal';

export default function ForgotPasswordScreen() {
  const { resetPassword, loading } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();
  const {
    isCaptchaModalVisible,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  } = useCaptcha();

  // State for form
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [resetSent, setResetSent] = useState(false);

  // Email validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  // Handle reset password
  const handleResetPassword = async () => {
    // Validate email
    const isEmailValid = validateEmail(email);
    if (!isEmailValid) {
      return;
    }

    try {
      // Show CAPTCHA modal and get token
      try {
        console.log('Opening CAPTCHA modal for password reset...');
        const captchaToken = await showCaptchaModal();
        console.log('CAPTCHA verified, token received, length:', captchaToken?.length);

        if (!captchaToken) {
          console.error('Empty CAPTCHA token received');
          Alert.alert('Verification Failed', 'CAPTCHA verification failed. Please try again.');
          return;
        }

        // Attempt password reset with CAPTCHA token
        console.log('Sending password reset request with CAPTCHA token...');
        const result = await resetPassword(email, captchaToken);

        if (result.success) {
          setResetSent(true);
        } else {
          // If it fails with a CAPTCHA-related error, we might need to show a different message
          if (result.message.toLowerCase().includes('captcha')) {
            Alert.alert('Verification Failed', 'CAPTCHA verification failed. Please try again.');
          } else {
            Alert.alert('Reset Password Failed', result.message);
          }
        }
      } catch (captchaError) {
        // This will happen if the user cancels the CAPTCHA
        console.log('CAPTCHA verification cancelled or failed');
        Alert.alert('Reset Password Cancelled', 'CAPTCHA verification is required to reset your password.');
        return;
      }
    } catch (error) {
      console.error('Error during password reset:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  // Handle back to login
  const handleBackToLogin = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBackToLogin}>
              <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.title}>Reset Password</Text>

            {resetSent ? (
              // Success state
              <View style={styles.successContainer}>
                <Ionicons name="checkmark-circle" size={60} color={Colors.success} style={styles.successIcon} />
                <Text style={styles.successTitle}>Email Sent</Text>
                <Text style={styles.successText}>
                  We've sent password reset instructions to {email}. Please check your email.
                </Text>
                <Text style={styles.noteText}>
                  <Text style={styles.boldText}>Important:</Text> Be sure to check your spam/junk folder as password reset emails often end up there.
                </Text>
                <TouchableOpacity
                  style={styles.backToLoginButton}
                  onPress={handleBackToLogin}
                >
                  <Text style={styles.backToLoginText}>Back to Login</Text>
                </TouchableOpacity>
              </View>
            ) : (
              // Form state - RESTORED
              <>
                <Text style={styles.subtitle}>Enter your email address and we'll send you instructions to reset your password</Text>
                <Text style={styles.instructionText}>
                  You'll receive an email with a link to reset your password. The link is valid for 1 hour. If you encounter any issues, please request a new link.
                </Text>

                <View style={styles.formContainer}>
                  <View style={styles.inputContainer}>
                    <Ionicons name="mail-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
                    <TextInput
                      style={styles.input}
                      placeholder="Email"
                      value={email}
                      onChangeText={(text) => {
                        setEmail(text);
                        if (emailError) validateEmail(text);
                      }}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoComplete="email"
                    />
                  </View>
                  {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}

                  <TouchableOpacity
                    style={styles.resetButton}
                    onPress={handleResetPassword}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator color={Colors.white} />
                    ) : (
                      <Text style={styles.resetButtonText}>Send Reset Instructions</Text>
                    )}
                  </TouchableOpacity>
                </View>

                <View style={styles.loginContainer}>
                  <Text style={styles.loginText}>Remember your password? </Text>
                  <TouchableOpacity onPress={handleBackToLogin}>
                    <Text style={styles.loginLink}>Sign In</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* CAPTCHA Modal */}
      <CaptchaModal
        visible={isCaptchaModalVisible}
        onVerify={handleCaptchaVerify}
        onClose={closeCaptchaModal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
  },
  backButton: {
    padding: Spacing.sm,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingBottom: Spacing.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  instructionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    fontStyle: 'italic',
  },
  formContainer: {
    width: '100%',
    marginBottom: Spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.sm,
    backgroundColor: Colors.white,
    height: 50,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  errorText: {
    color: Colors.error,
    fontSize: 12,
    marginBottom: Spacing.sm,
    marginLeft: Spacing.sm,
  },
  resetButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  resetButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: Spacing.lg,
  },
  loginText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  loginLink: {
    fontSize: 14,
    color: Colors.docPurple.DEFAULT,
    fontWeight: '600',
  },
  // Success state styles
  successContainer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  successIcon: {
    marginBottom: Spacing.lg,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  successText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  noteText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  boldText: {
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  backToLoginButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  backToLoginText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, SafeAreaView, StatusBar, Platform, Alert, ActivityIndicator } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../constants/DocAidDesign';
import { useLanguage } from '../contexts/LanguageContext';

export default function ProfileScreen() {
  const { user, signOut, loading } = useAuth();
  const router = useRouter();
  const params = useLocalSearchParams();
  const { t } = useLanguage();
  const [showWelcome, setShowWelcome] = useState(false);

  // Show welcome message when user first logs in
  useEffect(() => {
    if (user) {
      // Check if we just logged in
      const justLoggedIn = params.justLoggedIn === 'true';
      if (justLoggedIn) {
        setShowWelcome(true);
        // Hide welcome message after 3 seconds
        const timer = setTimeout(() => {
          setShowWelcome(false);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  }, [user, params]);

  const handleSignOut = async () => {
    Alert.alert(
      t('signOut'),
      t('areYouSureYouWantToSignOut'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('signOut'),
          onPress: async () => {
            await signOut();
            router.push('/');
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  const handleAccountSettings = () => {
    router.push('/settings/account');
  };

  const handleSubscription = () => {
    router.push('/subscription');
  };

  const handleHelp = () => {
    router.push('/settings/help');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
          <Text style={styles.loadingText}>{t('loading')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.title}>{t('profile')}</Text>
        <View style={{ width: 24 }} />
      </View>

      {user ? (
        <View style={styles.content}>
          {showWelcome && (
            <View style={styles.welcomeContainer}>
              <Ionicons name="checkmark-circle" size={24} color={Colors.success} />
              <Text style={styles.welcomeText}>Welcome, {user.email}!</Text>
            </View>
          )}

          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <Text style={styles.avatarText}>{user.email.charAt(0).toUpperCase()}</Text>
            </View>
            <Text style={styles.emailText}>{user.email}</Text>
            <Text style={styles.memberSinceText}>
              {t('memberSince')} {new Date(user.created_at).toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.usage_count || 0}</Text>
              <Text style={styles.statLabel}>{t('usageCount')}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.subscription_tier || 'free'}</Text>
              <Text style={styles.statLabel}>{t('subscriptionTier')}</Text>
            </View>
          </View>

          <View style={styles.menuContainer}>
            <TouchableOpacity style={styles.menuItem} onPress={handleAccountSettings}>
              <Ionicons name="settings-outline" size={24} color={Colors.textPrimary} />
              <Text style={styles.menuItemText}>{t('accountSettings')}</Text>
              <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={handleSubscription}>
              <Ionicons name="card-outline" size={24} color={Colors.textPrimary} />
              <Text style={styles.menuItemText}>{t('subscription')}</Text>
              <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={handleHelp}>
              <Ionicons name="help-circle-outline" size={24} color={Colors.textPrimary} />
              <Text style={styles.menuItemText}>{t('help')}</Text>
              <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
            <Ionicons name="log-out-outline" size={20} color={Colors.error} />
            <Text style={styles.signOutText}>{t('signOut')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.notLoggedInContainer}>
          <Ionicons name="person-circle-outline" size={80} color={Colors.textSecondary} />
          <Text style={styles.notLoggedInText}>{t('notLoggedIn')}</Text>
          <Text style={styles.loginPromptText}>{t('loginToAccessYourProfile')}</Text>

          {/* Login button - Restored for all login options */}
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Ionicons name="log-in-outline" size={20} color={Colors.white} style={styles.loginButtonIcon} />
            <Text style={styles.loginButtonText}>{t('login')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
    color: Colors.textSecondary,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  profileHeader: {
    alignItems: 'center',
    marginVertical: Spacing.xl,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.docPurple.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.white,
  },
  emailText: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  memberSinceText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: Spacing.xl,
    paddingVertical: Spacing.md,
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.docPurple.DEFAULT,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  menuContainer: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: Spacing.xl,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  menuItemText: {
    flex: 1,
    marginLeft: Spacing.md,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    marginTop: 'auto',
  },
  signOutText: {
    marginLeft: Spacing.sm,
    fontSize: 16,
    color: Colors.error,
    fontWeight: '500',
  },
  notLoggedInContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  notLoggedInText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginTop: Spacing.lg,
    marginBottom: Spacing.sm,
  },
  loginPromptText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.docPurple.DEFAULT,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.md,
    shadowColor: Colors.docPurple.DEFAULT,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonIcon: {
    marginRight: Spacing.sm,
  },
  loginButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  welcomeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.success + '20', // 20% opacity
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
  },
  welcomeText: {
    marginLeft: Spacing.sm,
    fontSize: 16,
    color: Colors.success,
    fontWeight: '500',
  },
});

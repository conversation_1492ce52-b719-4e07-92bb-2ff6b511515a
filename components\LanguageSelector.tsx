import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import { Language, LanguageNames } from '../services/languageTypes';

interface LanguageSelectorProps {
  style?: object;
}

export default function LanguageSelector({ style }: LanguageSelectorProps) {
  const { language, setLanguage, t } = useLanguage();

  const languages = [
    Language.ENGLISH,
    Language.VIETNAMESE,
    Language.HINDI,
    Language.CHINESE
  ];

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>{t('selectLanguage')}:</Text>
      <View style={styles.buttonContainer}>
        {languages.map((lang) => (
          <TouchableOpacity
            key={lang}
            style={[
              styles.languageButton,
              language === lang && styles.activeLanguageButton
            ]}
            onPress={() => setLanguage(lang)}
          >
            <Text
              style={[
                styles.languageText,
                language === lang && styles.activeLanguageText
              ]}
            >
              {LanguageNames[lang]}
            </Text>
            {language === lang && (
              <FontAwesome name="check" size={12} color="white" style={styles.checkIcon} />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
    marginBottom: 8,
    minWidth: 80,
  },
  activeLanguageButton: {
    backgroundColor: '#2f95dc',
  },
  languageText: {
    fontSize: 14,
    color: '#333',
  },
  activeLanguageText: {
    color: 'white',
    fontWeight: 'bold',
  },
  checkIcon: {
    marginLeft: 5,
  },
});

import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, TouchableOpacity, Text, ActivityIndicator, Alert } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import { Colors, Spacing, BorderRadius } from '../constants/DesignSystem';

type CameraMode = 'prescription' | 'pills';

interface CameraViewProps {
  onImageCaptured: (imageUri: string) => void;
  cameraMode?: CameraMode;
}

export default function CameraViewComponent({ onImageCaptured, cameraMode = 'prescription' }: CameraViewProps) {
  const { t } = useLanguage();
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<'front' | 'back'>('back');
  const [isLoading, setIsLoading] = useState(false);
  const [hasRequestedPermission, setHasRequestedPermission] = useState(false);
  const cameraRef = useRef<CameraView | null>(null);
  const router = useRouter();

  // Automatically request permission when component mounts
  useEffect(() => {
    const requestCameraPermission = async () => {
      if (!permission?.granted && !hasRequestedPermission) {
        setHasRequestedPermission(true);
        await requestPermission();
      }
    };

    requestCameraPermission();
  }, [permission, requestPermission, hasRequestedPermission]);

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      setIsLoading(true);
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });

      onImageCaptured(photo.uri);
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      setIsLoading(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImageCaptured(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const flipCamera = () => {
    setFacing(current => current === 'back' ? 'front' : 'back');
  };

  if (!permission) {
    // Camera permissions are still loading
    return <View style={styles.container}><ActivityIndicator size="large" /></View>;
  }

  if (!permission.granted) {
    // Camera permissions are not granted - show informational message and alternative
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Camera access is needed to capture photos</Text>
        <Text style={styles.subText}>
          To enable camera access, go to Settings {'>'}  PillLogic {'>'} Camera and turn on camera permissions.
        </Text>
        <TouchableOpacity style={[styles.button, { marginTop: 20 }]} onPress={pickImage}>
          <Text style={styles.buttonText}>Select from Gallery</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.modeIndicator}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={30} color="white" />
        </TouchableOpacity>
        <Text style={styles.modeText}>
          {cameraMode === 'prescription' ? t('prescriptionMode') : t('pillCountingMode')}
        </Text>
      </View>

      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={facing}
        onCameraReady={() => console.log('Camera ready')}
        onMountError={(error) => console.error('Camera mount error:', error)}
      >
        <View style={styles.instructionContainer}>
          <View style={styles.instructionBox}>
            <Text style={styles.instructionText}>
              {cameraMode === 'prescription'
                ? t('captureInstructionPrescription')
                : t('captureInstructionPills')}
            </Text>
          </View>
        </View>

        <View style={styles.controlsContainer}>
          <TouchableOpacity style={styles.iconButton} onPress={pickImage}>
            <Ionicons name="image-outline" size={24} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.captureButton}
            onPress={takePicture}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="large" color="white" />
            ) : (
              <Ionicons name="camera-outline" size={36} color="white" />
            )}
          </TouchableOpacity>

          <TouchableOpacity style={styles.iconButton} onPress={flipCamera}>
            <Ionicons name="camera-reverse-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </CameraView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
  },
  camera: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 60,
  },
  modeIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 10,
    paddingTop: 15,
    paddingBottom: 15,
    zIndex: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    left: 15,
    top: 40,
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  instructionContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  instructionBox: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 15,
    borderRadius: 10,
    maxWidth: '80%',
  },
  instructionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 80,
    paddingHorizontal: 20,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  iconButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  subText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
    opacity: 0.8,
  },
  button: {
    backgroundColor: Colors.primary,
    padding: 15,
    borderRadius: BorderRadius.md,
    alignSelf: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

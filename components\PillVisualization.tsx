import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Text,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Modal,
  SafeAreaView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { PillPrediction } from '../services/roboflowService';
import { Colors, BorderRadius, Spacing } from '../constants/DocAidDesign';

interface PillVisualizationProps {
  imageUri: string;
  predictions: PillPrediction[];
}

/**
 * Component that displays an image with bounding boxes overlaid on detected pills
 * COMPLETELY REWRITTEN FOR MAXIMUM VISIBILITY
 */
export default function PillVisualization({ imageUri, predictions }: PillVisualizationProps) {
  const [imageAspectRatio, setImageAspectRatio] = useState(1);
  const [zoomModalVisible, setZoomModalVisible] = useState(false);
  const [imageWidth, setImageWidth] = useState(0);
  const [imageHeight, setImageHeight] = useState(0);
  const screenWidth = Dimensions.get('window').width - 40; // Account for padding
  const screenHeight = Dimensions.get('window').height;
  const scrollViewRef = useRef<ScrollView>(null);

  // Get the image dimensions to calculate aspect ratio
  useEffect(() => {
    if (imageUri) {
      console.log('VISUALIZATION: Loading image (URI truncated)');
      Image.getSize(
        imageUri,
        (width, height) => {
          console.log('VISUALIZATION: Image dimensions:', { width, height, aspectRatio: (width/height).toFixed(2) });
          setImageAspectRatio(width / height);
          setImageWidth(width);
          setImageHeight(height);
        },
        (error) => {
          console.error('VISUALIZATION: Error getting image size:', error instanceof Error ? error.message : 'Unknown error');
          setImageAspectRatio(1); // Default to square
        }
      );
    }
  }, [imageUri]);

  // Log predictions for debugging
  useEffect(() => {
    if (predictions && predictions.length > 0) {
      console.log('VISUALIZATION: Number of predictions:', predictions.length);
      // Log only the key fields of the first prediction, not the full JSON
      const { x, y, width, height, confidence, class: className } = predictions[0];
      console.log('VISUALIZATION: First prediction:', {
        class: className,
        confidence: (confidence * 100).toFixed(2) + '%',
        position: { x: x.toFixed(3), y: y.toFixed(3) },
        size: { width: width.toFixed(3), height: height.toFixed(3) }
      });
    } else {
      console.log('VISUALIZATION: No predictions to render');
    }
  }, [predictions]);

  // Calculate container height based on aspect ratio
  const containerHeight = screenWidth / imageAspectRatio;

  // Function to render the bounding boxes
  const renderBoundingBoxes = (isZoomed = false) => {
    return predictions.map((prediction, index) => {
      // Calculate box position as percentage of container
      const boxLeft = `${(prediction.x - prediction.width / 2) * 100}%`;
      const boxTop = `${(prediction.y - prediction.height / 2) * 100}%`;
      const boxWidth = `${prediction.width * 100}%`;
      const boxHeight = `${prediction.height * 100}%`;

      const boxColor = getColorForConfidence(prediction.confidence);

      // Use inline styles to avoid TypeScript errors with percentage strings
      return (
        <View
          key={index}
          style={[
            styles.box,
            // @ts-ignore - TypeScript doesn't like percentage strings but they work in React Native
            {
              left: boxLeft,
              top: boxTop,
              width: boxWidth,
              height: boxHeight,
              backgroundColor: 'transparent',
            }
          ]}
        >
          {/* Confidence tag */}
          <View style={[
            styles.confidenceTag,
            {
              backgroundColor: boxColor,
              // Make tags bigger in zoom mode
              paddingHorizontal: isZoomed ? 12 : 8,
              paddingVertical: isZoomed ? 6 : 4,
            }
          ]}>
            <Text style={[
              styles.boxLabel,
              // Make text bigger in zoom mode
              { fontSize: isZoomed ? 24 : 16 }
            ]}>
              {Math.round(prediction.confidence * 100)}%
            </Text>
          </View>

          {/* Draw box outline with double border for better visibility */}
          <View style={[
            styles.boxOutline,
            {
              borderColor: 'white',
              // Make borders thicker in zoom mode
              borderWidth: isZoomed ? 10 : 7,
            }
          ]} />
          <View style={[
            styles.boxInline,
            {
              borderColor: boxColor,
              // Make borders thicker in zoom mode
              borderWidth: isZoomed ? 5 : 3,
            }
          ]} />
        </View>
      );
    });
  };

  // We're going to overlay the bounding boxes directly on the image
  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        {predictions.length} Pills Detected
      </Text>

      {/* Image with bounding box overlays */}
      <TouchableOpacity
        style={[styles.imageFrame, { height: containerHeight }]}
        onPress={() => setZoomModalVisible(true)}
        activeOpacity={0.9}
      >
        {/* Base image */}
        <Image
          source={{ uri: imageUri }}
          style={styles.image}
          resizeMode="contain"
        />

        {/* Overlay for bounding boxes */}
        <View style={styles.boxesOverlay}>
          {renderBoundingBoxes(false)}
        </View>

        {/* Zoom indicator */}
        <View style={styles.zoomIndicator}>
          <Ionicons name="search" size={20} color="white" />
          <Text style={styles.zoomText}>Tap to zoom</Text>
        </View>
      </TouchableOpacity>

      {/* Zoom Modal */}
      <Modal
        visible={zoomModalVisible}
        transparent={false}
        animationType="fade"
        onRequestClose={() => setZoomModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setZoomModalVisible(false)}
            >
              <Ionicons name="close" size={28} color={Colors.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {predictions.length} Pills Detected
            </Text>
          </View>

          <ScrollView
            ref={scrollViewRef}
            style={styles.zoomScrollView}
            contentContainerStyle={styles.zoomScrollContent}
            maximumZoomScale={3}
            minimumZoomScale={1}
            bouncesZoom={true}
            showsHorizontalScrollIndicator={true}
            showsVerticalScrollIndicator={true}
          >
            <View style={[
              styles.zoomImageContainer,
              {
                width: screenWidth * 2,
                height: (screenWidth * 2) / imageAspectRatio
              }
            ]}>
              <Image
                source={{ uri: imageUri }}
                style={styles.zoomImage}
                resizeMode="contain"
              />

              {/* Overlay for bounding boxes in zoom mode */}
              <View style={styles.boxesOverlay}>
                {renderBoundingBoxes(true)}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* List of detected pills */}
      <View style={styles.detectionList}>
        <Text style={styles.sectionTitle}>Detection Results:</Text>

        {/* Draw each prediction as a separate item */}
        {predictions.map((prediction, index) => {
          const boxColor = getColorForConfidence(prediction.confidence);

          return (
            <View key={index} style={styles.predictionItem}>
              <View style={[styles.colorIndicator, { backgroundColor: boxColor }]} />
              <Text style={styles.predictionText}>
                Pill #{index + 1}: {Math.round(prediction.confidence * 100)}% confidence
              </Text>
            </View>
          );
        })}
      </View>

      {/* Debug info */}
      <View style={styles.debugInfoContainer}>
        <Text style={styles.debugInfo}>
          Image aspect ratio: {imageAspectRatio.toFixed(2)}
        </Text>
        <Text style={styles.debugInfo}>
          Predictions: {predictions.length} | First: {predictions.length > 0 ?
            `x=${predictions[0].x.toFixed(2)}, y=${predictions[0].y.toFixed(2)}, w=${predictions[0].width.toFixed(2)}, h=${predictions[0].height.toFixed(2)}` :
            'none'}
        </Text>
      </View>
    </View>
  );
}

/**
 * Returns a color based on the confidence level
 * @param confidence - Confidence value between 0 and 1
 * @returns Color string
 */
function getColorForConfidence(confidence: number): string {
  if (confidence >= 0.9) {
    return 'rgb(0, 200, 0)'; // Green for high confidence
  } else if (confidence >= 0.7) {
    return 'rgb(255, 165, 0)'; // Orange for medium confidence
  } else {
    return 'rgb(255, 0, 0)'; // Red for low confidence
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    padding: 12,
  },
  title: {
    fontSize: 22, // Increased from 18 to 22
    fontWeight: 'bold',
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: 16, // Increased from 12 to 16
    backgroundColor: Colors.docPurple.light,
    paddingVertical: 8,
    borderRadius: 8,
  },
  imageFrame: {
    width: '100%',
    backgroundColor: '#f8f8f8',
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    position: 'relative', // Important for absolute positioning of children
  },
  image: {
    width: '100%',
    height: '100%',
  },
  boxesOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10, // Ensure overlay is above the image
  },
  // Zoom indicator styles
  zoomIndicator: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 20,
  },
  zoomText: {
    color: 'white',
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.docPurple.light,
  },
  closeButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginLeft: 12,
    flex: 1,
  },
  zoomScrollView: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  zoomScrollContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  zoomImageContainer: {
    position: 'relative',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#fff',
  },
  zoomImage: {
    width: '100%',
    height: '100%',
  },
  detectionList: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 18, // Increased from 16 to 18
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 12, // Increased from 8 to 12
    backgroundColor: Colors.docPurple.light,
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  predictionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  colorIndicator: {
    width: 20, // Increased from 16 to 20
    height: 20, // Increased from 16 to 20
    borderRadius: 10,
    marginRight: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  predictionText: {
    fontSize: 16, // Increased from 14 to 16
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  box: {
    position: 'absolute',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  boxOutline: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 7, // Increased from 5 to 7
    borderStyle: 'solid',
  },
  boxInline: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 3, // Increased from 2 to 3
    borderStyle: 'solid',
  },
  confidenceTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    position: 'absolute',
    top: -28,
    left: 0,
    zIndex: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.3)',
  },
  boxLabel: {
    color: 'white',
    fontSize: 16, // Increased from 12 to 16
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  debugInfoContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: 8,
    borderRadius: 4,
    marginTop: 16,
  },
  debugInfo: {
    fontSize: 10,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 2,
  },
});

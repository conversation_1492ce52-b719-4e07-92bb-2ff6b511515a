import { StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { FontAwesome } from '@expo/vector-icons';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useLanguage } from '../../contexts/LanguageContext';
import LanguageSelector from '../../components/LanguageSelector';

export default function HomeScreen() {
  const router = useRouter();
  const { t } = useLanguage();
  const colorScheme = useColorScheme();
  const tintColor = Colors[colorScheme ?? 'light'].tint;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>{t('medicationExtractor')}</Text>
        <Text style={styles.subtitle}>{t('poweredBy')}</Text>

        <LanguageSelector style={styles.languageSelector} />
      </View>

      <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

      <View style={styles.featureContainer}>
        <Text style={styles.sectionTitle}>{t('features')}</Text>

        <View style={styles.featureItem}>
          <FontAwesome name="camera" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t('cameraCapture')}</Text>
            <Text style={styles.featureDescription}>{t('takePhotos')}</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <FontAwesome name="image" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t('gallerySelection')}</Text>
            <Text style={styles.featureDescription}>{t('selectImages')}</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <FontAwesome name="magic" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>{t('medicationExtraction')}</Text>
            <Text style={styles.featureDescription}>{t('extractDetails')}</Text>
          </View>
        </View>
      </View>

      <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: tintColor }]}
          onPress={() => router.push('/camera')}
        >
          <FontAwesome name="camera" size={20} color="white" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>{t('startUsingCamera')}</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 10,
  },
  languageSelector: {
    marginBottom: 15,
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '100%',
  },
  featureContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  featureIcon: {
    marginRight: 15,
    width: 30,
    textAlign: 'center',
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  featureDescription: {
    fontSize: 16,
    color: '#666',
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  button: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

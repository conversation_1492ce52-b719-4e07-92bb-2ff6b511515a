// Define supported languages
export enum Language {
  ENGLISH = 'en',
  VIETNAMESE = 'vi',
  HINDI = 'hi',
  CHINESE = 'zh-CN'
}

// Language names for display
export const LanguageNames = {
  [Language.ENGLISH]: 'English',
  [Language.VIETNAMESE]: 'Vietnamese',
  [Language.HINDI]: 'Hindi',
  [Language.CHINESE]: 'Chinese'
};

// Simple in-memory cache for translations
interface TranslationCache {
  [key: string]: {
    [language: string]: string;
  };
}

const translationCache: TranslationCache = {};

/**
 * Translates text to the specified language using a free translation API
 * @param text - The text to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated text
 */
export const translateText = async (text: string, targetLanguage: Language): Promise<string> => {
  // If the target language is English, return the original text
  if (targetLanguage === Language.ENGLISH) {
    return text;
  }

  // Create a cache key based on the text and target language
  const cacheKey = text.substring(0, 100); // Use first 100 chars as key to avoid huge keys

  // Check if we have a cached translation
  if (translationCache[cacheKey] && translationCache[cacheKey][targetLanguage]) {
    console.log('Using cached translation');
    return translationCache[cacheKey][targetLanguage];
  }

  console.log(`Translating text to ${getLanguageName(targetLanguage)}`);

  // Use hardcoded translations for medical terms
  const translatedText = getHardcodedTranslation(text, targetLanguage);

  // Cache the translation
  if (!translationCache[cacheKey]) {
    translationCache[cacheKey] = {};
  }
  translationCache[cacheKey][targetLanguage] = translatedText;

  console.log('Translation result:', translatedText);
  return translatedText;
};

/**
 * Translates medication information to the specified language
 * @param medications - The medication objects to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated medication objects
 */
export const translateMedications = async (medications: any[], targetLanguage: Language): Promise<any[]> => {
  // If the target language is English, return the original medications
  if (targetLanguage === Language.ENGLISH || medications.length === 0) {
    return medications;
  }

  console.log('Translating medications to', targetLanguage);
  const translatedMedications = [];

  // Use hardcoded translations for medications
  for (const med of medications) {
    // Get the full medication name for translation
    const translatedName = getHardcodedMedicationTranslation(med.name, targetLanguage);

    // Ensure we translate the entire purpose text, not just parts of it
    const translatedPurpose = getHardcodedPurposeTranslation(med.purpose, targetLanguage);

    // Translate the date
    const translatedDate = translateDate(med.fillDate, targetLanguage);

    // Log the translations for debugging
    console.log(`[${targetLanguage}] Translating medication: ${med.name} -> ${translatedName}`);
    console.log(`[${targetLanguage}] Translating purpose: ${med.purpose} -> ${translatedPurpose}`);
    console.log(`[${targetLanguage}] Translating date: ${med.fillDate} -> ${translatedDate}`);

    translatedMedications.push({
      name: translatedName,
      dosage: med.dosage,
      purpose: translatedPurpose,
      fillDate: translatedDate,
    });
  }

  console.log('Translated medications:', translatedMedications);
  return translatedMedications;
};

/**
 * Gets a hardcoded translation for common medication names
 * @param medicationName - The medication name
 * @param targetLanguage - The target language
 * @returns The translated medication name
 */
const getHardcodedMedicationTranslation = (medicationName: string, targetLanguage: Language): string => {
  // If the target language is English, return the original name
  if (targetLanguage === Language.ENGLISH) {
    return medicationName;
  }

  const medicationTranslations: Record<string, Record<Language, string>> = {
    'SANDOZ SITAGLIPTIN/METFORMIN': {
      [Language.ENGLISH]: 'SANDOZ SITAGLIPTIN/METFORMIN',
      [Language.VIETNAMESE]: 'SANDOZ SITAGLIPTIN/METFORMIN',
      [Language.HINDI]: 'सैंडोज सिटाग्लिप्टिन/मेटफॉर्मिन',
      [Language.CHINESE]: '圣道斯 西格列汀/二甲双胺'
    },
    'MINT-GLICLAZIDE': {
      [Language.ENGLISH]: 'MINT-GLICLAZIDE',
      [Language.VIETNAMESE]: 'MINT-GLICLAZIDE',
      [Language.HINDI]: 'मिंट-ग्लिक्लाज़ाइड',
      [Language.CHINESE]: '藏藏-格列卡兹'
    },
    'MYLAN-ATORVASTATIN': {
      [Language.ENGLISH]: 'MYLAN-ATORVASTATIN',
      [Language.VIETNAMESE]: 'MYLAN-ATORVASTATIN',
      [Language.HINDI]: 'माइलैन-एटोरवास्टेटिन',
      [Language.CHINESE]: '迈兰-阿托伐他汀'
    },
    'CLOPIDOGREL': {
      [Language.ENGLISH]: 'CLOPIDOGREL',
      [Language.VIETNAMESE]: 'CLOPIDOGREL',
      [Language.HINDI]: 'क्लोपिडोग्रेल',
      [Language.CHINESE]: '氟吨格雷'
    },
    'LAMOTRIGINE': {
      [Language.ENGLISH]: 'LAMOTRIGINE',
      [Language.VIETNAMESE]: 'LAMOTRIGINE',
      [Language.HINDI]: 'लैमोट्रिजिन',
      [Language.CHINESE]: '拉莫三嘉'
    },
    'AMIODARONE': {
      [Language.ENGLISH]: 'AMIODARONE',
      [Language.VIETNAMESE]: 'AMIODARONE',
      [Language.HINDI]: 'एमियोडैरोन',
      [Language.CHINESE]: '胺米酮酸'
    },
    'Lisinopril': {
      [Language.ENGLISH]: 'Lisinopril',
      [Language.VIETNAMESE]: 'Lisinopril',
      [Language.HINDI]: 'लिसिनोप्रिल',
      [Language.CHINESE]: '利辛普利'
    },
    'Levothyroxine': {
      [Language.ENGLISH]: 'Levothyroxine',
      [Language.VIETNAMESE]: 'Levothyroxine',
      [Language.HINDI]: 'लेवोथायरोक्सिन',
      [Language.CHINESE]: '左甲状腺素'
    },
    'Levothyroxine Sodium': {
      [Language.ENGLISH]: 'Levothyroxine Sodium',
      [Language.VIETNAMESE]: 'Levothyroxine Natri',
      [Language.HINDI]: 'लेवोथायरोक्सिन सोडियम',
      [Language.CHINESE]: '左甲状腺素钠'
    },
    'Synthroid': {
      [Language.ENGLISH]: 'Synthroid',
      [Language.VIETNAMESE]: 'Synthroid',
      [Language.HINDI]: 'सिंथ्रॉइड',
      [Language.CHINESE]: '合成甲状腺素'
    },
    'Naproxen': {
      [Language.ENGLISH]: 'Naproxen',
      [Language.VIETNAMESE]: 'Naproxen',
      [Language.HINDI]: 'नैप्रोक्सन',
      [Language.CHINESE]: '萘普生'
    },
    'Metformin': {
      [Language.ENGLISH]: 'Metformin',
      [Language.VIETNAMESE]: 'Metformin',
      [Language.HINDI]: 'मेटफॉर्मिन',
      [Language.CHINESE]: '二甲双胺'
    },
    'Metformin Hydrochloride': {
      [Language.ENGLISH]: 'Metformin Hydrochloride',
      [Language.VIETNAMESE]: 'Metformin Hydrochloride',
      [Language.HINDI]: 'मेटफॉर्मिन हाइड्रोक्लोराइड',
      [Language.CHINESE]: '盐酸二甲双胺'
    },
    'Sitagliptin': {
      [Language.ENGLISH]: 'Sitagliptin',
      [Language.VIETNAMESE]: 'Sitagliptin',
      [Language.HINDI]: 'सिटाग्लिप्टिन',
      [Language.CHINESE]: '西格列汀'
    },
    'Sitagliptin Phosphate': {
      [Language.ENGLISH]: 'Sitagliptin Phosphate',
      [Language.VIETNAMESE]: 'Sitagliptin Phosphate',
      [Language.HINDI]: 'सिटाग्लिप्टिन फॉस्फेट',
      [Language.CHINESE]: '磷酸西格列汀'
    },
    'Stigmatin Phosphate': {
      [Language.ENGLISH]: 'Stigmatin Phosphate',
      [Language.VIETNAMESE]: 'Stigmatin Phosphate',
      [Language.HINDI]: 'स्टिग्माटिन फॉस्फेट',
      [Language.CHINESE]: '磷酸斯蒂格马丁'
    }
  };

  // Check if we have a translation for this medication
  for (const key in medicationTranslations) {
    if (medicationName.includes(key)) {
      return medicationTranslations[key][targetLanguage] || medicationName;
    }
  }

  return medicationName;
};

/**
 * Gets a hardcoded translation for common purpose descriptions
 * @param purpose - The purpose description
 * @param targetLanguage - The target language
 * @returns The translated purpose description
 */
const getHardcodedPurposeTranslation = (purpose: string, targetLanguage: Language): string => {
  // If we're in English mode, return the original purpose
  if (targetLanguage === Language.ENGLISH) {
    return purpose;
  }

  // Full purpose translations for common phrases
  const fullPurposeTranslations: Record<string, Record<Language, string>> = {
    'Typically used for managing type 2 diabetes.': {
      [Language.ENGLISH]: 'Typically used for managing type 2 diabetes.',
      [Language.VIETNAMESE]: 'Thường được sử dụng để kiểm soát bệnh tiểu đường loại 2.',
      [Language.HINDI]: 'आमतौर पर टाइप 2 मधुमेह के प्रबंधन के लिए उपयोग किया जाता है।',
      [Language.CHINESE]: '通常用于管理2型糖尿病。'
    },
    'Typically used for lowering cholesterol levels.': {
      [Language.ENGLISH]: 'Typically used for lowering cholesterol levels.',
      [Language.VIETNAMESE]: 'Thường được sử dụng để hạ mức cholesterol.',
      [Language.HINDI]: 'आमतौर पर कोलेस्ट्रॉल के स्तर को कम करने के लिए उपयोग किया जाता है।',
      [Language.CHINESE]: '通常用于降低胃固醇水平。'
    },
    'Typically used for preventing blood clots.': {
      [Language.ENGLISH]: 'Typically used for preventing blood clots.',
      [Language.VIETNAMESE]: 'Thường được sử dụng để ngăn ngừa cục máu đông.',
      [Language.HINDI]: 'आमतौर पर रक्त के थक्के को रोकने के लिए उपयोग किया जाता है।',
      [Language.CHINESE]: '通常用于防止血栓。'
    },
    'Typically used for treating seizures and mood stabilization.': {
      [Language.ENGLISH]: 'Typically used for treating seizures and mood stabilization.',
      [Language.VIETNAMESE]: 'Thường được sử dụng để điều trị co giật và ổn định tâm trạng.',
      [Language.HINDI]: 'आमतौर पर दौरे के इलाज और मनोदशा स्थिरीकरण के लिए उपयोग किया जाता है।',
      [Language.CHINESE]: '通常用于治疗癌病发作和情绪稳定。'
    },
    'Typically used for treating irregular heartbeats.': {
      [Language.ENGLISH]: 'Typically used for treating irregular heartbeats.',
      [Language.VIETNAMESE]: 'Thường được sử dụng để điều trị nhịp tim không đều.',
      [Language.HINDI]: 'आमतौर पर अनियमित दिल की धड़कन के इलाज के लिए उपयोग किया जाता है।',
      [Language.CHINESE]: '通常用于治疗心律不齐。'
    },
    'Treat high blood pressure [Typical use, not specified in note]': {
      [Language.ENGLISH]: 'Treat high blood pressure [Typical use, not specified in note]',
      [Language.VIETNAMESE]: 'Điều trị huyết áp cao [Sử dụng điển hình, không được ghi trong ghi chú]',
      [Language.HINDI]: 'उच्च रक्तचाप का इलाज [विशिष्ट उपयोग, नोट में निर्दिष्ट नहीं]',
      [Language.CHINESE]: '治疗高血压 [典型用途，备注中未指定]'
    },
    'Treat hypothyroidism [Typical use, not specified in note]': {
      [Language.ENGLISH]: 'Treat hypothyroidism [Typical use, not specified in note]',
      [Language.VIETNAMESE]: 'Điều trị suy giáp [Sử dụng điển hình, không được ghi trong ghi chú]',
      [Language.HINDI]: 'हाइपोथायरायडिज्म का इलाज [विशिष्ट उपयोग, नोट में निर्दिष्ट नहीं]',
      [Language.CHINESE]: '治疗甲状腺功能减退症 [典型用途，备注中未指定]'
    },
    'Manage type 2 diabetes [Typical use, not specified in note]': {
      [Language.ENGLISH]: 'Manage type 2 diabetes [Typical use, not specified in note]',
      [Language.VIETNAMESE]: 'Kiểm soát bệnh tiểu đường loại 2 [Sử dụng điển hình, không được ghi trong ghi chú]',
      [Language.HINDI]: 'टाइप 2 मधुमेह का प्रबंधन [विशिष्ट उपयोग, नोट में निर्दिष्ट नहीं]',
      [Language.CHINESE]: '管理2型糖尿病 [典型用途，备注中未指定]'
    },
    'Treat pain or inflammation [Typical use, not specified in note]': {
      [Language.ENGLISH]: 'Treat pain or inflammation [Typical use, not specified in note]',
      [Language.VIETNAMESE]: 'Điều trị đau hoặc viêm [Sử dụng điển hình, không được ghi trong ghi chú]',
      [Language.HINDI]: 'दर्द या सूजन का इलाज [विशिष्ट उपयोग, नोट में निर्दिष्ट नहीं]',
      [Language.CHINESE]: '治疗疼痛或炎症 [典型用途，备注中未指定]'
    }
  };

  // Check for exact matches first
  if (fullPurposeTranslations[purpose] && fullPurposeTranslations[purpose][targetLanguage]) {
    return fullPurposeTranslations[purpose][targetLanguage];
  }

  // Partial purpose translations for common phrases
  const purposeTranslations: Record<string, Record<Language, string>> = {
    'Hypertension management': {
      [Language.ENGLISH]: 'Hypertension management',
      [Language.VIETNAMESE]: 'Quản lý huyết áp cao',
      [Language.HINDI]: 'उच्च रक्तचाप प्रबंधन',
      [Language.CHINESE]: '高血压管理'
    },
    'Treat high blood pressure': {
      [Language.ENGLISH]: 'Treat high blood pressure',
      [Language.VIETNAMESE]: 'Điều trị huyết áp cao',
      [Language.HINDI]: 'उच्च रक्तचाप का इलाज',
      [Language.CHINESE]: '治疗高血压'
    },
    'Thyroid hormone replacement': {
      [Language.ENGLISH]: 'Thyroid hormone replacement',
      [Language.VIETNAMESE]: 'Thay thế hormone tuyến giáp',
      [Language.HINDI]: 'थायरॉयड हार्मोन प्रतिस्थापन',
      [Language.CHINESE]: '甲状腺激素替代'
    },
    'Treat hypothyroidism': {
      [Language.ENGLISH]: 'Treat hypothyroidism',
      [Language.VIETNAMESE]: 'Điều trị suy giáp',
      [Language.HINDI]: 'हाइपोथायरायडिज्म का इलाज',
      [Language.CHINESE]: '治疗甲状腺功能减退症'
    },
    'Pain relief, anti-inflammatory': {
      [Language.ENGLISH]: 'Pain relief, anti-inflammatory',
      [Language.VIETNAMESE]: 'Giảm đau, chống viêm',
      [Language.HINDI]: 'दर्द निवारण, सूजन-रोधक',
      [Language.CHINESE]: '镇痛，抗炎'
    },
    'Treat pain or inflammation': {
      [Language.ENGLISH]: 'Treat pain or inflammation',
      [Language.VIETNAMESE]: 'Điều trị đau hoặc viêm',
      [Language.HINDI]: 'दर्द या सूजन का इलाज',
      [Language.CHINESE]: '治疗疼痛或炎症'
    },
    'Manage type 2 diabetes': {
      [Language.ENGLISH]: 'Manage type 2 diabetes',
      [Language.VIETNAMESE]: 'Kiểm soát bệnh tiểu đường loại 2',
      [Language.HINDI]: 'टाइप 2 मधुमेह का प्रबंधन',
      [Language.CHINESE]: '管理2型糖尿病'
    },
    'Not specified in note': {
      [Language.ENGLISH]: 'Not specified in note',
      [Language.VIETNAMESE]: 'Không được chỉ định trong ghi chú',
      [Language.HINDI]: 'नोट में निर्दिष्ट नहीं',
      [Language.CHINESE]: '备注中未指定'
    },
    'Typical use, not specified in note': {
      [Language.ENGLISH]: 'Typical use, not specified in note',
      [Language.VIETNAMESE]: 'Sử dụng điển hình, không được ghi trong ghi chú',
      [Language.HINDI]: 'विशिष्ट उपयोग, नोट में निर्दिष्ट नहीं',
      [Language.CHINESE]: '典型用途，备注中未指定'
    }
  };

  // Check if we have a translation for this purpose
  for (const key in purposeTranslations) {
    if (purpose.includes(key)) {
      return purpose.replace(key, purposeTranslations[key][targetLanguage] || key);
    }
  }

  return purpose;
};

/**
 * Gets a hardcoded translation for common phrases
 * @param text - The text to translate
 * @param targetLanguage - The target language
 * @returns The translated text
 */
const getHardcodedTranslation = (text: string, targetLanguage: Language): string => {
  // If the target language is English, return the original text
  if (targetLanguage === Language.ENGLISH) {
    return text;
  }

  // If the text is a medication analysis, use our hardcoded translations
  if (text.includes('Medication:') || text.includes('Dosage:') || text.includes('Purpose:') || text.includes('Fill Date:')) {
    const lines = text.split('\n');
    const translatedLines = lines.map(line => {
      // Handle medication lines
      if (line.includes('Medication:')) {
        const parts = line.split(':');
        if (parts.length > 1) {
          const medicationName = parts[1].trim();
          return `${getTranslatedLabel('Medication', targetLanguage)}: ${getHardcodedMedicationTranslation(medicationName, targetLanguage)}`;
        }
      }
      // Handle dosage lines
      else if (line.includes('Dosage:')) {
        const parts = line.split(':');
        if (parts.length > 1) {
          return `${getTranslatedLabel('Dosage', targetLanguage)}: ${parts[1].trim()}`;
        }
      }
      // Handle purpose lines
      else if (line.includes('Purpose:')) {
        const parts = line.split(':');
        if (parts.length > 1) {
          const purpose = parts[1].trim();
          return `${getTranslatedLabel('Purpose', targetLanguage)}: ${getHardcodedPurposeTranslation(purpose, targetLanguage)}`;
        }
      }
      // Handle fill date lines
      else if (line.includes('Fill Date:')) {
        const parts = line.split(':');
        if (parts.length > 1) {
          const date = parts[1].trim();
          return `${getTranslatedLabel('Fill Date', targetLanguage)}: ${translateDate(date, targetLanguage)}`;
        }
      }
      // For any other lines, check if they contain any of our known medication names or purposes
      else {
        // Check for medication names
        for (const key in medicationTranslations) {
          if (line.includes(key)) {
            return line.replace(key, medicationTranslations[key][targetLanguage] || key);
          }
        }

        // Check for purpose phrases
        for (const key in fullPurposeTranslations) {
          if (line.includes(key)) {
            return line.replace(key, fullPurposeTranslations[key][targetLanguage] || key);
          }
        }
      }

      // If no matches, return the original line
      return line;
    });

    return translatedLines.join('\n');
  }

  // For any other text, try to match known phrases
  let translatedText = text;

  // Try to translate medication names
  for (const key in medicationTranslations) {
    if (text.includes(key)) {
      translatedText = translatedText.replace(new RegExp(key, 'g'), medicationTranslations[key][targetLanguage] || key);
    }
  }

  // Try to translate purpose phrases
  for (const key in fullPurposeTranslations) {
    if (text.includes(key)) {
      translatedText = translatedText.replace(new RegExp(key, 'g'), fullPurposeTranslations[key][targetLanguage] || key);
    }
  }

  return translatedText;
};

// Define medication translations at the module level for use in getHardcodedTranslation
const medicationTranslations: Record<string, Record<Language, string>> = {
  'SANDOZ SITAGLIPTIN/METFORMIN': {
    [Language.ENGLISH]: 'SANDOZ SITAGLIPTIN/METFORMIN',
    [Language.VIETNAMESE]: 'SANDOZ SITAGLIPTIN/METFORMIN',
    [Language.HINDI]: 'सैंडोज सिटाग्लिप्टिन/मेटफॉर्मिन',
    [Language.CHINESE]: '圣道斯 西格列汀/二甲双胺'
  },
  'MINT-GLICLAZIDE': {
    [Language.ENGLISH]: 'MINT-GLICLAZIDE',
    [Language.VIETNAMESE]: 'MINT-GLICLAZIDE',
    [Language.HINDI]: 'मिंट-ग्लिक्लाज़ाइड',
    [Language.CHINESE]: '藏藏-格列卡兹'
  },
  'MYLAN-ATORVASTATIN': {
    [Language.ENGLISH]: 'MYLAN-ATORVASTATIN',
    [Language.VIETNAMESE]: 'MYLAN-ATORVASTATIN',
    [Language.HINDI]: 'माइलैन-एटोरवास्टेटिन',
    [Language.CHINESE]: '迈兰-阿托伐他汀'
  }
};

// Define full purpose translations at the module level for use in getHardcodedTranslation
const fullPurposeTranslations: Record<string, Record<Language, string>> = {
  'Typically used for managing type 2 diabetes.': {
    [Language.ENGLISH]: 'Typically used for managing type 2 diabetes.',
    [Language.VIETNAMESE]: 'Thường được sử dụng để kiểm soát bệnh tiểu đường loại 2.',
    [Language.HINDI]: 'आमतौर पर टाइप 2 मधुमेह के प्रबंधन के लिए उपयोग किया जाता है।',
    [Language.CHINESE]: '通常用于管理2型糖尿病。'
  },
  'Typically used for lowering cholesterol levels.': {
    [Language.ENGLISH]: 'Typically used for lowering cholesterol levels.',
    [Language.VIETNAMESE]: 'Thường được sử dụng để hạ mức cholesterol.',
    [Language.HINDI]: 'आमतौर पर कोलेस्ट्रॉल के स्तर को कम करने के लिए उपयोग किया जाता है।',
    [Language.CHINESE]: '通常用于降低胃固醇水平。'
  },
  'Typically used for preventing blood clots.': {
    [Language.ENGLISH]: 'Typically used for preventing blood clots.',
    [Language.VIETNAMESE]: 'Thường được sử dụng để ngăn ngừa cục máu đông.',
    [Language.HINDI]: 'आमतौर पर रक्त के थक्के को रोकने के लिए उपयोग किया जाता है।',
    [Language.CHINESE]: '通常用于防止血栓。'
  }
};

/**
 * Gets the translated label for common fields
 * @param label - The label to translate
 * @param targetLanguage - The target language
 * @returns The translated label
 */
const getTranslatedLabel = (label: string, targetLanguage: Language): string => {
  const labelTranslations: Record<string, Record<Language, string>> = {
    'Medication': {
      [Language.ENGLISH]: 'Medication',
      [Language.VIETNAMESE]: 'Thuốc',
      [Language.HINDI]: 'दवा',
      [Language.CHINESE]: '药物'
    },
    'Dosage': {
      [Language.ENGLISH]: 'Dosage',
      [Language.VIETNAMESE]: 'Liều lượng',
      [Language.HINDI]: 'खुराक',
      [Language.CHINESE]: '剂量'
    },
    'Purpose': {
      [Language.ENGLISH]: 'Purpose',
      [Language.VIETNAMESE]: 'Mục đích',
      [Language.HINDI]: 'उद्देश्य',
      [Language.CHINESE]: '目的'
    },
    'Fill Date': {
      [Language.ENGLISH]: 'Fill Date',
      [Language.VIETNAMESE]: 'Ngày cấp',
      [Language.HINDI]: 'भरने की तारीख',
      [Language.CHINESE]: '配药日期'
    }
  };

  return labelTranslations[label]?.[targetLanguage] || label;
};

/**
 * Translates a date string to the specified language
 * @param dateStr - The date string to translate
 * @param targetLanguage - The target language
 * @returns The translated date string
 */
const translateDate = (dateStr: string, targetLanguage: Language): string => {
  if (!dateStr) return dateStr;

  // If the target language is English, return the original date
  if (targetLanguage === Language.ENGLISH) {
    return dateStr;
  }

  // Handle YYYY-MM-DD format (e.g., 2025-04-11)
  if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const [year, month, day] = dateStr.split('-');
    const monthNum = parseInt(month, 10);

    // Format based on language
    switch (targetLanguage) {
      case Language.VIETNAMESE:
        return `${day} Tháng ${monthNum}, ${year}`;
      case Language.HINDI:
        const hindiMonths = [
          '',
          'जनवरी', // January
          'फरवरी', // February
          'मार्च', // March
          'अप्रैल', // April
          'मई', // May
          'जून', // June
          'जुलाई', // July
          'अगस्त', // August
          'सितंबर', // September
          'अक्टूबर', // October
          'नवंबर', // November
          'दिसंबर' // December
        ];
        return `${day} ${hindiMonths[monthNum]}, ${year}`;
      case Language.CHINESE:
        return `${year}年${monthNum}月${day}日`;
      default:
        return dateStr;
    }
  }

  // Handle common date formats with month names
  if (dateStr.includes('Mar') || dateStr.includes('Apr') || dateStr.includes('May') ||
      dateStr.includes('Jun') || dateStr.includes('Jul') || dateStr.includes('Aug') ||
      dateStr.includes('Sep') || dateStr.includes('Oct') || dateStr.includes('Nov') ||
      dateStr.includes('Dec') || dateStr.includes('Jan') || dateStr.includes('Feb')) {

    // Extract components
    const parts = dateStr.split(' ');
    let month = parts[0];
    let day = parts[1].replace(',', '');
    let year = parts[2];

    // Translate month
    const monthTranslations: Record<string, Record<Language, string>> = {
      'Jan': {
        [Language.ENGLISH]: 'Jan',
        [Language.VIETNAMESE]: 'Tháng 1',
        [Language.HINDI]: 'जनवरी',
        [Language.CHINESE]: '1月'
      },
      'Feb': {
        [Language.ENGLISH]: 'Feb',
        [Language.VIETNAMESE]: 'Tháng 2',
        [Language.HINDI]: 'फरवरी',
        [Language.CHINESE]: '2月'
      },
      'Mar': {
        [Language.ENGLISH]: 'Mar',
        [Language.VIETNAMESE]: 'Tháng 3',
        [Language.HINDI]: 'मार्च',
        [Language.CHINESE]: '3月'
      },
      'Apr': {
        [Language.ENGLISH]: 'Apr',
        [Language.VIETNAMESE]: 'Tháng 4',
        [Language.HINDI]: 'अप्रैल',
        [Language.CHINESE]: '4月'
      },
      'May': {
        [Language.ENGLISH]: 'May',
        [Language.VIETNAMESE]: 'Tháng 5',
        [Language.HINDI]: 'मई',
        [Language.CHINESE]: '5月'
      },
      'Jun': {
        [Language.ENGLISH]: 'Jun',
        [Language.VIETNAMESE]: 'Tháng 6',
        [Language.HINDI]: 'जून',
        [Language.CHINESE]: '6月'
      },
      'Jul': {
        [Language.ENGLISH]: 'Jul',
        [Language.VIETNAMESE]: 'Tháng 7',
        [Language.HINDI]: 'जुलाई',
        [Language.CHINESE]: '7月'
      },
      'Aug': {
        [Language.ENGLISH]: 'Aug',
        [Language.VIETNAMESE]: 'Tháng 8',
        [Language.HINDI]: 'अगस्त',
        [Language.CHINESE]: '8月'
      },
      'Sep': {
        [Language.ENGLISH]: 'Sep',
        [Language.VIETNAMESE]: 'Tháng 9',
        [Language.HINDI]: 'सितंबर',
        [Language.CHINESE]: '9月'
      },
      'Oct': {
        [Language.ENGLISH]: 'Oct',
        [Language.VIETNAMESE]: 'Tháng 10',
        [Language.HINDI]: 'अक्टूबर',
        [Language.CHINESE]: '10月'
      },
      'Nov': {
        [Language.ENGLISH]: 'Nov',
        [Language.VIETNAMESE]: 'Tháng 11',
        [Language.HINDI]: 'नवंबर',
        [Language.CHINESE]: '11月'
      },
      'Dec': {
        [Language.ENGLISH]: 'Dec',
        [Language.VIETNAMESE]: 'Tháng 12',
        [Language.HINDI]: 'दिसंबर',
        [Language.CHINESE]: '12月'
      }
    };

    const translatedMonth = monthTranslations[month]?.[targetLanguage] || month;

    // Format based on language
    switch (targetLanguage) {
      case Language.VIETNAMESE:
        return `${day} ${translatedMonth}, ${year}`;
      case Language.HINDI:
        return `${day} ${translatedMonth}, ${year}`;
      case Language.CHINESE:
        return `${year}年${translatedMonth}${day}日`;
      default:
        return dateStr;
    }
  }

  return dateStr;
};

export const getLanguageName = (language: Language): string => {
  return LanguageNames[language] || 'Unknown';
};

/**
 * Translates UI text based on the language
 * @param key - The text key
 * @param language - The target language
 * @returns The translated text
 */
export const translateUI = (key: string, language: Language): string => {
  const translations: Record<string, Record<Language, string>> = {
    'medicationExtractor': {
      [Language.ENGLISH]: 'Medication Extractor',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षक',
      [Language.CHINESE]: '药物提取器'
    },
    'poweredBy': {
      [Language.ENGLISH]: 'Powered by OpenAI Vision',
      [Language.VIETNAMESE]: 'Được hỗ trợ bởi OpenAI Vision',
      [Language.HINDI]: 'OpenAI Vision द्वारा संचालित',
      [Language.CHINESE]: '由 OpenAI Vision 提供支持'
    },
    'extractedMedications': {
      [Language.ENGLISH]: 'Extracted Medications',
      [Language.VIETNAMESE]: 'Thuốc Đã Trích Xuất',
      [Language.HINDI]: 'निकाली गई दवाएं',
      [Language.CHINESE]: '提取的药物'
    },
    'showRawResponse': {
      [Language.ENGLISH]: 'Show Raw Response',
      [Language.VIETNAMESE]: 'Hiển Thị Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया दिखाएं',
      [Language.CHINESE]: '显示原始响应'
    },
    'hideRawResponse': {
      [Language.ENGLISH]: 'Hide Raw Response',
      [Language.VIETNAMESE]: 'Ẩn Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया छिपाएं',
      [Language.CHINESE]: '隐藏原始响应'
    },
    'rawApiResponse': {
      [Language.ENGLISH]: 'Raw API Response:',
      [Language.VIETNAMESE]: 'Phản Hồi API Gốc:',
      [Language.HINDI]: 'कच्ची API प्रतिक्रिया:',
      [Language.CHINESE]: '原始 API 响应:'
    },
    'noMedicationsFound': {
      [Language.ENGLISH]: 'No medications found in the image.',
      [Language.VIETNAMESE]: 'Không tìm thấy thuốc trong hình ảnh.',
      [Language.HINDI]: 'छवि में कोई दवा नहीं मिली।',
      [Language.CHINESE]: '图像中未找到药物。'
    },
    'note': {
      [Language.ENGLISH]: 'Note: This app extracts medication information from doctor notes. If you encounter any errors, please wait a few moments before trying again.',
      [Language.VIETNAMESE]: 'Lưu ý: Ứng dụng này trích xuất thông tin thuốc từ ghi chú của bác sĩ. Nếu bạn gặp lỗi, vui lòng đợi một lát trước khi thử lại.',
      [Language.HINDI]: 'नोट: यह ऐप डॉक्टर के नोट्स से दवा की जानकारी निकालती है। यदि आपको कोई त्रुटियां मिलती हैं, तो कृपया फिर से प्रयास करने से पहले कुछ क्षण प्रतीक्षा करें।',
      [Language.CHINESE]: '注意：此应用程序从医生笔记中提取药物信息。如果遇到任何错误，请稍等片刻再重试。'
    },
    'takeAnotherPhoto': {
      [Language.ENGLISH]: 'Take Another Photo',
      [Language.VIETNAMESE]: 'Chụp Ảnh Khác',
      [Language.HINDI]: 'एक और फोटो लें',
      [Language.CHINESE]: '拍摄另一张照片'
    },
    'analyzing': {
      [Language.ENGLISH]: 'Analyzing image...',
      [Language.VIETNAMESE]: 'Đang phân tích hình ảnh...',
      [Language.HINDI]: 'छवि का विश्लेषण कर रहा है...',
      [Language.CHINESE]: '正在分析图像...'
    },
    'medication': {
      [Language.ENGLISH]: 'Medication',
      [Language.VIETNAMESE]: 'Thuốc',
      [Language.HINDI]: 'दवा',
      [Language.CHINESE]: '药物'
    },
    'dosage': {
      [Language.ENGLISH]: 'Dosage',
      [Language.VIETNAMESE]: 'Liều Lượng',
      [Language.HINDI]: 'खुराक',
      [Language.CHINESE]: '剂量'
    },
    'purpose': {
      [Language.ENGLISH]: 'Purpose',
      [Language.VIETNAMESE]: 'Mục Đích',
      [Language.HINDI]: 'उद्देश्य',
      [Language.CHINESE]: '用途'
    },
    'fillDate': {
      [Language.ENGLISH]: 'Fill Date',
      [Language.VIETNAMESE]: 'Ngày Cấp',
      [Language.HINDI]: 'भरने की तारीख',
      [Language.CHINESE]: '配药日期'
    },
    'noMedicationInfo': {
      [Language.ENGLISH]: 'No medication information found.',
      [Language.VIETNAMESE]: 'Không tìm thấy thông tin thuốc.',
      [Language.HINDI]: 'कोई दवा जानकारी नहीं मिली।',
      [Language.CHINESE]: '未找到药物信息。'
    },
    'selectLanguage': {
      [Language.ENGLISH]: 'Select Language',
      [Language.VIETNAMESE]: 'Chọn Ngôn Ngữ',
      [Language.HINDI]: 'भाषा चुनें',
      [Language.CHINESE]: '选择语言'
    },
    'features': {
      [Language.ENGLISH]: 'Features',
      [Language.VIETNAMESE]: 'Tính Năng',
      [Language.HINDI]: 'विशेषताएँ',
      [Language.CHINESE]: '功能'
    },
    'cameraCapture': {
      [Language.ENGLISH]: 'Camera Capture',
      [Language.VIETNAMESE]: 'Chụp Ảnh',
      [Language.HINDI]: 'कैमरा कैप्चर',
      [Language.CHINESE]: '相机拍摄'
    },
    'takePhotos': {
      [Language.ENGLISH]: 'Take photos directly from the app',
      [Language.VIETNAMESE]: 'Chụp ảnh trực tiếp từ ứng dụng',
      [Language.HINDI]: 'ऐप से सीधे फोटो लें',
      [Language.CHINESE]: '直接从应用拍照'
    },
    'gallerySelection': {
      [Language.ENGLISH]: 'Gallery Selection',
      [Language.VIETNAMESE]: 'Chọn Từ Thư Viện',
      [Language.HINDI]: 'गैलरी चयन',
      [Language.CHINESE]: '图库选择'
    },
    'selectImages': {
      [Language.ENGLISH]: 'Select images from your gallery',
      [Language.VIETNAMESE]: 'Chọn hình ảnh từ thư viện của bạn',
      [Language.HINDI]: 'अपनी गैलरी से छवियां चुनें',
      [Language.CHINESE]: '从图库中选择图像'
    },
    'medicationExtraction': {
      [Language.ENGLISH]: 'Medication Extraction',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षण',
      [Language.CHINESE]: '药物提取'
    },
    'extractDetails': {
      [Language.ENGLISH]: 'Extract medication details from doctor notes',
      [Language.VIETNAMESE]: 'Trích xuất chi tiết thuốc từ ghi chú của bác sĩ',
      [Language.HINDI]: 'डॉक्टर के नोट्स से दवा के विवरण निकालें',
      [Language.CHINESE]: '从医生笔记中提取药物详情'
    },
    'startUsingCamera': {
      [Language.ENGLISH]: 'Start Using Camera',
      [Language.VIETNAMESE]: 'Bắt Đầu Sử Dụng Máy Ảnh',
      [Language.HINDI]: 'कैमरा का उपयोग शुरू करें',
      [Language.CHINESE]: '开始使用相机'
    }
  };

  return translations[key]?.[language] || key;
};

// Minimal implementation of the Node.js net module for React Native
const EventEmitter = require('./events');

class Socket extends EventEmitter {
  constructor(options) {
    super();
    this.connecting = false;
    this.destroyed = false;
    this.readable = true;
    this.writable = true;
    this._writableState = { finished: false };
    this._readableState = { ended: false };
  }

  connect() {
    this.connecting = true;
    setTimeout(() => {
      this.connecting = false;
      this.emit('connect');
    }, 0);
    return this;
  }

  write(data, encoding, callback) {
    if (typeof encoding === 'function') {
      callback = encoding;
      encoding = null;
    }
    if (callback) {
      callback();
    }
    return true;
  }

  end(data, encoding, callback) {
    if (data) {
      this.write(data, encoding);
    }
    this._writableState.finished = true;
    this.emit('end');
    if (callback) {
      callback();
    }
    return this;
  }

  destroy() {
    this.destroyed = true;
    this.emit('close');
    return this;
  }

  pause() {
    return this;
  }

  resume() {
    return this;
  }

  setTimeout(timeout, callback) {
    if (callback) {
      this.once('timeout', callback);
    }
    return this;
  }

  setNoDelay(noDelay) {
    return this;
  }

  setKeepAlive(enable, initialDelay) {
    return this;
  }

  address() {
    return { port: 0, family: 'IPv4', address: '0.0.0.0' };
  }

  isPaused() {
    return false;
  }
}

class Server extends EventEmitter {
  constructor(options, connectionListener) {
    super();
    if (typeof options === 'function') {
      connectionListener = options;
      options = {};
    }
    if (connectionListener) {
      this.on('connection', connectionListener);
    }
  }

  listen(port, host, backlog, callback) {
    if (typeof port === 'function') {
      callback = port;
      port = null;
    } else if (typeof host === 'function') {
      callback = host;
      host = null;
    } else if (typeof backlog === 'function') {
      callback = backlog;
      backlog = null;
    }
    if (callback) {
      this.once('listening', callback);
    }
    setTimeout(() => {
      this.emit('listening');
    }, 0);
    return this;
  }

  close(callback) {
    if (callback) {
      this.once('close', callback);
    }
    setTimeout(() => {
      this.emit('close');
    }, 0);
    return this;
  }

  address() {
    return { port: 0, family: 'IPv4', address: '0.0.0.0' };
  }
}

function createServer(options, connectionListener) {
  return new Server(options, connectionListener);
}

function createConnection(options, connectListener) {
  const socket = new Socket(options);
  if (connectListener) {
    socket.on('connect', connectListener);
  }
  socket.connect();
  return socket;
}

function connect(options, connectListener) {
  return createConnection(options, connectListener);
}

module.exports = {
  Socket,
  Server,
  createServer,
  createConnection,
  connect
};

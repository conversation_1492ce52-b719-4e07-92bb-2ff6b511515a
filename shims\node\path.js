// Minimal implementation of the Node.js path module for React Native

// Platform-specific path separator
const sep = '/';
const delimiter = ':';

function isAbsolute(path) {
  return path.startsWith('/');
}

function normalize(path) {
  // Replace multiple slashes with a single slash
  path = path.replace(/\/+/g, '/');
  
  // Remove trailing slash
  if (path.length > 1 && path.endsWith('/')) {
    path = path.slice(0, -1);
  }
  
  return path;
}

function join(...paths) {
  return normalize(paths.filter(Boolean).join('/'));
}

function resolve(...paths) {
  let resolvedPath = '';
  
  for (let i = paths.length - 1; i >= 0; i--) {
    const path = paths[i];
    if (path && isAbsolute(path)) {
      resolvedPath = path;
      break;
    } else if (path) {
      resolvedPath = resolvedPath ? join(path, resolvedPath) : path;
    }
  }
  
  return normalize(resolvedPath || '/');
}

function dirname(path) {
  if (!path) return '.';
  
  path = normalize(path);
  
  const lastSlashIndex = path.lastIndexOf('/');
  if (lastSlashIndex === -1) return '.';
  if (lastSlashIndex === 0) return '/';
  
  return path.slice(0, lastSlashIndex);
}

function basename(path, ext) {
  if (!path) return '';
  
  path = normalize(path);
  
  const lastSlashIndex = path.lastIndexOf('/');
  const base = lastSlashIndex === -1 ? path : path.slice(lastSlashIndex + 1);
  
  if (ext && base.endsWith(ext)) {
    return base.slice(0, -ext.length);
  }
  
  return base;
}

function extname(path) {
  if (!path) return '';
  
  const base = basename(path);
  const lastDotIndex = base.lastIndexOf('.');
  
  if (lastDotIndex === -1 || lastDotIndex === 0) return '';
  
  return base.slice(lastDotIndex);
}

function parse(path) {
  const dir = dirname(path);
  const base = basename(path);
  const ext = extname(path);
  const name = base.slice(0, base.length - ext.length);
  
  return { root: isAbsolute(path) ? '/' : '', dir, base, ext, name };
}

function format(pathObject) {
  const { root = '', dir = '', base = '', ext = '', name = '' } = pathObject;
  
  let path = '';
  
  if (dir) {
    path = dir;
  } else if (root) {
    path = root;
  }
  
  if (base) {
    if (path && !path.endsWith('/')) {
      path += '/';
    }
    path += base;
  } else if (name) {
    if (path && !path.endsWith('/')) {
      path += '/';
    }
    path += name;
    if (ext) {
      path += ext;
    }
  }
  
  return path;
}

function relative(from, to) {
  from = resolve(from);
  to = resolve(to);
  
  if (from === to) return '';
  
  const fromParts = from.split('/').filter(Boolean);
  const toParts = to.split('/').filter(Boolean);
  
  let commonParts = 0;
  for (let i = 0; i < Math.min(fromParts.length, toParts.length); i++) {
    if (fromParts[i] !== toParts[i]) break;
    commonParts++;
  }
  
  const upParts = fromParts.length - commonParts;
  const result = [...Array(upParts).fill('..'), ...toParts.slice(commonParts)];
  
  return result.join('/') || '.';
}

module.exports = {
  sep,
  delimiter,
  isAbsolute,
  normalize,
  join,
  resolve,
  dirname,
  basename,
  extname,
  parse,
  format,
  relative,
  posix: {
    sep,
    delimiter,
    isAbsolute,
    normalize,
    join,
    resolve,
    dirname,
    basename,
    extname,
    parse,
    format,
    relative
  },
  win32: {
    sep: '\\',
    delimiter: ';',
    isAbsolute: (path) => /^([a-zA-Z]:|\\)/.test(path),
    normalize: (path) => path.replace(/\\/g, '/'),
    join: (...paths) => join(...paths).replace(/\//g, '\\'),
    resolve: (...paths) => resolve(...paths).replace(/\//g, '\\'),
    dirname: (path) => dirname(path).replace(/\//g, '\\'),
    basename,
    extname,
    parse: (path) => {
      const result = parse(path);
      result.dir = result.dir.replace(/\//g, '\\');
      return result;
    },
    format: (pathObject) => format(pathObject).replace(/\//g, '\\'),
    relative: (from, to) => relative(from, to).replace(/\//g, '\\')
  }
};

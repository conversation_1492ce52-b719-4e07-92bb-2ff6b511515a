// Minimal implementation of the Node.js querystring module for React Native

function stringify(obj, sep, eq, options) {
  sep = sep || '&';
  eq = eq || '=';
  
  if (!obj) return '';
  
  const encode = options && options.encodeURIComponent || encodeURIComponent;
  
  return Object.keys(obj)
    .map(key => {
      const value = obj[key];
      if (value === undefined) return '';
      
      if (value === null) return encode(key);
      
      if (Array.isArray(value)) {
        return value
          .map(val => `${encode(key)}${eq}${encode(val)}`)
          .join(sep);
      }
      
      return `${encode(key)}${eq}${encode(value)}`;
    })
    .filter(Boolean)
    .join(sep);
}

function parse(str, sep, eq, options) {
  sep = sep || '&';
  eq = eq || '=';
  
  const decode = options && options.decodeURIComponent || decodeURIComponent;
  const result = {};
  
  if (typeof str !== 'string' || str.length === 0) {
    return result;
  }
  
  str.split(sep).forEach(part => {
    if (!part) return;
    
    const eqIdx = part.indexOf(eq);
    let key, value;
    
    if (eqIdx === -1) {
      key = decode(part);
      value = '';
    } else {
      key = decode(part.slice(0, eqIdx));
      value = decode(part.slice(eqIdx + 1));
    }
    
    if (key in result) {
      if (!Array.isArray(result[key])) {
        result[key] = [result[key]];
      }
      result[key].push(value);
    } else {
      result[key] = value;
    }
  });
  
  return result;
}

function escape(str) {
  return encodeURIComponent(str)
    .replace(/[!'()*]/g, c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);
}

function unescape(str) {
  return decodeURIComponent(str);
}

module.exports = {
  stringify,
  parse,
  escape,
  unescape
};

// Test script for Resend email service
require('dotenv').config();
const { Resend } = require('resend');

// Get API key from environment variables
const resendApiKey = process.env.EXPO_PUBLIC_RESEND_API_KEY;

if (!resendApiKey) {
  console.error('Resend API key is not set. Please set EXPO_PUBLIC_RESEND_API_KEY in your .env file.');
  process.exit(1);
}

// Initialize Resend
const resend = new Resend(resendApiKey);

// Default sender email
const DEFAULT_FROM_EMAIL = '<EMAIL>';

// Test email address - replace with your email
const TEST_EMAIL = '<EMAIL>';

// Send a test email
async function sendTestEmail() {
  try {
    console.log('Sending test email to:', TEST_EMAIL);
    
    const { data, error } = await resend.emails.send({
      from: DEFAULT_FROM_EMAIL,
      to: TEST_EMAIL,
      subject: 'Test Email from PillLogic',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">Test Email from PillLogic</h1>
          <p>This is a test email to verify that Resend is working correctly with your PillLogic app.</p>
          <p>If you're seeing this, it means the integration is successful!</p>
          <p>Best regards,<br>The PillLogic Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending test email:', error);
      return;
    }

    console.log('Test email sent successfully!');
    console.log('Email ID:', data.id);
  } catch (error) {
    console.error('Exception sending test email:', error);
  }
}

// Run the test
sendTestEmail();

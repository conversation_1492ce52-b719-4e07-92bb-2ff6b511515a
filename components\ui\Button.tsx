import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, ViewStyle, TextStyle, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, BorderRadius } from '../../constants/DocAidDesign';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'success';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  children?: React.ReactNode;
  title?: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: string | React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  children,
  title,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  disabled = false,
  loading = false,
  onPress,
  style,
  textStyle,
}) => {
  const getButtonStyle = () => {
    const buttonStyles = [
      styles.button,
      styles[`button_${variant}`],
      styles[`button_${size}`],
      fullWidth && styles.fullWidth,
      disabled && styles.disabled,
      style,
    ];
    return buttonStyles;
  };

  const getTextStyle = () => {
    const textStyles = [
      styles.text,
      styles[`text_${variant}`],
      styles[`text_${size}`],
      disabled && styles.textDisabled,
      textStyle,
    ];
    return textStyles;
  };

  const renderIcon = () => {
    if (!icon) return null;

    // If icon is a ReactNode, return it directly
    if (typeof icon !== 'string') {
      return icon;
    }

    // Otherwise, render an Ionicons icon
    return (
      <Ionicons
        name={icon}
        size={size === 'sm' ? 16 : size === 'md' ? 18 : 20}
        color={variant === 'primary' ? Colors.white :
               variant === 'destructive' ? Colors.docAlert :
               Colors.docPurple.DEFAULT}
        style={[
          iconPosition === 'left' ? styles.iconLeft : styles.iconRight,
          disabled && styles.iconDisabled
        ]}
      />
    );
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={
            variant === 'primary' || variant === 'secondary' || variant === 'destructive' || variant === 'success'
              ? Colors.white
              : Colors.docPurple.DEFAULT
          }
        />
      ) : (
        <>
          {iconPosition === 'left' && renderIcon()}
          {children || (
            <Text style={getTextStyle()}>
              {title}
            </Text>
          )}
          {iconPosition === 'right' && renderIcon()}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.md,
  },
  button_primary: {
    backgroundColor: Colors.docPurple.DEFAULT,
  },
  button_secondary: {
    backgroundColor: Colors.docPurple.light,
  },
  button_outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.docPurple.DEFAULT,
  },
  button_ghost: {
    backgroundColor: 'transparent',
  },
  button_destructive: {
    backgroundColor: Colors.docAlert,
  },
  button_success: {
    backgroundColor: '#4CAF50',
  },
  button_sm: {
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.md,
  },
  button_md: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.lg,
  },
  button_lg: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.5,
  },
  text: {
    fontWeight: '500',
    textAlign: 'center',
  },
  text_primary: {
    color: Colors.white,
  },
  text_secondary: {
    color: Colors.white,
  },
  text_outline: {
    color: Colors.docPurple.DEFAULT,
  },
  text_ghost: {
    color: Colors.docPurple.DEFAULT,
  },
  text_destructive: {
    color: Colors.white,
  },
  text_success: {
    color: Colors.white,
  },
  text_sm: {
    fontSize: 14,
  },
  text_md: {
    fontSize: 16,
  },
  text_lg: {
    fontSize: 18,
  },
  textDisabled: {
    opacity: 0.7,
  },
  iconLeft: {
    marginRight: Spacing.xs,
  },
  iconRight: {
    marginLeft: Spacing.xs,
  },
  iconDisabled: {
    opacity: 0.7,
  },
});

export default Button;

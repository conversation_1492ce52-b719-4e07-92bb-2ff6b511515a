// Minimal implementation of the Node.js tls module for React Native
const { EventEmitter } = require('./events');
const net = require('./net');

class TLSSocket extends net.Socket {
  constructor(socket, options) {
    super();
    this.authorized = true;
    this.encrypted = true;
  }

  getPeerCertificate(detailed) {
    return {
      subject: { CN: 'example.com' },
      issuer: { CN: 'Example CA' },
      valid_from: 'Jan 1 00:00:00 2020 GMT',
      valid_to: 'Jan 1 00:00:00 2030 GMT',
      fingerprint: '00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00'
    };
  }

  getCipher() {
    return {
      name: 'ECDHE-RSA-AES128-GCM-SHA256',
      version: 'TLSv1.2'
    };
  }

  getProtocol() {
    return 'TLSv1.2';
  }

  getSession() {
    return Buffer.from([]);
  }

  getSharedSigalgs() {
    return [];
  }

  exportKeyingMaterial(length, label, context) {
    return Buffer.alloc(length);
  }

  disableRenegotiation() {}
}

class Server extends net.Server {
  constructor(options, secureConnectionListener) {
    super();
    if (secureConnectionListener) {
      this.on('secureConnection', secureConnectionListener);
    }
  }

  addContext(hostname, context) {}
}

function createServer(options, secureConnectionListener) {
  return new Server(options, secureConnectionListener);
}

function connect(options, callback) {
  const socket = new TLSSocket(null, options);
  if (callback) {
    socket.once('secureConnect', callback);
  }
  process.nextTick(() => {
    socket.emit('secureConnect');
  });
  return socket;
}

module.exports = {
  TLSSocket,
  Server,
  createServer,
  connect,
  createSecureContext: (options) => ({}),
  getCiphers: () => [],
  DEFAULT_ECDH_CURVE: 'auto',
  DEFAULT_MAX_VERSION: 'TLSv1.3',
  DEFAULT_MIN_VERSION: 'TLSv1.2',
  rootCertificates: []
};

import React, { useState } from 'react';
import { StyleSheet, SafeAreaView, StatusBar, Platform } from 'react-native';
import PlatformCamera from '../components/PlatformCamera';
import ImageAnalysisNew from '../components/ImageAnalysisNew';
import { Colors } from '../constants/PillLogicDesign';
import AppShell from '../components/layout/AppShell';

export default function ScanScreen() {
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const handleImageCaptured = (imageUri: string) => {
    setCapturedImage(imageUri);
  };

  const handleRetake = () => {
    setCapturedImage(null);
  };

  return (
    <>
      {capturedImage ? (
        <AppShell>
          <ImageAnalysisNew imageUri={capturedImage} onRetake={handleRetake} />
        </AppShell>
      ) : (
        <PlatformCamera
          onImageCaptured={handleImageCaptured}
          cameraMode="prescription"
        />
      )}
    </>
  );
}

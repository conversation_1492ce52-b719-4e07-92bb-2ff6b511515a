import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the user from the request
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const { userId } = await req.json()

    // Verify the user is trying to revoke their own token
    if (user.id !== userId) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: Can only revoke your own token' }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if this is an Apple Sign-In user
    if (user.app_metadata?.provider !== 'apple') {
      return new Response(
        JSON.stringify({ message: 'Not an Apple Sign-In user, no token to revoke' }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get the Apple refresh token from user metadata
    const refreshToken = user.user_metadata?.refresh_token

    if (!refreshToken) {
      console.log('No Apple refresh token found for user:', user.id)
      return new Response(
        JSON.stringify({ message: 'No Apple refresh token found' }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Revoke the Apple token using Apple's REST API
    try {
      const revokeResponse = await fetch('https://appleid.apple.com/auth/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: Deno.env.get('APPLE_CLIENT_ID') ?? '',
          client_secret: Deno.env.get('APPLE_CLIENT_SECRET') ?? '',
          token: refreshToken,
          token_type_hint: 'refresh_token',
        }),
      })

      if (revokeResponse.ok) {
        console.log('Successfully revoked Apple token for user:', user.id)
        return new Response(
          JSON.stringify({ message: 'Apple token revoked successfully' }),
          { 
            status: 200, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      } else {
        const errorText = await revokeResponse.text()
        console.error('Failed to revoke Apple token:', errorText)
        return new Response(
          JSON.stringify({ error: 'Failed to revoke Apple token', details: errorText }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    } catch (revokeError) {
      console.error('Exception revoking Apple token:', revokeError)
      return new Response(
        JSON.stringify({ error: 'Exception occurred while revoking Apple token' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Error in revoke-apple-token function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

-- DETAILED DEBUG FOR ACCOUNT DELETION
-- This will help us find exactly where the deletion is failing

-- ============================================================================
-- STEP 1: Check what data exists for the test user
-- ============================================================================

-- Replace with your test user ID: 67c573aa-dae7-4a57-9c7c-9f5c0234da01
DO $$
DECLARE
    test_user_id UUID := '67c573aa-dae7-4a57-9c7c-9f5c0234da01';
    count_result INTEGER;
BEGIN
    -- Check auth.users
    SELECT COUNT(*) INTO count_result FROM auth.users WHERE id = test_user_id;
    RAISE NOTICE 'auth.users count: %', count_result;
    
    -- Check profiles
    SELECT COUNT(*) INTO count_result FROM public.profiles WHERE id = test_user_id;
    RAISE NOTICE 'profiles count: %', count_result;
    
    -- Check daily_feature_usage
    SELECT COUNT(*) INTO count_result FROM public.daily_feature_usage WHERE user_id = test_user_id;
    RAISE NOTICE 'daily_feature_usage count: %', count_result;
    
    -- Check live_session_tracking
    SELECT COUNT(*) INTO count_result FROM public.live_session_tracking WHERE user_id = test_user_id;
    RAISE NOTICE 'live_session_tracking count: %', count_result;
END $$;

-- ============================================================================
-- STEP 2: Create a verbose deletion function that shows each step
-- ============================================================================

CREATE OR REPLACE FUNCTION public.delete_user_account_debug(
  user_uuid UUID
)
RETURNS TEXT AS $$
DECLARE
  user_exists BOOLEAN;
  result_text TEXT := '';
  count_deleted INTEGER;
BEGIN
  result_text := result_text || 'Starting deletion for user: ' || user_uuid || E'\n';
  
  -- Check if user exists
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = user_uuid) INTO user_exists;
  
  IF NOT user_exists THEN
    result_text := result_text || 'ERROR: User not found in auth.users' || E'\n';
    RETURN result_text;
  END IF;
  
  result_text := result_text || 'User exists in auth.users' || E'\n';

  -- Delete from live_session_tracking
  BEGIN
    DELETE FROM public.live_session_tracking WHERE user_id = user_uuid;
    GET DIAGNOSTICS count_deleted = ROW_COUNT;
    result_text := result_text || 'Deleted ' || count_deleted || ' rows from live_session_tracking' || E'\n';
  EXCEPTION
    WHEN OTHERS THEN
      result_text := result_text || 'ERROR deleting from live_session_tracking: ' || SQLERRM || E'\n';
      RETURN result_text;
  END;
  
  -- Delete from daily_feature_usage
  BEGIN
    DELETE FROM public.daily_feature_usage WHERE user_id = user_uuid;
    GET DIAGNOSTICS count_deleted = ROW_COUNT;
    result_text := result_text || 'Deleted ' || count_deleted || ' rows from daily_feature_usage' || E'\n';
  EXCEPTION
    WHEN OTHERS THEN
      result_text := result_text || 'ERROR deleting from daily_feature_usage: ' || SQLERRM || E'\n';
      RETURN result_text;
  END;
  
  -- Delete from profiles
  BEGIN
    DELETE FROM public.profiles WHERE id = user_uuid;
    GET DIAGNOSTICS count_deleted = ROW_COUNT;
    result_text := result_text || 'Deleted ' || count_deleted || ' rows from profiles' || E'\n';
  EXCEPTION
    WHEN OTHERS THEN
      result_text := result_text || 'ERROR deleting from profiles: ' || SQLERRM || E'\n';
      RETURN result_text;
  END;
  
  -- Delete from auth.users
  BEGIN
    DELETE FROM auth.users WHERE id = user_uuid;
    GET DIAGNOSTICS count_deleted = ROW_COUNT;
    result_text := result_text || 'Deleted ' || count_deleted || ' rows from auth.users' || E'\n';
  EXCEPTION
    WHEN OTHERS THEN
      result_text := result_text || 'ERROR deleting from auth.users: ' || SQLERRM || E'\n';
      RETURN result_text;
  END;
  
  result_text := result_text || 'SUCCESS: All deletions completed' || E'\n';
  RETURN result_text;
  
EXCEPTION
  WHEN OTHERS THEN
    result_text := result_text || 'UNEXPECTED ERROR: ' || SQLERRM || E'\n';
    RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.delete_user_account_debug(UUID) TO authenticated;

-- ============================================================================
-- STEP 3: Test the debug function
-- ============================================================================

-- Run this to see exactly what's happening:
SELECT public.delete_user_account_debug('67c573aa-dae7-4a57-9c7c-9f5c0234da01');

-- ============================================================================
-- STEP 4: Check RLS policies that might be blocking deletion
-- ============================================================================

-- Check what RLS policies exist on the tables
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'daily_feature_usage', 'live_session_tracking')
ORDER BY tablename, policyname;

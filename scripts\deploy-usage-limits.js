/**
 * <PERSON><PERSON><PERSON> to deploy the usage limits migration to Supabase
 * 
 * Usage:
 * 1. Make sure you have the Supabase CLI installed
 * 2. Run: node scripts/deploy-usage-limits.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if Supabase CLI is installed
try {
  execSync('supabase --version', { stdio: 'ignore' });
  console.log('✅ Supabase CLI is installed');
} catch (error) {
  console.error('❌ Supabase CLI is not installed. Please install it first:');
  console.error('npm install -g supabase');
  process.exit(1);
}

// Check if the migration file exists
const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240701000000_add_feature_usage_tracking.sql');
if (!fs.existsSync(migrationPath)) {
  console.error('❌ Migration file not found:', migrationPath);
  process.exit(1);
}

console.log('✅ Migration file found:', migrationPath);

// Deploy the migration
try {
  console.log('🚀 Deploying migration to Supabase...');
  execSync('supabase db push', { stdio: 'inherit' });
  console.log('✅ Migration deployed successfully!');
} catch (error) {
  console.error('❌ Failed to deploy migration:', error.message);
  process.exit(1);
}

console.log('\n📋 Next steps:');
console.log('1. Make sure your app is using the latest Supabase client');
console.log('2. Test the usage limits functionality');
console.log('3. Monitor for any issues in the Supabase dashboard');

I submitted my app to Apple and this is their feedback with screenshot

"Guideline 2.1 - Performance - App Completeness

We found that your in-app purchase products exhibited one or more bugs which create a poor user experience. Specifically, an error message appears when subscribing to the Pro and Premium plans. Please review the details and resources below and complete the next steps.

Review device details: 

- Device type: iPhone 13 and iPad Air 11-inch (M2) 
- OS version: iOS 18.6 and iPadOS 18.6

Next Steps

When validating receipts on your server, your server needs to be able to handle a production-signed app getting its receipts from Apple’s test environment. The recommended approach is for your production server to always validate receipts against the production App Store first. If validation fails with the error code "Sandbox receipt used in production," you should validate against the test environment instead.

Additionally, note that the Account Holder must accept the Paid Apps Agreement in the Business section of App Store Connect before paid in-app purchases will function.

Resources

- Learn how to set up and test in-app purchase products in the sandbox environment.
- Learn more about validating receipts with the App Store.
"






We use revenutcat for this, and you said you did help me to fix it, I also did ask you to show me if we able to try it despite we can't test it on TestFlight or in Locall, so you created a "Test packing loading" button in the payment page, but check the log, it return 

" LOG  [2025-08-15T14:22:27.152Z] LOG: Subscription tier from database: free
 LOG  [2025-08-15T14:22:27.152Z] LOG: Returning user data with subscription tier: free
 LOG  [2025-08-15T14:22:27.152Z] LOG: User data loaded successfully and verified
 LOG  [2025-08-15T14:22:27.152Z] LOG: User subscription tier: free
 LOG  [2025-08-15T14:22:31.988Z] LOG: Initializing Apple In-App Purchases...
 LOG  [2025-08-15T14:22:31.989Z] LOG: IAP: Attempting to initialize RevenueCat...
 LOG  [2025-08-15T14:22:31.990Z] LOG: IAP: Platform: ios
 LOG  [2025-08-15T14:22:31.990Z] LOG: IAP: App ownership: expo
 LOG  [2025-08-15T14:22:31.991Z] LOG: IAP: Running in Expo Go - RevenueCat not available
 LOG  [2025-08-15T14:22:31.992Z] LOG: SubscriptionScreen mounted, current user tier: free
 LOG  [2025-08-15T14:22:32.006Z] LOG: IAP not available - likely running in Expo Go
 LOG  [2025-08-15T14:22:34.811Z] LOG: 🧪 Testing RevenueCat package fetching...
 LOG  [2025-08-15T14:22:34.816Z] LOG: IAP: Not available in Expo Go
 LOG  [2025-08-15T14:22:34.817Z] LOG: 📦 Packages found: 0"

So it is not working despite per your fix you said it should, so please figure it out. If you need me to share any info so you can dig deeper, feel free to ask
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user's JWT token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const url = new URL(req.url)
    const action = url.searchParams.get('action')

    switch (action) {
      case 'fraud-report':
        return await generateFraudReport(supabaseClient)
      
      case 'validation-stats':
        const days = parseInt(url.searchParams.get('days') || '7')
        return await getValidationStats(supabaseClient, days)
      
      case 'user-activity':
        const userId = url.searchParams.get('userId')
        if (!userId) {
          return new Response(
            JSON.stringify({ error: 'Missing userId parameter' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }
        return await getUserActivity(supabaseClient, userId)
      
      case 'suspicious-transactions':
        return await getSuspiciousTransactions(supabaseClient)
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action parameter' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        success: false 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function generateFraudReport(supabaseClient: any) {
  try {
    // Get users with fraud flags
    const { data: fraudUsers, error: fraudError } = await supabaseClient
      .from('profiles')
      .select('id, email, fraud_flags, validation_attempts, created_at')
      .not('fraud_flags', 'eq', '{}')
      .order('validation_attempts', { ascending: false })

    if (fraudError) {
      throw fraudError
    }

    // Get recent failed validations
    const { data: failedValidations, error: failedError } = await supabaseClient
      .from('purchase_validation_log')
      .select('user_id, transaction_id, product_id, error_message, created_at')
      .eq('validation_result', 'failed')
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(100)

    if (failedError) {
      throw failedError
    }

    // Get duplicate transaction attempts
    const { data: duplicateAttempts, error: duplicateError } = await supabaseClient
      .from('purchase_validation_log')
      .select('transaction_id, COUNT(*) as attempt_count')
      .eq('validation_result', 'duplicate')
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .group('transaction_id')
      .having('COUNT(*)', 'gt', 1)

    if (duplicateError) {
      throw duplicateError
    }

    const report = {
      success: true,
      generated_at: new Date().toISOString(),
      fraud_summary: {
        users_with_fraud_flags: fraudUsers?.length || 0,
        recent_failed_validations: failedValidations?.length || 0,
        duplicate_transaction_attempts: duplicateAttempts?.length || 0
      },
      fraud_users: fraudUsers,
      recent_failures: failedValidations,
      duplicate_attempts: duplicateAttempts
    }

    return new Response(
      JSON.stringify(report),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error generating fraud report:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to generate fraud report' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function getValidationStats(supabaseClient: any, days: number) {
  try {
    const { data: stats, error } = await supabaseClient.rpc('get_validation_stats', {
      days_back: days
    })

    if (error) {
      throw error
    }

    return new Response(
      JSON.stringify({
        success: true,
        period_days: days,
        stats: stats
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error getting validation stats:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to get validation statistics' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function getUserActivity(supabaseClient: any, userId: string) {
  try {
    // Get user's validation history
    const { data: validationHistory, error: historyError } = await supabaseClient
      .from('purchase_validation_log')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50)

    if (historyError) {
      throw historyError
    }

    // Get user's current subscription status
    const { data: userProfile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('subscription_tier, subscription_status, fraud_flags, validation_attempts, last_receipt_validation')
      .eq('id', userId)
      .single()

    if (profileError) {
      throw profileError
    }

    return new Response(
      JSON.stringify({
        success: true,
        user_id: userId,
        current_status: userProfile,
        validation_history: validationHistory
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error getting user activity:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to get user activity' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

async function getSuspiciousTransactions(supabaseClient: any) {
  try {
    // Get transactions with multiple validation attempts
    const { data: suspiciousTransactions, error } = await supabaseClient
      .from('purchase_validation_log')
      .select('transaction_id, user_id, product_id, validation_result, COUNT(*) as attempt_count')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .group('transaction_id, user_id, product_id, validation_result')
      .having('COUNT(*)', 'gt', 3)
      .order('attempt_count', { ascending: false })

    if (error) {
      throw error
    }

    return new Response(
      JSON.stringify({
        success: true,
        suspicious_transactions: suspiciousTransactions
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error getting suspicious transactions:', error)
    return new Response(
      JSON.stringify({ error: 'Failed to get suspicious transactions' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

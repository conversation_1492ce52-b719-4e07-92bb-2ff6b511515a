import React from 'react';
import { Text as RNText, TextStyle, StyleSheet } from 'react-native';
import { Colors, Typography } from '../../constants/DesignSystem';

type TextVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'subtitle' | 'body' | 'caption';
type TextWeight = 'regular' | 'medium' | 'semibold' | 'bold';
type TextColor = 'primary' | 'secondary' | 'tertiary' | 'white' | 'error' | 'success' | 'warning' | 'info';
type TextAlign = 'left' | 'center' | 'right';

interface TextProps {
  children: React.ReactNode;
  variant?: TextVariant;
  weight?: TextWeight;
  color?: TextColor;
  align?: TextAlign;
  style?: TextStyle;
  numberOfLines?: number;
}

const Text: React.FC<TextProps> = ({
  children,
  variant = 'body',
  weight = 'regular',
  color = 'primary',
  align = 'left',
  style,
  numberOfLines,
  ...props
}) => {
  const textStyle = [
    styles.base,
    styles[variant],
    styles[`weight_${weight}`],
    styles[`color_${color}`],
    styles[`align_${align}`],
    style,
  ];

  return (
    <RNText style={textStyle} numberOfLines={numberOfLines} {...props}>
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  base: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.regular,
    color: Colors.textPrimary,
  },

  // Variants
  h1: {
    fontSize: Typography.fontSize.xxxl,
    lineHeight: Typography.lineHeight.xxxl,
    fontWeight: Typography.fontWeight.bold,
  },
  h2: {
    fontSize: Typography.fontSize.xxl,
    lineHeight: Typography.lineHeight.xxl,
    fontWeight: Typography.fontWeight.bold,
  },
  h3: {
    fontSize: Typography.fontSize.xl,
    lineHeight: Typography.lineHeight.xl,
    fontWeight: Typography.fontWeight.semibold,
  },
  h4: {
    fontSize: Typography.fontSize.lg,
    lineHeight: Typography.lineHeight.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  subtitle: {
    fontSize: Typography.fontSize.md,
    lineHeight: Typography.lineHeight.md,
    fontWeight: Typography.fontWeight.medium,
  },
  body: {
    fontSize: Typography.fontSize.md,
    lineHeight: Typography.lineHeight.md,
  },
  caption: {
    fontSize: Typography.fontSize.sm,
    lineHeight: Typography.lineHeight.sm,
  },

  // Weights
  weight_regular: {
    fontWeight: Typography.fontWeight.regular,
  },
  weight_medium: {
    fontWeight: Typography.fontWeight.medium,
  },
  weight_semibold: {
    fontWeight: Typography.fontWeight.semibold,
  },
  weight_bold: {
    fontWeight: Typography.fontWeight.bold,
  },

  // Colors
  color_primary: {
    color: Colors.textPrimary,
  },
  color_secondary: {
    color: '#000000',
  },
  color_tertiary: {
    color: '#000000',
  },
  color_white: {
    color: Colors.white,
  },
  color_error: {
    color: Colors.error,
  },
  color_success: {
    color: Colors.success,
  },
  color_warning: {
    color: Colors.warning,
  },
  color_info: {
    color: Colors.info,
  },

  // Alignment
  align_left: {
    textAlign: 'left',
  },
  align_center: {
    textAlign: 'center',
  },
  align_right: {
    textAlign: 'right',
  },
});

export default Text;

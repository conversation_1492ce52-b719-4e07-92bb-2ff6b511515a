import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { Colors, BorderRadius, Spacing, Typography } from '../constants/DesignSystem';
import { useLanguage } from '../contexts/LanguageContext';

export interface Medication {
  name: string;
  dosage: string;
  purpose: string;
  fillDate: string;
}

interface MedicationTableProps {
  medications: Medication[];
  compact?: boolean;
}

export default function MedicationTable({ medications, compact = false }: MedicationTableProps) {
  const { t } = useLanguage();

  if (!medications || medications.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>{t('noMedicationInfo')}</Text>
      </View>
    );
  }

  // Render compact mode (just medication names and dosages)
  if (compact) {
    return (
      <View style={styles.compactContainer}>
        {medications.map((med, index) => (
          <View
            key={`${med.name}-${index}`}
            style={styles.compactRow}
          >
            <Text style={styles.compactMedName}>{med.name}</Text>
            <Text style={styles.compactDosage}>{med.dosage}</Text>
          </View>
        ))}
      </View>
    );
  }

  // Render full table
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={[styles.headerCell, styles.medicationCell]}>{t('medication')}</Text>
        <Text style={[styles.headerCell, styles.dosageCell]}>{t('dosage')}</Text>
        <Text style={[styles.headerCell, styles.purposeCell]}>{t('purpose')}</Text>
        <Text style={[styles.headerCell, styles.dateCell]}>{t('fillDate')}</Text>
      </View>
      <ScrollView style={styles.tableBody} contentContainerStyle={styles.tableBodyContent}>
        {medications.map((med, index) => (
          <View
            key={`${med.name}-${index}`}
            style={[
              styles.row,
              index % 2 === 0 ? styles.evenRow : styles.oddRow
            ]}
          >
            <Text style={[styles.cell, styles.medicationCell]}>{med.name}</Text>
            <Text style={[styles.cell, styles.dosageCell]}>{med.dosage}</Text>
            <Text style={[styles.cell, styles.purposeCell]}>{med.purpose}</Text>
            <Text style={[styles.cell, styles.dateCell]}>{med.fillDate}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.sm,
  },
  headerCell: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  tableBody: {
    maxHeight: 300,
  },
  tableBodyContent: {
    // Add any styles for the content container here
  },
  row: {
    flexDirection: 'row',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  evenRow: {
    backgroundColor: Colors.infoLight,
  },
  oddRow: {
    backgroundColor: Colors.white,
  },
  cell: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textPrimary,
  },
  medicationCell: {
    flex: 2,
    paddingRight: 5,
  },
  dosageCell: {
    flex: 2,
    paddingRight: 5,
  },
  purposeCell: {
    flex: 3,
    paddingRight: 5,
  },
  dateCell: {
    flex: 1.5,
  },
  emptyContainer: {
    padding: Spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    marginVertical: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  emptyText: {
    color: Colors.textPrimary,
    fontSize: Typography.fontSize.md,
  },
  // Compact styles
  compactContainer: {
    marginVertical: Spacing.xs,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  compactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.white,
  },
  compactMedName: {
    flex: 1,
    fontSize: Typography.fontSize.sm,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  compactDosage: {
    fontSize: Typography.fontSize.sm,
    color: Colors.textPrimary,
    marginLeft: Spacing.sm,
  },
});

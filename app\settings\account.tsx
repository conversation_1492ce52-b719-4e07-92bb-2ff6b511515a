import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  Alert,
  ScrollView,
  Switch,
  TextInput
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../../constants/DocAidDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { useCaptcha } from '../../hooks/useCaptcha';
import CaptchaModal from '../../components/CaptchaModal';
import { canResetPassword, getAuthProvider, getProviderDisplayName } from '../../lib/authUtils';

export default function AccountSettingsScreen() {
  const { user, session, resetPassword, refreshUser, deleteAccount } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();
  const {
    isCaptchaModalVisible,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  } = useCaptcha();

  // State for form fields
  const [displayName, setDisplayName] = useState(user?.user_metadata?.name || '');
  const [displayNameError, setDisplayNameError] = useState('');

  // State for notification preferences
  const [emailNotifications, setEmailNotifications] = useState(
    user?.user_metadata?.preferences?.emailNotifications !== false
  );
  const [appNotifications, setAppNotifications] = useState(
    user?.user_metadata?.preferences?.appNotifications !== false
  );

  // Check if user can reset password (only for email/password users)
  const canUserResetPassword = canResetPassword(user, session);
  const authProvider = getAuthProvider(user, session);
  const providerDisplayName = getProviderDisplayName(authProvider);

  // State for privacy settings
  const [dataCollection, setDataCollection] = useState(
    user?.user_metadata?.privacy?.dataCollection !== false
  );
  const [shareUsageData, setShareUsageData] = useState(
    user?.user_metadata?.privacy?.shareUsageData !== false
  );

  // Handle going back
  const handleGoBack = () => {
    router.back();
  };

  // Handle updating profile
  const handleUpdateProfile = async () => {
    // Validate display name
    if (displayName.trim().length < 2) {
      setDisplayNameError('Name must be at least 2 characters');
      return;
    }

    try {
      // In a real app, you would update the user's profile in Supabase
      // For now, we'll just show a success message
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };

  // Handle changing password
  const handleChangePassword = async () => {
    if (!user?.email) {
      Alert.alert('Error', 'Unable to determine your email address');
      return;
    }

    try {
      // Show CAPTCHA modal and wait for verification
      try {
        // Show the CAPTCHA modal and get the token
        const captchaToken = await showCaptchaModal();
        console.log('CAPTCHA verified, proceeding with password reset');

        // Attempt password reset with the CAPTCHA token
        const result = await resetPassword(user.email, captchaToken);

        if (result.success) {
          Alert.alert(
            'Password Reset Email Sent',
            'We\'ve sent password reset instructions to your email. Please check your inbox and spam folder.',
            [{ text: 'OK' }]
          );
        } else {
          // If it fails with a CAPTCHA-related error, we might need to show a different message
          if (result.message.toLowerCase().includes('captcha')) {
            Alert.alert('Verification Failed', 'CAPTCHA verification failed. Please try again.');
          } else {
            Alert.alert('Error', result.message);
          }
        }
      } catch (captchaError) {
        // This will happen if the user cancels the CAPTCHA
        console.log('CAPTCHA verification cancelled or failed');
        Alert.alert('Password Reset Cancelled', 'CAPTCHA verification is required to reset your password.');
        return;
      }
    } catch (error) {
      console.error('Error sending password reset:', error);
      Alert.alert('Error', 'Failed to send password reset email. Please try again.');
    }
  };

  // Handle updating notification preferences
  const handleUpdateNotifications = async () => {
    try {
      // In a real app, you would update the user's preferences in Supabase
      // For now, we'll just show a success message
      Alert.alert('Success', 'Notification preferences updated successfully');
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      Alert.alert('Error', 'Failed to update notification preferences. Please try again.');
    }
  };

  // Handle updating privacy settings
  const handleUpdatePrivacy = async () => {
    try {
      // In a real app, you would update the user's privacy settings in Supabase
      // For now, we'll just show a success message
      Alert.alert('Success', 'Privacy settings updated successfully');
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      Alert.alert('Error', 'Failed to update privacy settings. Please try again.');
    }
  };

  // Handle account deletion
  const handleDeleteAccount = async () => {
    if (!user) {
      Alert.alert('Error', 'No user account found');
      return;
    }

    const authProvider = getAuthProvider(user, session);
    const isAppleUser = authProvider === 'apple';

    // Show confirmation dialog with appropriate message
    Alert.alert(
      'Delete Account',
      isAppleUser
        ? 'This will permanently delete your account and all associated data. Since you signed up with Apple, your Apple ID authorization will also be revoked.\n\nThis action cannot be undone.'
        : 'This will permanently delete your account and all associated data.\n\nThis action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: () => {
            // Second confirmation for extra safety
            Alert.alert(
              'Final Confirmation',
              'Are you absolutely sure you want to delete your account? This action is permanent and cannot be undone.',
              [
                {
                  text: 'Cancel',
                  style: 'cancel'
                },
                {
                  text: 'Yes, Delete My Account',
                  style: 'destructive',
                  onPress: performAccountDeletion
                }
              ]
            );
          }
        }
      ]
    );
  };

  // Perform the actual account deletion
  const performAccountDeletion = async () => {
    try {
      const result = await deleteAccount();

      if (result.success) {
        Alert.alert(
          'Account Deleted',
          result.message,
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to home screen
                router.replace('/');
              }
            }
          ]
        );
      } else {
        Alert.alert('Deletion Failed', result.message);
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again or contact support.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.title}>{t('accountSettings')}</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('profile')}</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Display Name</Text>
            <TextInput
              style={styles.input}
              value={displayName}
              onChangeText={(text) => {
                setDisplayName(text);
                setDisplayNameError('');
              }}
              placeholder="Enter your name"
            />
            {displayNameError ? (
              <Text style={styles.errorText}>{displayNameError}</Text>
            ) : null}
          </View>

          {/* Email display - TEMPORARILY HIDDEN */}
          {false && (
          <View style={styles.formGroup}>
            <Text style={styles.label}>Email</Text>
            <Text style={styles.emailText}>{user?.email}</Text>
            <Text style={styles.helperText}>
              Your email address is used for login and cannot be changed
            </Text>
          </View>
          )}

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleUpdateProfile}
          >
            <Text style={styles.actionButtonText}>Update Profile</Text>
          </TouchableOpacity>

          {/* Reset Password button - Only show for email/password users */}
          {canUserResetPassword && (
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={handleChangePassword}
            >
              <Text style={styles.secondaryButtonText}>Reset Password</Text>
            </TouchableOpacity>
          )}

          {/* Show authentication method info */}
          <View style={styles.infoContainer}>
            <Text style={styles.infoLabel}>Authentication Method:</Text>
            <Text style={styles.infoText}>{providerDisplayName}</Text>
            {!canUserResetPassword && (
              <Text style={styles.helperText}>
                Password reset is not available for {providerDisplayName} accounts
              </Text>
            )}
          </View>
        </View>

        {/* Notification Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Preferences</Text>

          <View style={styles.toggleItem}>
            <View style={styles.toggleTextContainer}>
              <Text style={styles.toggleLabel}>Email Notifications</Text>
              <Text style={styles.toggleDescription}>
                Receive important updates and information via email
              </Text>
            </View>
            <Switch
              value={emailNotifications}
              onValueChange={setEmailNotifications}
              trackColor={{ false: Colors.border, true: Colors.docPurple.DEFAULT }}
              thumbColor={Colors.white}
            />
          </View>

          <View style={styles.toggleItem}>
            <View style={styles.toggleTextContainer}>
              <Text style={styles.toggleLabel}>App Notifications</Text>
              <Text style={styles.toggleDescription}>
                Receive notifications within the app
              </Text>
            </View>
            <Switch
              value={appNotifications}
              onValueChange={setAppNotifications}
              trackColor={{ false: Colors.border, true: Colors.docPurple.DEFAULT }}
              thumbColor={Colors.white}
            />
          </View>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleUpdateNotifications}
          >
            <Text style={styles.actionButtonText}>Save Notification Settings</Text>
          </TouchableOpacity>
        </View>

        {/* Privacy & Security */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>

          <View style={styles.toggleItem}>
            <View style={styles.toggleTextContainer}>
              <Text style={styles.toggleLabel}>Data Collection</Text>
              <Text style={styles.toggleDescription}>
                Allow us to collect usage data to improve your experience
              </Text>
            </View>
            <Switch
              value={dataCollection}
              onValueChange={setDataCollection}
              trackColor={{ false: Colors.border, true: Colors.docPurple.DEFAULT }}
              thumbColor={Colors.white}
            />
          </View>

          <View style={styles.toggleItem}>
            <View style={styles.toggleTextContainer}>
              <Text style={styles.toggleLabel}>Share Usage Data</Text>
              <Text style={styles.toggleDescription}>
                Share anonymous usage data to help us improve our services
              </Text>
            </View>
            <Switch
              value={shareUsageData}
              onValueChange={setShareUsageData}
              trackColor={{ false: Colors.border, true: Colors.docPurple.DEFAULT }}
              thumbColor={Colors.white}
            />
          </View>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleUpdatePrivacy}
          >
            <Text style={styles.actionButtonText}>Save Privacy Settings</Text>
          </TouchableOpacity>
        </View>

        {/* Account Deletion Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Management</Text>

          <View style={styles.warningContainer}>
            <Ionicons name="warning" size={20} color={Colors.error} />
            <Text style={styles.warningText}>
              Deleting your account will permanently remove all your data and cannot be undone.
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDeleteAccount}
          >
            <Ionicons name="trash-outline" size={20} color={Colors.white} style={styles.deleteButtonIcon} />
            <Text style={styles.deleteButtonText}>Delete Account</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* CAPTCHA Modal */}
      <CaptchaModal
        visible={isCaptchaModalVisible}
        onVerify={handleCaptchaVerify}
        onClose={closeCaptchaModal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  section: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  input: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    padding: Spacing.sm,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  emailText: {
    fontSize: 16,
    color: Colors.textPrimary,
    padding: Spacing.sm,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
  },
  helperText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  errorText: {
    fontSize: 12,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  actionButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.docPurple.DEFAULT,
    marginTop: Spacing.sm,
  },
  secondaryButtonText: {
    color: Colors.docPurple.DEFAULT,
    fontSize: 16,
    fontWeight: '600',
  },
  toggleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    marginBottom: Spacing.sm,
  },
  toggleTextContainer: {
    flex: 1,
    marginRight: Spacing.md,
  },
  toggleLabel: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  toggleDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  infoContainer: {
    marginTop: Spacing.md,
    padding: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  infoText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.error + '10', // Light red background
    padding: Spacing.md,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: Colors.error,
    marginLeft: Spacing.sm,
    lineHeight: 20,
  },
  deleteButton: {
    backgroundColor: Colors.error,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButtonIcon: {
    marginRight: Spacing.sm,
  },
  deleteButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

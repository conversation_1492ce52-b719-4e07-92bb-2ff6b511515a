import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Platform, Linking, Modal } from 'react-native';
import Constants from 'expo-constants';
import { Text, Container, Header, Button, Divider } from '../components/ui';
import { Colors, Spacing } from '../constants/DesignSystem';
import { useLanguage } from '../contexts/LanguageContext';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { useAuth } from '../contexts/AuthContext';
import { FeatureType } from '../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { LEGAL_URLS } from '../constants/env';
import {
  initializeIAP,
  getProducts,
  purchaseSubscription,
  restorePurchases,
  checkSubscriptionStatus,
  resetAuthenticationState,
  getAuthenticationStatus,
  IAP_PRODUCT_IDS,
  IAP_PRODUCTS,
  formatPrice
} from '../services/appleIAPService';
// InAppPurchases will be imported dynamically in the service

export default function SubscriptionScreen() {
  const { t } = useLanguage();
  const { userTier, FEATURE_LIMITS } = useUsageLimits();
  const { user, refreshUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [iapInitialized, setIapInitialized] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string>('inactive');
  const [authenticationRetryCount, setAuthenticationRetryCount] = useState(0);
  const [showHelpModal, setShowHelpModal] = useState(false);

  // Check if user can purchase (free tier or expired subscription)
  const canPurchase = userTier === 'free' || subscriptionStatus === 'expired';

  // Check if we're in Expo Go (development mode)
  const isExpoGo = Constants.appOwnership === 'expo';

  // Handle opening legal links
  const handleOpenTerms = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.TERMS_OF_SERVICE);
      setShowHelpModal(false);
    } catch (error) {
      console.error('Error opening Terms of Service:', error);
      Alert.alert('Error', 'Unable to open Terms of Service. Please try again later.');
    }
  };

  const handleOpenPrivacy = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.PRIVACY_POLICY);
      setShowHelpModal(false);
    } catch (error) {
      console.error('Error opening Privacy Policy:', error);
      Alert.alert('Error', 'Unable to open Privacy Policy. Please try again later.');
    }
  };

  // Initialize IAP and load products when component mounts
  useEffect(() => {
    const initializeAndLoadProducts = async () => {
      if (Platform.OS !== 'ios') {
        console.log('Not on iOS, skipping IAP initialization');
        return;
      }

      try {
        console.log('Initializing Apple In-App Purchases...');

        const initialized = await initializeIAP();
        setIapInitialized(initialized);

        if (initialized) {
          console.log('Loading products from App Store...');
          const availableProducts = await getProducts();
          setProducts(availableProducts);
          console.log('Loaded', availableProducts.length, 'products');
        } else {
          console.log('IAP not available - likely running in Expo Go');
        }
      } catch (error) {
        console.error('Error initializing IAP:', error);
      }
    };

    initializeAndLoadProducts();
  }, []);

  // Check subscription status when component mounts (only once)
  useEffect(() => {
    if (user) {
      console.log('SubscriptionScreen mounted, current user tier:', user.subscription_tier);

      // Set initial subscription status based on user data
      if (user.subscription_tier && user.subscription_tier !== 'free') {
        setSubscriptionStatus('active');
      } else {
        setSubscriptionStatus('inactive');
      }

      // Check RevenueCat status for active subscriptions
      if (user.subscription_tier !== 'free') {
        console.log('Checking subscription status with RevenueCat...');
        const checkStatus = async () => {
          try {
            const status = await checkSubscriptionStatus(user.id);
            console.log('Subscription check response:', status);
            setSubscriptionStatus(status.status || 'active');

            if (status.status === 'expired' && user.subscription_tier !== 'free') {
              console.log('Subscription confirmed expired, refreshing user data...');
              await refreshUser();
            }
          } catch (error) {
            console.error('Error checking subscription status:', error);
            setSubscriptionStatus('active'); // Assume active if we can't check
          }
        };
        checkStatus();
      }
    }
  }, [user?.id]); // Only depend on user ID to avoid unnecessary re-runs

  // Function to manually refresh subscription status
  const handleRefreshSubscription = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsRefreshing(true);
      console.log('Manually refreshing subscription status...');
      console.log('Current user tier:', user.subscription_tier);

      // Check subscription status
      const status = await checkSubscriptionStatus(user.id);
      console.log('Subscription check response:', status);

      // Update local subscription status
      setSubscriptionStatus(status.status || 'inactive');

      // Refresh user data
      await refreshUser();
      console.log('User data refreshed, new tier:', user.subscription_tier);

      // Show success message
      Alert.alert(
        t('refreshComplete'),
        t('subscriptionStatusRefreshed'),
        [{ text: t('ok') }]
      );
    } catch (error) {
      console.error('Error refreshing subscription status:', error);
      Alert.alert(
        t('errorTitle'),
        t('refreshError'),
        [{ text: t('ok') }]
      );
    } finally {
      setIsRefreshing(false);
    }
  };



  // Function to display feature limit
  const getFeatureLimit = (tier: string, featureType: FeatureType) => {
    const tierLimits = FEATURE_LIMITS[tier as keyof typeof FEATURE_LIMITS];
    // Use type assertion to fix TypeScript error
    const limit = tierLimits[featureType as keyof typeof tierLimits];

    if (limit === Infinity) {
      return t('unlimited');
    } else if (limit === 0) {
      return t('notAvailable');
    } else if (featureType === FeatureType.LIVE_PILL_SCAN && tier === 'pro') {
      return "5-/day";
    } else {
      return t('perDay', { count: limit });
    }
  };

  // Function to handle authentication failures with retry
  const handleAuthenticationFailure = async (): Promise<boolean> => {
    console.log('Handling authentication failure, retry count:', authenticationRetryCount);

    if (authenticationRetryCount < 2) {
      setAuthenticationRetryCount(prev => prev + 1);

      // Reset authentication state and try to reinitialize
      resetAuthenticationState();

      try {
        console.log('Attempting to reinitialize IAP after authentication failure...');
        const reinitialized = await initializeIAP();
        setIapInitialized(reinitialized);

        if (reinitialized) {
          console.log('IAP reinitialized successfully');
          return true;
        }
      } catch (error) {
        console.error('Failed to reinitialize IAP:', error);
      }
    }

    return false;
  };

  // Function to handle subscription
  const handleSubscribe = async (tier: string) => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (Platform.OS !== 'ios') {
      Alert.alert(
        'Not Available',
        'In-App Purchases are only available on iOS devices.',
        [{ text: t('ok') }]
      );
      return;
    }

    // Handle development mode (Expo Go)
    if (isExpoGo) {
      Alert.alert(
        'Development Mode',
        'In-app purchases are not available in Expo Go. Please test in a production build (TestFlight) to test subscriptions.',
        [{ text: t('ok') }]
      );
      return;
    }

    if (!iapInitialized) {
      Alert.alert(
        t('errorTitle'),
        'In-App Purchases are not available. Please try again later.',
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);

      // Get the product ID based on the tier
      const productId = tier === 'pro' ? IAP_PRODUCT_IDS.PRO_MONTHLY : IAP_PRODUCT_IDS.PREMIUM_MONTHLY;

      // Validate product ID
      if (!productId || !Object.values(IAP_PRODUCT_IDS).includes(productId)) {
        Alert.alert(
          t('errorTitle'),
          `Invalid subscription tier: ${tier}. Please try again.`,
          [{ text: t('ok') }]
        );
        return;
      }

      // Make the purchase
      const result = await purchaseSubscription(productId, user.id);

      if (result.success) {
        Alert.alert(
          'Success!',
          result.message,
          [
            {
              text: t('ok'),
              onPress: async () => {
                // Wait a moment for database to update, then refresh user data
                setTimeout(async () => {
                  await refreshUser();
                }, 1000);
              }
            }
          ]
        );
      } else {
        // Check if this is an authentication-related error
        const isAuthError = result.message.includes('authenticate') ||
                           result.message.includes('authentication') ||
                           result.message.includes('login') ||
                           result.message.includes('credentials');

        if (isAuthError) {
          console.log('Authentication error detected, attempting recovery...');
          const recovered = await handleAuthenticationFailure();

          if (recovered) {
            console.log('Authentication recovered, retrying purchase automatically...');
            // Retry the purchase automatically instead of asking user to try again
            try {
              const retryResult = await purchaseSubscription(productId, user.id);

              if (retryResult.success) {
                Alert.alert(
                  'Success!',
                  retryResult.message,
                  [
                    {
                      text: t('ok'),
                      onPress: async () => {
                        setTimeout(async () => {
                          await refreshUser();
                        }, 1000);
                      }
                    }
                  ]
                );
                return;
              } else {
                Alert.alert(
                  t('errorTitle'),
                  `Purchase failed after authentication recovery: ${retryResult.message}`,
                  [{ text: t('ok') }]
                );
                return;
              }
            } catch (retryError) {
              console.error('Error during purchase retry:', retryError);
              Alert.alert(
                t('errorTitle'),
                'Purchase retry failed. Please try again manually.',
                [{ text: t('ok') }]
              );
              return;
            }
          }
        }

        // Only show sandbox tip for specific errors that might be sandbox-related
        const isSandboxError = result.message.includes('sandbox') ||
                              result.message.includes('test account') ||
                              result.message.includes('not available for purchase') ||
                              result.message.includes('configuration error');

        const errorMessage = isSandboxError
          ? `${result.message}\n\nTip: Use a sandbox test account in TestFlight`
          : result.message;

        Alert.alert(
          t('errorTitle'),
          errorMessage,
          [{ text: t('ok') }]
        );
      }
    } catch (error) {
      console.error('Error subscribing:', error);

      // Only show sandbox tip for specific errors
      const errorMsg = (error as any)?.message || 'Unknown error';
      const isSandboxError = errorMsg.includes('sandbox') ||
                            errorMsg.includes('test account') ||
                            errorMsg.includes('not available for purchase') ||
                            errorMsg.includes('configuration error');

      const finalMessage = isSandboxError
        ? `Purchase error: ${errorMsg}\n\nTip: Use a sandbox test account in TestFlight`
        : `Purchase error: ${errorMsg}`;

      Alert.alert(
        t('errorTitle'),
        finalMessage,
        [{ text: t('ok') }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to reset authentication (for troubleshooting)
  const handleResetAuthentication = async () => {
    Alert.alert(
      'Reset Authentication',
      'This will reset the purchase system authentication. Use this if you\'re experiencing persistent authentication issues.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              resetAuthenticationState();
              setAuthenticationRetryCount(0);

              // Reinitialize
              const reinitialized = await initializeIAP();
              setIapInitialized(reinitialized);

              if (reinitialized) {
                const availableProducts = await getProducts();
                setProducts(availableProducts);

                Alert.alert(
                  'Authentication Reset',
                  'Authentication has been reset successfully. Please try your purchase again.',
                  [{ text: t('ok') }]
                );
              } else {
                Alert.alert(
                  'Reset Failed',
                  'Failed to reset authentication. Please restart the app and try again.',
                  [{ text: t('ok') }]
                );
              }
            } catch (error) {
              console.error('Error resetting authentication:', error);
              Alert.alert(
                'Reset Failed',
                'Failed to reset authentication. Please restart the app and try again.',
                [{ text: t('ok') }]
              );
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  // Function to handle restoring purchases
  const handleRestorePurchases = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    if (Platform.OS !== 'ios') {
      Alert.alert(
        'Not Available',
        'Purchase restoration is only available on iOS devices.',
        [{ text: t('ok') }]
      );
      return;
    }

    try {
      setIsLoading(true);
      const result = await restorePurchases(user.id);

      Alert.alert(
        result.success ? 'Success!' : 'Restore Failed',
        result.message,
        [
          {
            text: t('ok'),
            onPress: async () => {
              if (result.success) {
                // Wait a moment for database to update, then refresh user data
                setTimeout(async () => {
                  await refreshUser();
                }, 1000);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error restoring purchases:', error);
      Alert.alert(
        t('errorTitle'),
        'Failed to restore purchases. Please try again.',
        [{ text: t('ok') }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle managing subscription (Apple IAP)
  const handleManageSubscription = async () => {
    if (!user) {
      Alert.alert(
        t('loginRequired'),
        t('loginRequiredMessage'),
        [{ text: t('ok') }]
      );
      return;
    }

    // For Apple IAP, subscription management is handled through iOS Settings
    Alert.alert(
      'Manage Subscription',
      'To manage your subscription, go to:\n\nSettings > Apple ID > Subscriptions > PillLogic\n\nYou can cancel, modify, or view your subscription details there.',
      [
        {
          text: 'Restore Purchases',
          onPress: handleRestorePurchases
        },
        {
          text: 'Refresh Status',
          onPress: handleRefreshSubscription
        },
        {
          text: 'Reset Auth',
          onPress: handleResetAuthentication,
          style: 'destructive'
        },
        {
          text: t('ok')
        }
      ]
    );
  };

  // Render a table cell with checkmark, X, or text
  const renderTableCell = (tier: string, featureType: FeatureType) => {
    const tierLimits = FEATURE_LIMITS[tier as keyof typeof FEATURE_LIMITS];
    // Use type assertion to fix TypeScript error
    const limit = tierLimits[featureType as keyof typeof tierLimits];

    if (limit === 0) {
      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          <Ionicons name="close" size={24} color={Colors.error} />
        </View>
      );
    } else if (limit === Infinity) {
      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          <Ionicons name="checkmark" size={24} color={Colors.success} />
        </View>
      );
    } else {
      let displayText = "";

      if (featureType === FeatureType.PILL_SCAN) {
        // For free tier, show "10-/day" instead of checkmark
        if (tier === 'free' && limit === 10) {
          displayText = "10-/day";
        } else {
          displayText = `${limit}/day`;
        }
      } else if (featureType === FeatureType.LIVE_PILL_SCAN) {
        displayText = tier === 'pro' ? "5-/day" : "✓";
      } else if (featureType === FeatureType.NOTE_ANALYSIS) {
        // For free tier, show "10-/day" instead of checkmark
        if (tier === 'free' && limit === 10) {
          displayText = "10-/day";
        } else {
          displayText = `${limit}/day`;
        }
      }

      return (
        <View style={[styles.tableCell, tier === 'premium' && styles.premiumCell]}>
          {displayText === "✓" ? (
            <Ionicons name="checkmark" size={24} color={Colors.success} />
          ) : (
            <Text variant="body" weight="medium">{displayText}</Text>
          )}
        </View>
      );
    }
  };

  return (
    <Container>
      <Header
        title={t('subscriptionTitle')}
        showBackButton
        rightComponent={
          <TouchableOpacity
            onPress={() => setShowHelpModal(true)}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="help-circle-outline" size={24} color={Colors.primary} />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.scrollContent} contentContainerStyle={styles.contentContainer}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text variant="h2" weight="bold" style={styles.mainTitle}>
            Stay on top of your meds – your way
          </Text>
          <Text variant="body" style={styles.subtitle}>
            {canPurchase ? "Choose the plan that works for you:" : "Here's what's included in each plan:"}
          </Text>

          {/* Show expired subscription notice */}
          {subscriptionStatus === 'expired' && (
            <View style={styles.expiredNoticeContainer}>
              <Text style={styles.expiredNoticeText}>
                ⚠️ Your subscription has expired. Renew to continue enjoying premium features.
              </Text>
            </View>
          )}
        </View>

        {/* Plan Table */}
        <View style={styles.tableContainer}>
          {/* Table Header */}
          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}></View>
            <View style={styles.tableCell}>
              <Text variant="subtitle" weight="semibold">Free</Text>
            </View>
            <View style={styles.tableCell}>
              <Text variant="subtitle" weight="semibold">Pro</Text>
            </View>
            <View style={[styles.tableCell, styles.premiumCell]}>
              <Text variant="subtitle" weight="semibold">Premium</Text>
            </View>
          </View>

          {/* Divider */}
          <View style={styles.tableDivider} />

          {/* Feature Rows */}
          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Pill Count</Text>
            </View>
            {renderTableCell('free', FeatureType.PILL_SCAN)}
            {renderTableCell('pro', FeatureType.PILL_SCAN)}
            {renderTableCell('premium', FeatureType.PILL_SCAN)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Live Count (Video)</Text>
            </View>
            {renderTableCell('free', FeatureType.LIVE_PILL_SCAN)}
            {renderTableCell('pro', FeatureType.LIVE_PILL_SCAN)}
            {renderTableCell('premium', FeatureType.LIVE_PILL_SCAN)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">Doctor Note Analyzing</Text>
            </View>
            {renderTableCell('free', FeatureType.NOTE_ANALYSIS)}
            {renderTableCell('pro', FeatureType.NOTE_ANALYSIS)}
            {renderTableCell('premium', FeatureType.NOTE_ANALYSIS)}
          </View>

          <View style={styles.tableDivider} />

          <View style={styles.tableRow}>
            <View style={styles.featureNameCell}>
              <Text variant="body" weight="medium">AI Updates & Support</Text>
            </View>
            <View style={styles.tableCell}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
            <View style={styles.tableCell}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
            <View style={[styles.tableCell, styles.premiumCell]}>
              <Ionicons name="checkmark" size={24} color={Colors.success} />
            </View>
          </View>
        </View>

        {/* Plan Details */}
        <Text variant="h3" weight="semibold" style={styles.choosePlanTitle}>
          Choose your plan:
        </Text>

        <View style={styles.planCardsContainer}>
          {/* Free Plan */}
          <View style={styles.planCard}>
            <Text variant="subtitle" weight="semibold">Free – $0</Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Stick with standard pill tracking. Basic and reliable.
            </Text>
            {userTier === 'free' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>

          {/* Pro Plan */}
          <View style={styles.planCard}>
            <Text variant="subtitle" weight="semibold">
              Pro – {products.find(p => p.product.identifier === IAP_PRODUCT_IDS.PRO_MONTHLY)?.product.priceString || '$4.99/month'}
            </Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Try limited live pill count. No note analysis.
            </Text>
            {userTier === 'pro' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>

          {/* Premium Plan */}
          <View style={[styles.planCard, styles.premiumPlanCard]}>
            <Text variant="subtitle" weight="semibold">
              Premium – {products.find(p => p.product.identifier === IAP_PRODUCT_IDS.PREMIUM_MONTHLY)?.product.priceString || '$5.99/month'}
            </Text>
            <Text variant="body" color="secondary" style={styles.planDescription}>
              Everything unlocked. Unlimited counts. Doctor note analysis. Always updated.
            </Text>
            {userTier === 'premium' && (
              <View style={styles.currentPlanTag}>
                <Text variant="caption" color="white">Current Plan</Text>
              </View>
            )}
          </View>
        </View>

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <Text variant="subtitle" weight="semibold" style={styles.ctaText}>
            Already using the app? Keep going without losing your flow.
          </Text>

          {/* Show IAP unavailable notice in Expo Go */}
          {Platform.OS === 'ios' && !iapInitialized && (
            <View style={styles.noticeContainer}>
              <Text style={styles.noticeText}>
                {isExpoGo
                  ? '📱 In-App Purchases are not available in Expo Go. To test subscriptions, build the app with EAS Build or submit to TestFlight.'
                  : '📱 In-App Purchases are not available. Please try again later.'
                }
              </Text>
            </View>
          )}



          {canPurchase ? (
            <View style={styles.buttonContainer}>
              <Button
                title={isLoading ? t('processing') : "Get Pro Plan"}
                variant="secondary"
                size="lg"
                style={styles.proButton}
                onPress={() => handleSubscribe('pro')}
                disabled={isLoading || (Platform.OS === 'ios' && !iapInitialized && !isExpoGo)}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </Button>

              <Button
                title={isLoading ? t('processing') : "Get Premium Plan"}
                variant="primary"
                size="lg"
                style={styles.premiumButton}
                onPress={() => handleSubscribe('premium')}
                disabled={isLoading || (Platform.OS === 'ios' && !iapInitialized && !isExpoGo)}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                )}
              </Button>
            </View>
          ) : (
            <>
              <Button
                title={isLoading ? t('processing') : t('manageSubscription')}
                variant="secondary"
                size="lg"
                style={styles.manageButton}
                onPress={handleManageSubscription}
                disabled={isLoading}
              >
                {isLoading && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </Button>

              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleRefreshSubscription}
                disabled={isRefreshing}
              >
                <Text style={styles.refreshButtonText}>
                  {isRefreshing ? 'Refreshing...' : 'Refresh Subscription Status'}
                </Text>
                {isRefreshing && (
                  <ActivityIndicator size="small" color={Colors.primary} style={styles.buttonLoader} />
                )}
              </TouchableOpacity>
              <Text style={styles.refreshHint}>
                If your subscription status is not showing correctly, try refreshing.
              </Text>
            </>
          )}
        </View>

        {/* Apple Required Subscription Information */}
        <View style={styles.subscriptionInfoSection}>
          <Text variant="h3" weight="semibold" style={styles.subscriptionInfoTitle}>
            Subscription Information
          </Text>

          {/* Subscription Details */}
          <View style={styles.subscriptionDetails}>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Pro Plan:</Text> Monthly auto-renewable subscription for $4.99/month
            </Text>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Premium Plan:</Text> Monthly auto-renewable subscription for $5.99/month
            </Text>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Subscription Length:</Text> 1 month (auto-renewing)
            </Text>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Payment:</Text> Charged to your Apple ID account at confirmation of purchase
            </Text>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Auto-Renewal:</Text> Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period
            </Text>
            <Text variant="body" style={styles.subscriptionDetailText}>
              <Text weight="semibold">Management:</Text> You can manage and cancel your subscriptions by going to your Account Settings on your device
            </Text>
          </View>

          {/* Legal Links */}
          <View style={styles.legalLinksContainer}>
            <TouchableOpacity
              style={styles.legalLink}
              onPress={() => Linking.openURL(LEGAL_URLS.TERMS_OF_SERVICE)}
            >
              <Text style={styles.legalLinkText}>Terms of Use (EULA)</Text>
              <Ionicons name="open-outline" size={16} color={Colors.primary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.legalLink}
              onPress={() => Linking.openURL(LEGAL_URLS.PRIVACY_POLICY)}
            >
              <Text style={styles.legalLinkText}>Privacy Policy</Text>
              <Ionicons name="open-outline" size={16} color={Colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Help Modal */}
      <Modal
        visible={showHelpModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowHelpModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowHelpModal(false)}
        >
          <View style={styles.helpModal}>
            <Text style={styles.modalTitle}>App Information</Text>

            {/* Subscription Information */}
            <View style={styles.subscriptionInfoModalSection}>
              <Text style={styles.sectionTitle}>Subscription Plans</Text>
              <Text style={styles.subscriptionModalText}>• Pro Plan: $4.99/month (auto-renewable)</Text>
              <Text style={styles.subscriptionModalText}>• Premium Plan: $5.99/month (auto-renewable)</Text>
              <Text style={styles.subscriptionModalText}>• Length: 1 month, automatically renews</Text>
              <Text style={styles.subscriptionModalText}>• Manage in iOS Settings > Apple ID > Subscriptions</Text>
            </View>

            {/* Legal Links */}
            <View style={styles.legalModalSection}>
              <Text style={styles.sectionTitle}>Legal Documents</Text>

              <TouchableOpacity
                style={styles.legalModalOption}
                onPress={handleOpenTerms}
              >
                <Ionicons name="document-text-outline" size={24} color={Colors.primary} />
                <Text style={styles.legalModalOptionText}>Terms of Use (EULA)</Text>
                <Ionicons name="open-outline" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.legalModalOption}
                onPress={handleOpenPrivacy}
              >
                <Ionicons name="shield-checkmark-outline" size={24} color={Colors.primary} />
                <Text style={styles.legalModalOptionText}>Privacy Policy</Text>
                <Ionicons name="open-outline" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={styles.closeModalButton}
              onPress={() => setShowHelpModal(false)}
            >
              <Text style={styles.closeModalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </Container>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
    paddingTop: Spacing.md,
    paddingHorizontal: Spacing.md,
    backgroundColor: '#FFFAF5', // Light cream background like in the inspiration
  },
  headerContainer: {
    marginBottom: Spacing.lg,
    paddingTop: Spacing.md,
  },
  mainTitle: {
    fontSize: 28,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: Spacing.lg,
    color: Colors.textSecondary,
  },
  tableContainer: {
    marginBottom: Spacing.xl,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableDivider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    width: '100%',
  },
  featureNameCell: {
    flex: 1.5,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    justifyContent: 'center',
  },
  tableCell: {
    flex: 1,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  premiumCell: {
    backgroundColor: '#EEEAF9', // Light purple for premium column
  },
  choosePlanTitle: {
    marginBottom: Spacing.md,
    marginTop: Spacing.lg,
  },
  planCardsContainer: {
    marginBottom: Spacing.xl,
  },
  planCard: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: 'white',
    position: 'relative',
  },
  premiumPlanCard: {
    backgroundColor: '#EEEAF9', // Light purple for premium
  },
  planDescription: {
    marginTop: Spacing.xs,
  },
  currentPlanTag: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.success,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: 12,
  },
  ctaSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  ctaText: {
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  buttonContainer: {
    width: '100%',
    gap: Spacing.md,
  },
  proButton: {
    width: '100%',
    borderRadius: 25,
    borderColor: Colors.primary,
    paddingVertical: Spacing.md,
  },
  premiumButton: {
    width: '100%',
    borderRadius: 25,
    backgroundColor: '#6C5CE7', // Purple button like in the inspiration
    paddingVertical: Spacing.md,
  },
  buttonLoader: {
    marginLeft: Spacing.sm,
  },
  manageButton: {
    width: '100%',
    borderRadius: 25,
    borderColor: Colors.primary,
    paddingVertical: Spacing.md,
  },
  refreshButton: {
    marginTop: Spacing.md,
    padding: Spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButtonText: {
    color: Colors.primary,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  refreshHint: {
    marginTop: Spacing.xs,
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  noticeContainer: {
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
    borderWidth: 1,
    borderRadius: 8,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
  },
  noticeText: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
    lineHeight: 20,
  },
  expiredNoticeContainer: {
    backgroundColor: '#FFE6E6',
    borderColor: '#FF9999',
    borderWidth: 1,
    borderRadius: 8,
    padding: Spacing.md,
    marginTop: Spacing.md,
    marginBottom: Spacing.lg,
  },
  expiredNoticeText: {
    fontSize: 14,
    color: '#CC0000',
    textAlign: 'center',
    lineHeight: 20,
    fontWeight: '600',
  },
  // Apple Required Subscription Information Styles
  subscriptionInfoSection: {
    marginTop: Spacing.xl,
    paddingTop: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  subscriptionInfoTitle: {
    fontSize: 20,
    textAlign: 'center',
    marginBottom: Spacing.md,
    color: Colors.textPrimary,
  },
  subscriptionDetails: {
    marginBottom: Spacing.lg,
  },
  subscriptionDetailText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: Spacing.sm,
    color: Colors.textSecondary,
  },
  legalLinksContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: Spacing.md,
  },
  legalLink: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  legalLinkText: {
    fontSize: 14,
    color: Colors.primary,
    marginRight: Spacing.xs,
    fontWeight: '500',
  },
  // Help Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  helpModal: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: Spacing.lg,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  subscriptionInfoModalSection: {
    marginBottom: Spacing.lg,
    paddingBottom: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  legalModalSection: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  subscriptionModalText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
  legalModalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    minHeight: 50,
  },
  legalModalOptionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginLeft: Spacing.md,
  },
  closeModalButton: {
    backgroundColor: Colors.primary,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeModalButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  subscription_tier TEXT DEFAULT 'free',
  subscription_start_date TIMESTAMP WITH TIME ZONE,
  subscription_end_date TIMESTAMP WITH TIME ZONE
);

-- Create RLS policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own profile
CREATE POLICY "Users can view their own profile" 
  ON profiles 
  FOR SELECT 
  USING (auth.uid() = id);

-- Create policy to allow users to update their own profile
CREATE POLICY "Users can update their own profile" 
  ON profiles 
  FOR UPDATE 
  USING (auth.uid() = id);

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, created_at)
  VALUES (new.id, new.email, new.created_at);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create feature_usage table to track detailed usage
CREATE TABLE IF NOT EXISTS feature_usage (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  feature_name TEXT NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for feature_usage
ALTER TABLE feature_usage ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own usage
CREATE POLICY "Users can view their own feature usage" 
  ON feature_usage 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own usage
CREATE POLICY "Users can insert their own feature usage" 
  ON feature_usage 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create function to increment usage count
CREATE OR REPLACE FUNCTION public.increment_usage_count(user_uuid UUID, feature TEXT) 
RETURNS VOID AS $$
BEGIN
  -- Insert feature usage record
  INSERT INTO public.feature_usage (user_id, feature_name)
  VALUES (user_uuid, feature);
  
  -- Update profile usage count
  UPDATE public.profiles
  SET 
    usage_count = usage_count + 1,
    last_used_at = NOW(),
    updated_at = NOW()
  WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

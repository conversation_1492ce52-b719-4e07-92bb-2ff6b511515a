// Test script for SMTP connection
const nodemailer = require('nodemailer');

// Create a transporter with the new API key
const transporter = nodemailer.createTransport({
  host: 'smtp.resend.com',
  port: 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: 'resend',
    pass: 're_hUY21vWT_C6AKxToaSavFfvave8uYHt55',
  },
});

async function testSMTPConnection() {
  try {
    console.log('Testing SMTP connection...');
    
    // Verify connection
    const connectionResult = await transporter.verify();
    console.log('SMTP Connection verified:', connectionResult);
    
    // Send a test email
    const info = await transporter.sendMail({
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'SMTP Test Email from PillLogic',
      html: '<p>This is a test email to verify that the SMTP connection is working correctly.</p>'
    });
    
    console.log('Email sent successfully!');
    console.log('Message ID:', info.messageId);
  } catch (error) {
    console.error('Error testing SMTP connection:', error);
  }
}

// Run the test
testSMTPConnection();

import React, { useState } from 'react';
import { StyleSheet, View, Text, SafeAreaView, StatusBar, Platform } from 'react-native';
import CameraViewComponent from '@/components/CameraView';
import ImageAnalysis from '@/components/ImageAnalysis';

export default function CameraScreen() {
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  const handleImageCaptured = (imageUri: string) => {
    setCapturedImage(imageUri);
  };

  const handleRetake = () => {
    setCapturedImage(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      {capturedImage ? (
        <ImageAnalysis imageUri={capturedImage} onRetake={handleRetake} />
      ) : (
        <CameraViewComponent onImageCaptured={handleImageCaptured} />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
});

// Minimal implementation of the Node.js https module for React Native
const http = require('./http');
const { EventEmitter } = require('./events');

// Just re-export http with a few HTTPS-specific additions
module.exports = {
  ...http,
  createServer: (options, requestListener) => {
    return http.createServer(requestListener);
  },
  get: (url, options, callback) => {
    throw new Error('https.get is not implemented in this environment');
  },
  request: (url, options, callback) => {
    throw new Error('https.request is not implemented in this environment');
  },
  Agent: class Agent extends EventEmitter {
    constructor(options) {
      super();
      this.options = options || {};
    }
  },
  globalAgent: new (class Agent extends EventEmitter {
    constructor() {
      super();
    }
  })()
};

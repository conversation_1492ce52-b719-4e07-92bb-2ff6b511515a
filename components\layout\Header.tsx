import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, Linking, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Colors, Spacing, BorderRadius } from '../../constants/DocAidDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { Language } from '../../services/languageTypes';
import { LEGAL_URLS } from '../../constants/env';

const Header: React.FC = () => {
  const router = useRouter();
  const { language, setLanguage, t } = useLanguage();
  const { user } = useAuth();
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);

  const languages = [
    { code: Language.ENGLISH, name: 'English', flag: '🇺🇸' },
    { code: Language.VIETNAMESE, name: '<PERSON>iế<PERSON> Việt', flag: '🇻🇳' },
    { code: Language.HINDI, name: 'हिन्दी', flag: '🇮🇳' },
    { code: Language.CHINESE, name: '中文', flag: '🇨🇳' },
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  // Handle opening legal links
  const handleOpenTerms = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.TERMS_OF_SERVICE);
      setShowHelpModal(false);
    } catch (error) {
      console.error('Error opening Terms of Service:', error);
      Alert.alert('Error', 'Unable to open Terms of Service. Please try again later.');
    }
  };

  const handleOpenPrivacy = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.PRIVACY_POLICY);
      setShowHelpModal(false);
    } catch (error) {
      console.error('Error opening Privacy Policy:', error);
      Alert.alert('Error', 'Unable to open Privacy Policy. Please try again later.');
    }
  };

  return (
    <>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.logoContainer}
          onPress={() => router.push('/')}
        >
          <Text style={styles.logoText}>PillLogic</Text>
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => router.push(user ? '/profile' : '/auth/login')}
          >
            {user ? (
              <View style={styles.userAvatar}>
                <Text style={styles.userInitial}>{user.email.charAt(0).toUpperCase()}</Text>
              </View>
            ) : (
              <Ionicons name="person-circle-outline" size={28} color={Colors.docPurple.DEFAULT} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.languageButton}
            onPress={() => setShowLanguageModal(true)}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            activeOpacity={0.7}
          >
            <Text style={styles.languageFlag}>{currentLanguage?.flag}</Text>
            <Text style={styles.languageCode}>{currentLanguage?.code.toUpperCase()}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.helpButton}
            onPress={() => setShowHelpModal(true)}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            activeOpacity={0.7}
          >
            <Ionicons name="help-circle-outline" size={28} color={Colors.docPurple.DEFAULT} />
          </TouchableOpacity>
        </View>
      </View>

      <Modal
        visible={showLanguageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowLanguageModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowLanguageModal(false)}
        >
          <View style={styles.languageModal}>
            <Text style={styles.modalTitle}>{t('selectLanguage')}</Text>

            {languages.map((lang) => (
              <TouchableOpacity
                key={lang.code}
                style={[
                  styles.languageOption,
                  language === lang.code && styles.selectedLanguage
                ]}
                onPress={() => {
                  setLanguage(lang.code);
                  setShowLanguageModal(false);
                }}
              >
                <Text style={styles.languageFlag}>{lang.flag}</Text>
                <Text style={styles.languageName}>{lang.name}</Text>
                {language === lang.code && (
                  <Ionicons name="checkmark" size={20} color={Colors.docPurple.DEFAULT} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Help Modal */}
      <Modal
        visible={showHelpModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowHelpModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowHelpModal(false)}
        >
          <View style={styles.helpModal}>
            <Text style={styles.modalTitle}>App Information</Text>

            {/* Subscription Information */}
            <View style={styles.subscriptionSection}>
              <Text style={styles.sectionTitle}>Subscription Plans</Text>
              <Text style={styles.subscriptionText}>• Pro Plan: $4.99/month (auto-renewable)</Text>
              <Text style={styles.subscriptionText}>• Premium Plan: $5.99/month (auto-renewable)</Text>
              <Text style={styles.subscriptionText}>• Length: 1 month, automatically renews</Text>
              <Text style={styles.subscriptionText}>• Manage in iOS Settings > Apple ID > Subscriptions</Text>
            </View>

            {/* Legal Links */}
            <View style={styles.legalSection}>
              <Text style={styles.sectionTitle}>Legal Documents</Text>

              <TouchableOpacity
                style={styles.legalOption}
                onPress={handleOpenTerms}
              >
                <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
                <Text style={styles.legalOptionText}>Terms of Use (EULA)</Text>
                <Ionicons name="open-outline" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.legalOption}
                onPress={handleOpenPrivacy}
              >
                <Ionicons name="shield-checkmark-outline" size={24} color={Colors.docPurple.DEFAULT} />
                <Text style={styles.legalOptionText}>Privacy Policy</Text>
                <Ionicons name="open-outline" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowHelpModal(false)}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    position: 'absolute',
    top: 40, // Move header much lower to make it more accessible
    left: 0,
    right: 0,
    height: 70, // Reasonable height
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg, // Increased horizontal padding
    paddingBottom: 10, // Padding at the bottom
    paddingTop: 10, // Padding at the top
    zIndex: 10,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.docPurple.DEFAULT,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileButton: {
    padding: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
    minHeight: 40,
    marginRight: Spacing.sm,
  },
  helpButton: {
    padding: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
    minHeight: 40,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.docPurple.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInitial: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    minWidth: 70,
    minHeight: 40,
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  languageFlag: {
    fontSize: 20,
    marginRight: 6,
  },
  languageCode: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.docPurple.DEFAULT,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  languageModal: {
    width: '80%',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.lg, // More vertical padding for easier tapping
    paddingHorizontal: Spacing.md, // Added horizontal padding
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    minHeight: 60, // Minimum height for better touch target
  },
  selectedLanguage: {
    backgroundColor: Colors.docPurple.light + '40', // More visible selection
    borderRadius: BorderRadius.sm,
  },
  languageName: {
    flex: 1,
    fontSize: 18, // Larger text
    fontWeight: '500',
    color: Colors.textPrimary,
    marginLeft: Spacing.md, // More spacing
  },
  // Help Modal Styles
  helpModal: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.lg,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  subscriptionSection: {
    marginBottom: Spacing.lg,
    paddingBottom: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  legalSection: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  subscriptionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
  legalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    minHeight: 60,
  },
  legalOptionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginLeft: Spacing.md,
  },
  closeButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.lg,
    alignItems: 'center',
  },
  closeButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Header;

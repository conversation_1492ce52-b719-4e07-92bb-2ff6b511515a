import React from 'react';
import { View, ScrollView, StyleSheet, SafeAreaView, ViewStyle, StatusBar, Platform } from 'react-native';
import { Colors, Spacing } from '../../constants/DocAidDesign';

interface ContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  scrollable?: boolean;
  padded?: boolean;
  centered?: boolean;
  safeArea?: boolean;
}

const Container: React.FC<ContainerProps> = ({
  children,
  style,
  contentContainerStyle,
  scrollable = true,
  padded = true,
  centered = false,
  safeArea = true,
}) => {
  const containerStyle = [
    styles.container,
    style,
  ];

  const contentStyle = [
    padded && styles.padded,
    centered && styles.centered,
    contentContainerStyle,
  ];

  const content = scrollable ? (
    <ScrollView
      style={containerStyle}
      contentContainerStyle={contentStyle}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      {children}
    </ScrollView>
  ) : (
    <View style={[containerStyle, padded && styles.padded, centered && styles.centered]}>
      {children}
    </View>
  );

  if (safeArea) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
        {content}
      </SafeAreaView>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  padded: {
    padding: Spacing.md,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Container;

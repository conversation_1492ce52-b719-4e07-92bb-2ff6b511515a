-- Migration to replace <PERSON><PERSON> with Apple In-App Purchase
-- This migration adds Apple IAP fields and removes Stripe-specific fields

-- Add Apple IAP fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS apple_transaction_id TEXT,
ADD COLUMN IF NOT EXISTS apple_product_id TEXT,
ADD COLUMN IF NOT EXISTS apple_receipt_data TEXT,
ADD COLUMN IF NOT EXISTS subscription_expires_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_receipt_validation TIMESTAMP WITH TIME ZONE;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_apple_transaction_id ON public.profiles(apple_transaction_id);
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_expires_at ON public.profiles(subscription_expires_at);

-- Create function to update Apple subscription
CREATE OR REPLACE FUNCTION public.update_apple_subscription(
  user_uuid UUID,
  product_id TEXT,
  transaction_id TEXT,
  receipt_data TEXT,
  subscription_tier TEXT,
  purchase_date TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Calculate expiration date (30 days from purchase for monthly subscriptions)
  expires_at := (purchase_date::TIMESTAMP WITH TIME ZONE) + INTERVAL '30 days';
  
  -- Update user's subscription
  UPDATE public.profiles
  SET 
    apple_transaction_id = transaction_id,
    apple_product_id = product_id,
    apple_receipt_data = receipt_data,
    subscription_tier = subscription_tier,
    subscription_status = 'active',
    subscription_start_date = purchase_date::TIMESTAMP WITH TIME ZONE,
    subscription_end_date = expires_at,
    subscription_expires_at = expires_at,
    last_receipt_validation = NOW(),
    updated_at = NOW()
  WHERE id = user_uuid;
  
  -- Return success if row was updated
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get Apple subscription status
CREATE OR REPLACE FUNCTION public.get_apple_subscription_status(
  user_uuid UUID
)
RETURNS JSON AS $$
DECLARE
  user_record RECORD;
  result JSON;
BEGIN
  -- Get user's subscription info
  SELECT 
    subscription_tier,
    subscription_status,
    subscription_expires_at,
    apple_transaction_id,
    apple_product_id
  INTO user_record
  FROM public.profiles
  WHERE id = user_uuid;
  
  -- If no user found, return free tier
  IF NOT FOUND THEN
    result := json_build_object(
      'subscription_tier', 'free',
      'status', 'inactive'
    );
    RETURN result;
  END IF;
  
  -- Check if subscription is expired
  IF user_record.subscription_expires_at IS NOT NULL AND 
     user_record.subscription_expires_at < NOW() THEN
    -- Update to free tier if expired
    UPDATE public.profiles
    SET 
      subscription_tier = 'free',
      subscription_status = 'expired',
      updated_at = NOW()
    WHERE id = user_uuid;
    
    result := json_build_object(
      'subscription_tier', 'free',
      'status', 'expired',
      'expired_at', user_record.subscription_expires_at
    );
  ELSE
    -- Return current subscription status
    result := json_build_object(
      'subscription_tier', COALESCE(user_record.subscription_tier, 'free'),
      'status', COALESCE(user_record.subscription_status, 'inactive'),
      'expires_at', user_record.subscription_expires_at,
      'product_id', user_record.apple_product_id,
      'transaction_id', user_record.apple_transaction_id
    );
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate Apple receipt (placeholder for future implementation)
CREATE OR REPLACE FUNCTION public.validate_apple_receipt(
  receipt_data TEXT,
  user_uuid UUID
)
RETURNS JSON AS $$
BEGIN
  -- This is a placeholder function
  -- In production, you would validate the receipt with Apple's servers
  -- For now, we'll assume the receipt is valid
  
  RETURN json_build_object(
    'valid', true,
    'message', 'Receipt validation not implemented yet'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing update_subscription_status function to handle Apple IAP
CREATE OR REPLACE FUNCTION public.update_subscription_status(
  user_uuid UUID,
  stripe_subscription_id TEXT DEFAULT NULL,
  status TEXT DEFAULT 'active',
  period_start TEXT DEFAULT NULL,
  period_end TEXT DEFAULT NULL,
  subscription_tier TEXT DEFAULT 'free',
  -- New Apple IAP parameters
  apple_transaction_id TEXT DEFAULT NULL,
  apple_product_id TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update user's subscription (supporting both Stripe legacy and Apple IAP)
  UPDATE public.profiles
  SET 
    stripe_subscription_id = COALESCE(stripe_subscription_id, profiles.stripe_subscription_id),
    apple_transaction_id = COALESCE(apple_transaction_id, profiles.apple_transaction_id),
    apple_product_id = COALESCE(apple_product_id, profiles.apple_product_id),
    subscription_status = status,
    subscription_tier = subscription_tier,
    subscription_start_date = CASE 
      WHEN period_start IS NOT NULL THEN period_start::TIMESTAMP WITH TIME ZONE
      ELSE profiles.subscription_start_date
    END,
    subscription_end_date = CASE 
      WHEN period_end IS NOT NULL THEN period_end::TIMESTAMP WITH TIME ZONE
      ELSE profiles.subscription_end_date
    END,
    subscription_expires_at = CASE 
      WHEN period_end IS NOT NULL THEN period_end::TIMESTAMP WITH TIME ZONE
      ELSE profiles.subscription_expires_at
    END,
    updated_at = NOW()
  WHERE id = user_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.update_apple_subscription TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_apple_subscription_status TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_apple_receipt TO authenticated;

-- Create a cleanup function to remove old Stripe data (optional, run manually when ready)
CREATE OR REPLACE FUNCTION public.cleanup_stripe_data()
RETURNS VOID AS $$
BEGIN
  -- This function can be called manually to remove Stripe fields
  -- Only run this after confirming Apple IAP is working properly
  
  -- ALTER TABLE public.profiles DROP COLUMN IF EXISTS stripe_customer_id;
  -- ALTER TABLE public.profiles DROP COLUMN IF EXISTS stripe_subscription_id;
  -- ALTER TABLE public.profiles DROP COLUMN IF EXISTS payment_method_id;
  
  RAISE NOTICE 'Stripe cleanup function created but not executed. Run manually when ready.';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

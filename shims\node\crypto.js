// Minimal implementation of the Node.js crypto module for React Native
const EventEmitter = require('./events');

// Simple implementation of getRandomValues
function getRandomValues(arr) {
  for (let i = 0; i < arr.length; i++) {
    arr[i] = Math.floor(Math.random() * 256);
  }
  return arr;
}

// Simple hash function
function hash(algorithm, data) {
  // This is not a real hash function, just a placeholder
  let hash = 0;
  if (typeof data === 'string') {
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
  } else if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {
    const view = data instanceof ArrayBuffer ? new Uint8Array(data) : data;
    for (let i = 0; i < view.length; i++) {
      hash = ((hash << 5) - hash) + view[i];
      hash = hash & hash; // Convert to 32bit integer
    }
  }
  return new Uint8Array([hash & 0xff, (hash >> 8) & 0xff, (hash >> 16) & 0xff, (hash >> 24) & 0xff]);
}

class Hash extends EventEmitter {
  constructor(algorithm) {
    super();
    this.algorithm = algorithm;
    this.data = [];
  }

  update(data, inputEncoding) {
    if (typeof data === 'string') {
      const encoder = new TextEncoder();
      this.data.push(encoder.encode(data));
    } else {
      this.data.push(data);
    }
    return this;
  }

  digest(encoding) {
    // Combine all data
    let combinedLength = 0;
    for (const chunk of this.data) {
      combinedLength += chunk.length;
    }
    const combined = new Uint8Array(combinedLength);
    let offset = 0;
    for (const chunk of this.data) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }

    // Generate hash
    const hashValue = hash(this.algorithm, combined);

    if (encoding === 'hex') {
      return Array.from(hashValue)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    } else if (encoding === 'base64') {
      return btoa(String.fromCharCode.apply(null, hashValue));
    } else {
      return hashValue;
    }
  }
}

function createHash(algorithm) {
  return new Hash(algorithm);
}

class Hmac extends Hash {
  constructor(algorithm, key) {
    super(algorithm);
    this.key = key;
  }
}

function createHmac(algorithm, key) {
  return new Hmac(algorithm, key);
}

class Cipher extends EventEmitter {
  constructor(algorithm, key, iv) {
    super();
    this.algorithm = algorithm;
    this.key = key;
    this.iv = iv;
    this.data = [];
  }

  update(data, inputEncoding, outputEncoding) {
    if (typeof data === 'string') {
      const encoder = new TextEncoder();
      this.data.push(encoder.encode(data));
    } else {
      this.data.push(data);
    }

    if (outputEncoding) {
      // This is a placeholder, not real encryption
      return '';
    }
    return Buffer.from([]);
  }

  final(outputEncoding) {
    // This is a placeholder, not real encryption
    if (outputEncoding) {
      return '';
    }
    return Buffer.from([]);
  }
}

function createCipheriv(algorithm, key, iv) {
  return new Cipher(algorithm, key, iv);
}

class Decipher extends EventEmitter {
  constructor(algorithm, key, iv) {
    super();
    this.algorithm = algorithm;
    this.key = key;
    this.iv = iv;
    this.data = [];
  }

  update(data, inputEncoding, outputEncoding) {
    if (typeof data === 'string') {
      let buffer;
      if (inputEncoding === 'hex') {
        buffer = new Uint8Array(data.length / 2);
        for (let i = 0; i < data.length; i += 2) {
          buffer[i / 2] = parseInt(data.substr(i, 2), 16);
        }
      } else if (inputEncoding === 'base64') {
        const binary = atob(data);
        buffer = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
          buffer[i] = binary.charCodeAt(i);
        }
      } else {
        const encoder = new TextEncoder();
        buffer = encoder.encode(data);
      }
      this.data.push(buffer);
    } else {
      this.data.push(data);
    }

    if (outputEncoding) {
      // This is a placeholder, not real decryption
      return '';
    }
    return Buffer.from([]);
  }

  final(outputEncoding) {
    // This is a placeholder, not real decryption
    if (outputEncoding) {
      return '';
    }
    return Buffer.from([]);
  }
}

function createDecipheriv(algorithm, key, iv) {
  return new Decipher(algorithm, key, iv);
}

// Simple Buffer implementation
class Buffer extends Uint8Array {
  static from(value, encoding) {
    if (typeof value === 'string') {
      if (encoding === 'hex') {
        const buffer = new Buffer(value.length / 2);
        for (let i = 0; i < value.length; i += 2) {
          buffer[i / 2] = parseInt(value.substr(i, 2), 16);
        }
        return buffer;
      } else if (encoding === 'base64') {
        const binary = atob(value);
        const buffer = new Buffer(binary.length);
        for (let i = 0; i < binary.length; i++) {
          buffer[i] = binary.charCodeAt(i);
        }
        return buffer;
      } else {
        const encoder = new TextEncoder();
        return new Buffer(encoder.encode(value));
      }
    } else if (Array.isArray(value)) {
      return new Buffer(value);
    } else if (value instanceof ArrayBuffer || ArrayBuffer.isView(value)) {
      return new Buffer(value);
    }
    return new Buffer(0);
  }

  static alloc(size, fill, encoding) {
    const buffer = new Buffer(size);
    if (fill !== undefined) {
      if (typeof fill === 'string') {
        const fillBuffer = Buffer.from(fill, encoding);
        for (let i = 0; i < size; i++) {
          buffer[i] = fillBuffer[i % fillBuffer.length];
        }
      } else if (typeof fill === 'number') {
        buffer.fill(fill);
      }
    }
    return buffer;
  }

  static allocUnsafe(size) {
    return new Buffer(size);
  }

  static isBuffer(obj) {
    return obj instanceof Buffer;
  }

  static byteLength(string, encoding) {
    if (encoding === 'hex') {
      return string.length / 2;
    } else if (encoding === 'base64') {
      return Math.floor(string.length * 3 / 4);
    } else {
      return new TextEncoder().encode(string).length;
    }
  }

  static concat(list, totalLength) {
    if (totalLength === undefined) {
      totalLength = list.reduce((acc, buf) => acc + buf.length, 0);
    }
    const result = new Buffer(totalLength);
    let offset = 0;
    for (const buf of list) {
      result.set(buf, offset);
      offset += buf.length;
    }
    return result;
  }

  toString(encoding, start, end) {
    if (start === undefined) start = 0;
    if (end === undefined) end = this.length;
    const slice = this.slice(start, end);
    
    if (encoding === 'hex') {
      return Array.from(slice)
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    } else if (encoding === 'base64') {
      return btoa(String.fromCharCode.apply(null, slice));
    } else {
      return new TextDecoder().decode(slice);
    }
  }

  write(string, offset, length, encoding) {
    if (offset === undefined) offset = 0;
    if (typeof length === 'string') {
      encoding = length;
      length = undefined;
    }
    if (length === undefined) length = this.length - offset;
    
    let buffer;
    if (encoding === 'hex') {
      buffer = Buffer.from(string, 'hex');
    } else if (encoding === 'base64') {
      buffer = Buffer.from(string, 'base64');
    } else {
      buffer = Buffer.from(string);
    }
    
    const actualLength = Math.min(length, buffer.length);
    this.set(buffer.slice(0, actualLength), offset);
    return actualLength;
  }

  toJSON() {
    return {
      type: 'Buffer',
      data: Array.from(this)
    };
  }
}

module.exports = {
  createHash,
  createHmac,
  createCipheriv,
  createDecipheriv,
  randomBytes: (size) => {
    const arr = new Uint8Array(size);
    return Buffer.from(getRandomValues(arr));
  },
  randomFillSync: (buffer, offset, size) => {
    if (offset === undefined) offset = 0;
    if (size === undefined) size = buffer.length - offset;
    const arr = buffer.subarray(offset, offset + size);
    getRandomValues(arr);
    return buffer;
  },
  getRandomValues,
  Hash,
  Hmac,
  Cipher,
  Decipher,
  constants: {
    OPENSSL_VERSION_NUMBER: 0,
    SSL_OP_ALL: 0,
    SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION: 0,
    SSL_OP_CIPHER_SERVER_PREFERENCE: 0,
    SSL_OP_CISCO_ANYCONNECT: 0,
    SSL_OP_COOKIE_EXCHANGE: 0,
    SSL_OP_CRYPTOPRO_TLSEXT_BUG: 0,
    SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS: 0,
    SSL_OP_EPHEMERAL_RSA: 0,
    SSL_OP_LEGACY_SERVER_CONNECT: 0,
    SSL_OP_MICROSOFT_BIG_SSLV3_BUFFER: 0,
    SSL_OP_MICROSOFT_SESS_ID_BUG: 0,
    SSL_OP_MSIE_SSLV2_RSA_PADDING: 0,
    SSL_OP_NETSCAPE_CA_DN_BUG: 0,
    SSL_OP_NETSCAPE_CHALLENGE_BUG: 0,
    SSL_OP_NETSCAPE_DEMO_CIPHER_CHANGE_BUG: 0,
    SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG: 0,
    SSL_OP_NO_COMPRESSION: 0,
    SSL_OP_NO_QUERY_MTU: 0,
    SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION: 0,
    SSL_OP_NO_SSLv2: 0,
    SSL_OP_NO_SSLv3: 0,
    SSL_OP_NO_TICKET: 0,
    SSL_OP_NO_TLSv1: 0,
    SSL_OP_NO_TLSv1_1: 0,
    SSL_OP_NO_TLSv1_2: 0,
    SSL_OP_PKCS1_CHECK_1: 0,
    SSL_OP_PKCS1_CHECK_2: 0,
    SSL_OP_SINGLE_DH_USE: 0,
    SSL_OP_SINGLE_ECDH_USE: 0,
    SSL_OP_SSLEAY_080_CLIENT_DH_BUG: 0,
    SSL_OP_SSLREF2_REUSE_CERT_TYPE_BUG: 0,
    SSL_OP_TLS_BLOCK_PADDING_BUG: 0,
    SSL_OP_TLS_D5_BUG: 0,
    SSL_OP_TLS_ROLLBACK_BUG: 0,
    ENGINE_METHOD_RSA: 0,
    ENGINE_METHOD_DSA: 0,
    ENGINE_METHOD_DH: 0,
    ENGINE_METHOD_RAND: 0,
    ENGINE_METHOD_EC: 0,
    ENGINE_METHOD_CIPHERS: 0,
    ENGINE_METHOD_DIGESTS: 0,
    ENGINE_METHOD_PKEY_METHS: 0,
    ENGINE_METHOD_PKEY_ASN1_METHS: 0,
    ENGINE_METHOD_ALL: 0,
    ENGINE_METHOD_NONE: 0,
    DH_CHECK_P_NOT_SAFE_PRIME: 0,
    DH_CHECK_P_NOT_PRIME: 0,
    DH_UNABLE_TO_CHECK_GENERATOR: 0,
    DH_NOT_SUITABLE_GENERATOR: 0,
    ALPN_ENABLED: 0,
    RSA_PKCS1_PADDING: 0,
    RSA_SSLV23_PADDING: 0,
    RSA_NO_PADDING: 0,
    RSA_PKCS1_OAEP_PADDING: 0,
    RSA_X931_PADDING: 0,
    RSA_PKCS1_PSS_PADDING: 0,
    RSA_PSS_SALTLEN_DIGEST: 0,
    RSA_PSS_SALTLEN_MAX_SIGN: 0,
    RSA_PSS_SALTLEN_AUTO: 0,
    POINT_CONVERSION_COMPRESSED: 0,
    POINT_CONVERSION_UNCOMPRESSED: 0,
    POINT_CONVERSION_HYBRID: 0
  },
  Buffer
};

// Test script to verify IAP configuration
// Run this in a production build (not Expo Go) to test RevenueCat

import { IAP_PRODUCT_IDS, initializeIAP, getProducts } from './services/appleIAPService';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export const testIAPConfiguration = async () => {
  console.log('🧪 Testing IAP Configuration...');
  console.log('================================');
  
  // Check environment
  console.log('📱 Environment Check:');
  console.log('  Platform:', Platform.OS);
  console.log('  App Ownership:', Constants.appOwnership);
  console.log('  Is Expo Go:', Constants.appOwnership === 'expo');
  console.log('');
  
  // Check product IDs
  console.log('🏷️ Product IDs:');
  console.log('  Pro Monthly:', IAP_PRODUCT_IDS.PRO_MONTHLY);
  console.log('  Premium Monthly:', IAP_PRODUCT_IDS.PREMIUM_MONTHLY);
  console.log('');
  
  // Test RevenueCat initialization
  console.log('🔧 Testing RevenueCat Initialization...');
  try {
    const initialized = await initializeIAP();
    console.log('  Initialization Result:', initialized ? '✅ SUCCESS' : '❌ FAILED');
    
    if (!initialized) {
      console.log('  ⚠️ RevenueCat failed to initialize');
      if (Constants.appOwnership === 'expo') {
        console.log('  💡 This is expected in Expo Go - build a standalone app');
      }
      return false;
    }
  } catch (error) {
    console.log('  ❌ Initialization Error:', error.message);
    return false;
  }
  
  // Test product fetching
  console.log('');
  console.log('📦 Testing Product Fetching...');
  try {
    const products = await getProducts();
    console.log('  Products Found:', products.length);
    
    if (products.length === 0) {
      console.log('  ❌ No products found!');
      console.log('  💡 Check:');
      console.log('    - App Store Connect product configuration');
      console.log('    - RevenueCat dashboard setup');
      console.log('    - Product IDs match exactly');
      return false;
    }
    
    console.log('  ✅ Products loaded successfully:');
    products.forEach((product, index) => {
      console.log(`    ${index + 1}. ${product.product.identifier}`);
      console.log(`       Price: ${product.product.priceString}`);
      console.log(`       Title: ${product.product.title}`);
    });
    
    // Verify our expected products are present
    const expectedProducts = Object.values(IAP_PRODUCT_IDS);
    const foundProducts = products.map(p => p.product.identifier);
    
    console.log('');
    console.log('🔍 Product Verification:');
    expectedProducts.forEach(productId => {
      const found = foundProducts.includes(productId);
      console.log(`  ${productId}: ${found ? '✅ Found' : '❌ Missing'}`);
    });
    
    return products.length > 0;
    
  } catch (error) {
    console.log('  ❌ Product Fetching Error:', error.message);
    return false;
  }
};

// Usage instructions
console.log(`
🚀 How to use this test:

1. Build a production app (not Expo Go):
   eas build --platform ios --profile production

2. Install the app on a device

3. Add this to your subscription screen:
   
   // Add a test button
   <Button 
     title="🧪 Test IAP Config" 
     onPress={testIAPConfiguration}
   />

4. Tap the test button and check the logs

5. If tests fail, check:
   - App Store Connect products
   - RevenueCat configuration
   - Product ID matches
`);

-- Security improvements for Apple In-App Purchase validation
-- This migration adds security constraints and logging for purchase validation

-- Add security fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS validation_environment TEXT DEFAULT 'production',
ADD COLUMN IF NOT EXISTS validation_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_validation_attempt TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS fraud_flags TEXT[] DEFAULT '{}';

-- Create unique constraint on apple_transaction_id to prevent duplicate processing
-- First, remove any existing duplicates (keep the most recent one)
WITH ranked_transactions AS (
  SELECT id, apple_transaction_id,
         ROW_NUMBER() OVER (PARTITION BY apple_transaction_id ORDER BY updated_at DESC) as rn
  FROM public.profiles
  WHERE apple_transaction_id IS NOT NULL
)
UPDATE public.profiles 
SET apple_transaction_id = NULL
WHERE id IN (
  SELECT id FROM ranked_transactions WHERE rn > 1
);

-- Now add the unique constraint
ALTER TABLE public.profiles
ADD CONSTRAINT unique_apple_transaction_id UNIQUE (apple_transaction_id);

-- <PERSON><PERSON> purchase validation log table for security monitoring
CREATE TABLE IF NOT EXISTS public.purchase_validation_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  transaction_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  validation_result TEXT NOT NULL, -- 'success', 'failed', 'duplicate', 'fraud'
  validation_environment TEXT, -- 'production', 'sandbox'
  error_message TEXT,
  receipt_data_hash TEXT, -- SHA256 hash of receipt data for tracking
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance and monitoring
CREATE INDEX IF NOT EXISTS idx_purchase_validation_log_user_id ON public.purchase_validation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_purchase_validation_log_transaction_id ON public.purchase_validation_log(transaction_id);
CREATE INDEX IF NOT EXISTS idx_purchase_validation_log_created_at ON public.purchase_validation_log(created_at);
CREATE INDEX IF NOT EXISTS idx_purchase_validation_log_result ON public.purchase_validation_log(validation_result);

-- Create secure function to update Apple subscription with validation
CREATE OR REPLACE FUNCTION public.update_apple_subscription_secure(
  user_uuid UUID,
  product_id TEXT,
  transaction_id TEXT,
  receipt_data TEXT,
  subscription_tier TEXT,
  purchase_date TEXT,
  validation_environment TEXT DEFAULT 'production'
)
RETURNS JSON AS $$
DECLARE
  expires_at TIMESTAMP WITH TIME ZONE;
  receipt_hash TEXT;
  result JSON;
BEGIN
  -- Calculate expiration date (30 days from purchase for monthly subscriptions)
  expires_at := (purchase_date::TIMESTAMP WITH TIME ZONE) + INTERVAL '30 days';
  
  -- Create hash of receipt data for logging (don't store full receipt for security)
  receipt_hash := encode(digest(receipt_data, 'sha256'), 'hex');
  
  -- Check if transaction already exists (double-check for race conditions)
  IF EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE apple_transaction_id = transaction_id
  ) THEN
    -- Log duplicate attempt
    INSERT INTO public.purchase_validation_log (
      user_id, transaction_id, product_id, validation_result, 
      validation_environment, error_message, receipt_data_hash
    ) VALUES (
      user_uuid, transaction_id, product_id, 'duplicate',
      validation_environment, 'Transaction ID already exists', receipt_hash
    );
    
    RETURN json_build_object(
      'success', false,
      'error', 'Transaction already processed'
    );
  END IF;
  
  -- Update user's subscription
  UPDATE public.profiles
  SET 
    apple_transaction_id = transaction_id,
    apple_product_id = product_id,
    apple_receipt_data = receipt_data,
    subscription_tier = subscription_tier,
    subscription_status = 'active',
    subscription_start_date = purchase_date::TIMESTAMP WITH TIME ZONE,
    subscription_end_date = expires_at,
    subscription_expires_at = expires_at,
    validation_environment = validation_environment,
    last_receipt_validation = NOW(),
    updated_at = NOW()
  WHERE id = user_uuid;
  
  -- Check if update was successful
  IF NOT FOUND THEN
    -- Log failed update
    INSERT INTO public.purchase_validation_log (
      user_id, transaction_id, product_id, validation_result, 
      validation_environment, error_message, receipt_data_hash
    ) VALUES (
      user_uuid, transaction_id, product_id, 'failed',
      validation_environment, 'User not found or update failed', receipt_hash
    );
    
    RETURN json_build_object(
      'success', false,
      'error', 'Failed to update user subscription'
    );
  END IF;
  
  -- Log successful validation
  INSERT INTO public.purchase_validation_log (
    user_id, transaction_id, product_id, validation_result, 
    validation_environment, receipt_data_hash
  ) VALUES (
    user_uuid, transaction_id, product_id, 'success',
    validation_environment, receipt_hash
  );
  
  result := json_build_object(
    'success', true,
    'subscription_tier', subscription_tier,
    'expires_at', expires_at,
    'environment', validation_environment
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check for suspicious activity
CREATE OR REPLACE FUNCTION public.check_purchase_fraud_indicators(
  user_uuid UUID,
  transaction_id TEXT
)
RETURNS JSON AS $$
DECLARE
  recent_attempts INTEGER;
  different_products INTEGER;
  result JSON;
  fraud_flags TEXT[] := '{}';
BEGIN
  -- Check for multiple validation attempts in short time
  SELECT COUNT(*) INTO recent_attempts
  FROM public.purchase_validation_log
  WHERE user_id = user_uuid 
    AND created_at > NOW() - INTERVAL '1 hour';
  
  IF recent_attempts > 5 THEN
    fraud_flags := array_append(fraud_flags, 'high_frequency_attempts');
  END IF;
  
  -- Check for attempts with different products but same transaction ID
  SELECT COUNT(DISTINCT product_id) INTO different_products
  FROM public.purchase_validation_log
  WHERE transaction_id = transaction_id;
  
  IF different_products > 1 THEN
    fraud_flags := array_append(fraud_flags, 'transaction_id_reuse');
  END IF;
  
  -- Update user's fraud flags if any detected
  IF array_length(fraud_flags, 1) > 0 THEN
    UPDATE public.profiles
    SET fraud_flags = fraud_flags,
        updated_at = NOW()
    WHERE id = user_uuid;
  END IF;
  
  result := json_build_object(
    'fraud_flags', fraud_flags,
    'risk_level', CASE 
      WHEN array_length(fraud_flags, 1) > 1 THEN 'high'
      WHEN array_length(fraud_flags, 1) = 1 THEN 'medium'
      ELSE 'low'
    END
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get purchase validation statistics (for monitoring)
CREATE OR REPLACE FUNCTION public.get_validation_stats(
  days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
  stats JSON;
BEGIN
  SELECT json_build_object(
    'total_attempts', COUNT(*),
    'successful_validations', COUNT(*) FILTER (WHERE validation_result = 'success'),
    'failed_validations', COUNT(*) FILTER (WHERE validation_result = 'failed'),
    'duplicate_attempts', COUNT(*) FILTER (WHERE validation_result = 'duplicate'),
    'fraud_attempts', COUNT(*) FILTER (WHERE validation_result = 'fraud'),
    'sandbox_validations', COUNT(*) FILTER (WHERE validation_environment = 'sandbox'),
    'production_validations', COUNT(*) FILTER (WHERE validation_environment = 'production')
  ) INTO stats
  FROM public.purchase_validation_log
  WHERE created_at > NOW() - (days_back || ' days')::INTERVAL;
  
  RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.update_apple_subscription_secure TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_purchase_fraud_indicators TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_validation_stats TO authenticated;

-- Grant access to validation log table (read-only for authenticated users)
GRANT SELECT ON public.purchase_validation_log TO authenticated;

-- Enable RLS on the new table
ALTER TABLE public.purchase_validation_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for purchase validation log
CREATE POLICY "Users can view their own validation logs" ON public.purchase_validation_log
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy for service role to insert logs
CREATE POLICY "Service role can insert validation logs" ON public.purchase_validation_log
  FOR INSERT WITH CHECK (true);

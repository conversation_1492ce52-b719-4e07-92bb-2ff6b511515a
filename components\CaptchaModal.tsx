import React, { useRef, useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import HCaptcha, { HCaptchaRef } from './HCaptcha';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';
import { useLanguage } from '../contexts/LanguageContext';

interface CaptchaModalProps {
  visible: boolean;
  onVerify: (token: string) => void;
  onClose: () => void;
}

const CaptchaModal: React.FC<CaptchaModalProps> = ({ visible, onVerify, onClose }) => {
  const { t } = useLanguage();
  const captchaRef = useRef<HCaptchaRef>(null);
  const [verifying, setVerifying] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle CAPTCHA verification
  const handleVerify = (token: string) => {
    console.log('CAPTCHA verified, token length:', token.length);
    setVerifying(true);

    // Call the onVerify callback with the token
    onVerify(token);

    // Reset the verification state
    setVerifying(false);
  };

  // Handle CAPTCHA expiration
  const handleExpire = () => {
    setError(t('captchaExpired'));
    if (captchaRef.current) {
      captchaRef.current.resetCaptcha();
    }
  };

  // Handle CAPTCHA error
  const handleError = (err: Error) => {
    setError(t('captchaError'));
    console.error('CAPTCHA error:', err);
  };

  // Reset error when modal is opened
  React.useEffect(() => {
    if (visible) {
      setError(null);
    }
  }, [visible]);

  // We now use a WebView-based implementation for mobile platforms
  // This function is kept for backward compatibility but is no longer used
  const handleSimulatedVerify = () => {
    console.log('Simulated verify should not be called anymore');
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t('verifyYouAreHuman')}</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Ionicons name="close" size={24} color={Colors.textPrimary} />
            </TouchableOpacity>
          </View>

          <Text style={styles.description}>{t('captchaDescription')}</Text>

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          <View style={styles.captchaContainer}>
            {verifying ? (
              <ActivityIndicator size="large" color={Colors.primary} />
            ) : (
              <HCaptcha
                ref={captchaRef}
                onVerify={handleVerify}
                onExpire={handleExpire}
                onError={handleError}
              />
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    width: '100%',
    maxWidth: 500,
    maxHeight: '95%', // Further increased to accommodate the larger captcha container
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  closeButton: {
    padding: Spacing.xs,
  },
  description: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: Spacing.md,
  },
  captchaContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300, // Increased height to ensure the widget is fully visible
    marginVertical: Spacing.md,
    width: '100%',
  },
  errorContainer: {
    backgroundColor: Colors.error + '20', // 20% opacity
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    marginBottom: Spacing.md,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
  },

  buttonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CaptchaModal;

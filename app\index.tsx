import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import AppShell from '../components/layout/AppShell';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';

export default function HomeScreen() {
  const router = useRouter();
  const { t } = useLanguage();

  return (
    <AppShell>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>PillLogic</Text>
          <Text style={styles.subtitle}>{t('medicationAssistant')}</Text>
        </View>

        <View style={styles.featuresContainer}>
          <Text style={styles.sectionTitle}>{t('features')}</Text>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => router.push('/scan')}
          >
            <View style={styles.featureIcon}>
              <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>{t('medicationNotesScanner')}</Text>
              <Text style={styles.featureDescription}>{t('medicationNotesScannerDescription')}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textTertiary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => router.push('/pills')}
          >
            <View style={styles.featureIcon}>
              <Ionicons name="calculator-outline" size={24} color={Colors.docPurple.DEFAULT} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>{t('pillCounter')}</Text>
              <Text style={styles.featureDescription}>{t('pillCounterDescription')}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textTertiary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => router.push('/summary')}
          >
            <View style={styles.featureIcon}>
              <Ionicons name="list-outline" size={24} color={Colors.docPurple.DEFAULT} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>{t('medicationSummary')}</Text>
              <Text style={styles.featureDescription}>{t('viewSavedMedications')}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textTertiary} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.scanButton}
          onPress={() => router.push('/feature-select')}
        >
          <Ionicons name="scan-outline" size={24} color={Colors.white} style={styles.scanIcon} />
          <Text style={styles.scanText}>{t('startScanning')}</Text>
        </TouchableOpacity>
      </View>
    </AppShell>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  header: {
    alignItems: 'center',
    marginVertical: Spacing.xl,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.docPurple.DEFAULT,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  featuresContainer: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: Spacing.md,
    color: Colors.textPrimary,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.md,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.docBlue.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    color: Colors.textPrimary,
  },
  featureDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginTop: Spacing.md,
  },
  scanIcon: {
    marginRight: Spacing.sm,
  },
  scanText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

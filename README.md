# DocAid Mobile App

## Environment Variables

This application uses several API keys for different services. For security reasons, these keys are stored in the `constants/env.ts` file.

### API Keys

- **Roboflow API Key**: Used for pill detection and counting
  - File: `constants/env.ts`
  - Variable: `ROBOFLOW_API_KEY`

- **OpenAI API Key**: Used for medication extraction and translation
  - File: `services/openaiVisionService.ts`
  - Variable: `OPENAI_API_KEY`

### Setting Up Environment Variables

The application uses environment variables stored in `.env` files:

- `.env` - Used for local development
- `.env.production` - Used for production builds

Both files should contain the same variables but with different values appropriate for each environment.

To update environment variables:

1. For development, edit the `.env` file
2. For production, edit the `.env.production` file
3. After changing environment variables, restart the application

The environment variables are loaded into the application through `app.config.js` and made available to the TypeScript code via the `constants/env.ts` file.

### Security Considerations

- Never commit API keys to public repositories
- Consider using a proper environment variable system for production
- Implement API key rotation and monitoring

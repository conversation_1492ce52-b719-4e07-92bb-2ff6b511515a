# Account Deletion Implementation

This document outlines the account deletion functionality implemented to meet Apple App Store Review Guidelines 5.1.1(v).

## Overview

The implementation provides users with the ability to delete their accounts and all associated data directly within the app, as required by Apple's guidelines.

## Components Implemented

### 1. Database Migration (`supabase/migrations/20250809000000_add_account_deletion.sql`)

- **`delete_user_account(user_uuid UUID)`**: Main function that deletes all user data
  - Removes data from all tables in correct order to respect foreign key constraints
  - Deletes: purchase_validation_log, live_session_tracking, daily_feature_usage, feature_usage, usage_logs, subscriptions, profiles, and auth.users
  - Returns boolean indicating success/failure

- **`schedule_account_deletion(user_uuid UUID, deletion_date TIMESTAMP)`**: Allows delayed deletion
  - Creates scheduled deletion record
  - Useful for users who want to delay deletion (e.g., until subscription expires)

- **`cancel_scheduled_deletion(user_uuid UUID)`**: Cancels scheduled deletion

- **`process_scheduled_deletions()`**: Processes scheduled deletions (for cron jobs)

### 2. Client-Side Implementation (`contexts/AuthContext.tsx`)

- **`deleteAccount()`**: Main client function for account deletion
  - Handles Apple Sign-In token revocation for Apple users
  - Calls database function to delete all user data
  - Clears local session after successful deletion
  - Provides appropriate error handling and user feedback

### 3. UI Implementation (`app/settings/account.tsx`)

- Added "Account Management" section to account settings
- **Delete Account button** with warning styling
- **Two-step confirmation process**:
  1. Initial warning with different messages for Apple vs email users
  2. Final confirmation to prevent accidental deletion
- Clear warning about permanent data loss

### 4. Apple Sign-In Token Revocation (`supabase/functions/revoke-apple-token/index.ts`)

- Supabase Edge Function to revoke Apple Sign-In tokens
- Uses Apple's REST API to revoke refresh tokens
- Ensures compliance with Apple's requirements for Sign in with Apple
- Secure server-side implementation

### 5. FAQ Updates (`app/settings/help.tsx`)

Added comprehensive FAQ entries covering:
- How to delete account
- What happens during deletion
- Special considerations for Apple Sign-In users
- Confirmation that deletion is permanent
- Account recovery information

## Key Features

### Security & Privacy
- **Complete data removal**: All user data is permanently deleted
- **Apple token revocation**: For Apple Sign-In users, authorization tokens are revoked
- **Two-step confirmation**: Prevents accidental deletions
- **Secure implementation**: Server-side functions for sensitive operations

### User Experience
- **Easy to find**: Located in Account Settings as recommended by Apple
- **Clear warnings**: Users understand the permanent nature of deletion
- **Appropriate messaging**: Different messages for Apple vs email users
- **Immediate feedback**: Success/error messages guide users

### Compliance
- **Apple Guidelines 5.1.1(v)**: Full compliance with account deletion requirements
- **Apple Sign-In**: Proper token revocation using Apple's REST API
- **Data protection**: Complete removal of all personal data
- **No workarounds**: Direct deletion without requiring external processes

## Usage Flow

1. User navigates to Account Settings
2. Scrolls to "Account Management" section
3. Taps "Delete Account" button
4. Sees initial warning dialog (customized for Apple/email users)
5. Confirms deletion intent
6. Sees final confirmation dialog
7. Account and all data are permanently deleted
8. User is signed out and redirected to home screen

## Testing Recommendations

1. **Test with email account**: Verify complete data deletion
2. **Test with Apple Sign-In account**: Verify token revocation and data deletion
3. **Test cancellation**: Ensure users can cancel at any step
4. **Test error handling**: Verify appropriate error messages
5. **Verify data cleanup**: Check that all user data is removed from database

## Apple Review Compliance

This implementation meets all Apple App Store Review Guidelines 5.1.1(v) requirements:

✅ **Account deletion option available in app**
✅ **Easy to find in account settings**
✅ **Deletes entire account record and associated data**
✅ **No requirement for external processes (phone/email)**
✅ **Appropriate confirmation steps**
✅ **Apple Sign-In token revocation**
✅ **Clear user communication**

## Files Modified

- `supabase/migrations/20250809000000_add_account_deletion.sql` (new)
- `supabase/functions/revoke-apple-token/index.ts` (new)
- `contexts/AuthContext.tsx` (modified)
- `app/settings/account.tsx` (modified)
- `app/settings/help.tsx` (modified)
- `ACCOUNT_DELETION_IMPLEMENTATION.md` (new)

## Next Steps

1. Deploy the database migration to production
2. Deploy the Supabase Edge Function
3. Test the complete flow in TestFlight
4. Submit for Apple Review

The implementation is minimal yet complete, meeting Apple's requirements without adding unnecessary complexity to the app.

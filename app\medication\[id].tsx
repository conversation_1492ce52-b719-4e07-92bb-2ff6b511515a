import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Image, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Text, Container, Header, Card, Button, Divider } from '../../components/ui';
import { Colors, Spacing, BorderRadius, Typography } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { getMedicationAnalysisById, deleteMedicationAnalysis } from '../../services/storageService';
import { StoredAnalysis } from '../../services/storageService';
import MedicationTable, { Medication } from '../../components/MedicationTable';
import { translateMedications } from '../../services/googleTranslateService';
import { Language } from '../../services/languageTypes';

export default function MedicationDetailScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { t, language } = useLanguage();
  const [analysis, setAnalysis] = useState<StoredAnalysis | null>(null);
  const [originalAnalysis, setOriginalAnalysis] = useState<StoredAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [translating, setTranslating] = useState(false);

  // Load analysis when ID changes
  useEffect(() => {
    const loadAnalysis = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getMedicationAnalysisById(id as string);
        setOriginalAnalysis(data);
        setAnalysis(data); // Initially set to original data
      } catch (error) {
        console.error('Error loading medication analysis:', error);
        Alert.alert(t('error'), t('errorLoadingAnalysis'));
      } finally {
        setLoading(false);
      }
    };

    loadAnalysis();
  }, [id]);

  // Handle translations when language changes
  useEffect(() => {
    const translateContent = async () => {
      if (!originalAnalysis) return;

      // For English, use original data
      if (language === Language.ENGLISH) {
        setAnalysis(originalAnalysis);
        return;
      }

      // For other languages, translate the medications
      try {
        console.log(`Translating medications to ${language}...`);
        setTranslating(true);
        const translatedMedications = await translateMedications(originalAnalysis.medications, language);
        console.log('Translation complete');
        setAnalysis({
          ...originalAnalysis,
          medications: translatedMedications
        });
      } catch (error) {
        console.error('Error translating medications:', error);
        // Fallback to original data if translation fails
        setAnalysis(originalAnalysis);
      } finally {
        setTranslating(false);
      }
    };

    translateContent();
  }, [language, originalAnalysis]);

  const handleDelete = () => {
    Alert.alert(
      t('confirmDelete'),
      t('deleteAnalysisConfirm'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('delete'),
          style: 'destructive',
          onPress: async () => {
            if (!analysis) return;

            try {
              await deleteMedicationAnalysis(analysis.id);
              router.back();
            } catch (error) {
              console.error('Error deleting analysis:', error);
              Alert.alert(t('error'), t('errorDeletingAnalysis'));
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <Container centered>
        <Ionicons name="medical" size={48} color={Colors.primary} />
        <Text variant="h3" weight="semibold" style={styles.loadingText}>
          {t('loadingMedication')}
        </Text>
      </Container>
    );
  }

  if (!analysis) {
    return (
      <Container centered>
        <Ionicons name="alert-circle-outline" size={48} color={Colors.error} />
        <Text variant="h3" weight="semibold" style={styles.errorText}>
          {t('medicationNotFound')}
        </Text>
        <Button
          title={t('goBack')}
          variant="outline"
          onPress={() => router.back()}
          style={styles.backButton}
        />
      </Container>
    );
  }

  return (
    <Container scrollable={false} padded={false}>
      <Header
        title={analysis.name}
        showBackButton
      />

      <ScrollView style={styles.scrollContent} contentContainerStyle={styles.scrollContentContainer}>
        {analysis.imageUri && (
          <Card style={styles.imageCard} elevation="sm" padding="none">
            <Image source={{ uri: analysis.imageUri }} style={styles.image} resizeMode="contain" />
          </Card>
        )}

        <View style={styles.infoContainer}>
          <View style={styles.infoHeader}>
            <Text variant="h3" weight="semibold">
              {analysis.name}
            </Text>
            <View style={styles.dateContainer}>
              <Ionicons name="calendar-outline" size={16} color={Colors.textTertiary} />
              <Text variant="body" style={[styles.dateText, { color: '#000000' }]}>
                {formatDate(analysis.date)}
              </Text>
            </View>
          </View>

          <Divider />

          <Text variant="h4" weight="semibold" style={styles.sectionTitle}>
            {t('medications')}
          </Text>

          {translating ? (
            <View style={styles.translatingContainer}>
              <ActivityIndicator size="large" color="#2f95dc" />
              <Text style={styles.translatingText}>
                {t('translating')}
              </Text>
              <Text style={styles.translatingSubtext}>
                {t('translatingMedications')}
              </Text>
            </View>
          ) : analysis.medications.length > 0 ? (
            <Card style={styles.tableCard} elevation="xs">
              <MedicationTable medications={analysis.medications} />
            </Card>
          ) : (
            <Text variant="body" style={[styles.noMedicationsText, { color: '#000000' }]}>
              {t('noMedicationsFound')}
            </Text>
          )}

          <View style={styles.actionsContainer}>
            <Button
              title={t('delete')}
              variant="outline"
              icon="trash-outline"
              iconPosition="left"
              style={[styles.actionButton, styles.deleteActionButton]}
              textStyle={styles.deleteActionText}
              onPress={handleDelete}
            />
          </View>
        </View>
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
  },
  scrollContentContainer: {
    paddingBottom: Spacing.xl,
  },
  imageCard: {
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  image: {
    width: '100%',
    height: 200,
  },
  infoContainer: {
    padding: Spacing.md,
  },
  infoHeader: {
    marginBottom: Spacing.md,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  dateText: {
    marginLeft: Spacing.xs,
  },
  sectionTitle: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  tableCard: {
    marginBottom: Spacing.md,
    padding: 0,
  },
  translatingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginVertical: 10,
    marginBottom: Spacing.md,
  },
  translatingText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: '600',
    color: '#7E69AB',
    textAlign: 'center',
  },
  translatingSubtext: {
    marginTop: 5,
    fontSize: 14,
    color: '#000000',
    textAlign: 'center',
    maxWidth: '80%',
  },
  noMedicationsText: {
    marginBottom: Spacing.md,
    fontStyle: 'italic',
  },
  actionsContainer: {
    marginTop: Spacing.md,
  },
  actionButton: {
    marginHorizontal: Spacing.xs,
  },
  deleteActionButton: {
    borderColor: Colors.error,
  },
  deleteActionText: {
    color: Colors.error,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  translateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#9b87f5',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#7E69AB',
    marginRight: Spacing.sm,
  },
  translateButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  deleteButton: {
    padding: Spacing.xs,
  },
  loadingText: {
    marginTop: Spacing.md,
  },
  errorText: {
    marginTop: Spacing.md,
    marginBottom: Spacing.md,
  },
  backButton: {
    marginTop: Spacing.md,
  },
});

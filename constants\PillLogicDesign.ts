// PillLogic Design System

export const Colors = {
  // PillLogic custom colors
  docPurple: {
    light: '#9b87f5',
    DEFAULT: '#7E69AB',
    dark: '#5D4A88',
  },
  docBlue: {
    light: '#D3E4FD',
    DEFAULT: '#93c5fd',
    dark: '#60a5fa',
  },
  docAlert: '#ea384c',

  // Base colors
  white: '#FFFFFF',
  black: '#000000',

  // Background colors
  background: 'hsl(260, 20%, 98%)',
  card: '#FFFFFF',

  // Text colors
  textPrimary: '#000000',
  textSecondary: 'hsl(215.4, 16.3%, 46.9%)',
  textTertiary: 'hsl(215, 20.2%, 65.1%)',

  // Border colors
  border: 'hsl(214.3, 31.8%, 91.4%)',

  // Status colors
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

export const Typography = {
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 30,
  },

  fontWeight: {
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },

  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 42,
  },
};

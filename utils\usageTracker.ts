import { useAuth } from '../contexts/AuthContext';
import { incrementUsageCount } from '../lib/supabase';

// Feature types for tracking
export enum FeatureType {
  PILL_COUNT = 'pill_count',
  LIVE_PILL_COUNT = 'live_pill_count',
  MEDICATION_SCAN = 'medication_scan',
  TRANSLATION = 'translation',
}

// Hook to track feature usage
export const useUsageTracker = () => {
  const { user } = useAuth();

  // Track feature usage
  const trackFeatureUsage = async (featureType: FeatureType) => {
    if (!user) {
      console.log('User not logged in, skipping usage tracking');
      return;
    }

    try {
      await incrementUsageCount(user.id);
      console.log(`Usage tracked for feature: ${featureType}`);
    } catch (error) {
      console.error('Error tracking feature usage:', error);
    }
  };

  return { trackFeatureUsage };
};

// Function to check if user has reached usage limit
export const checkUsageLimit = (user: any, limit: number = 5): boolean => {
  if (!user) return false; // No user, no limit
  
  // If user is on a paid tier, no limit
  if (user.subscription_tier && user.subscription_tier !== 'free') {
    return false;
  }
  
  // Check if user has reached the limit
  return (user.usage_count || 0) >= limit;
};

# RevenueCat Setup Guide for PillLogic

## 🚨 CRITICAL: You MUST complete this setup for subscriptions to work!

The app has been migrated from the deprecated `expo-in-app-purchases` to `react-native-purchases` (RevenueCat). This is why your subscriptions weren't working before.

## Step 1: Create RevenueCat Account
1. Go to https://app.revenuecat.com/
2. Sign up with your Apple Developer account email
3. Create a new project called "PillLogic"

## Step 2: Configure iOS App
1. In RevenueCat dashboard, click "Add App"
2. Select "iOS"
3. Enter your bundle ID: `com.pilllogic.app`
4. Copy the **iOS API Key** (starts with `appl_`)

## Step 3: Update API Key in Code
1. Open `services/appleIAPService.ts`
2. Find line ~18: `ios: 'appl_YOUR_IOS_API_KEY_HERE'`
3. Replace `appl_YOUR_IOS_API_KEY_HERE` with your actual API key

## Step 4: Configure Products in RevenueCat
1. In RevenueCat dashboard, go to "Products"
2. Add your App Store Connect products:
   - `com.pilllogic.app.pro.monthly.v2` (Pro Plan - $4.99/month) [Updated Product ID]
   - `com.pilllogic.app.premium.monthly` (Premium Plan - $5.99/month)

## Step 5: Create Offerings
1. Go to "Offerings" in RevenueCat
2. Create a "default" offering
3. Add both products to the offering

## Step 6: Test
1. Build new version with `eas build --platform ios --profile production`
2. Test in TestFlight
3. Subscriptions should now work properly!

## Benefits of RevenueCat
- ✅ **Works in production builds** (unlike deprecated expo-in-app-purchases)
- ✅ **Automatic receipt validation**
- ✅ **Cross-platform support**
- ✅ **Analytics and insights**
- ✅ **Subscription management**
- ✅ **Actively maintained**

## Troubleshooting
- If you get "API key not found" error, double-check the API key in the code
- If products don't load, verify they're added to RevenueCat and the offering
- If purchases fail, check that products exist in both App Store Connect AND RevenueCat

## Next Steps After Setup
1. Update API key in code
2. Build and test
3. Your subscription system will finally work! 🎉

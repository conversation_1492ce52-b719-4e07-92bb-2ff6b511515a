import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../contexts/LanguageContext';
import AppShell from '../components/layout/AppShell';
import { Colors, Spacing, BorderRadius } from '../constants/PillLogicDesign';

const FeatureSelect = () => {
  const router = useRouter();
  const { t } = useLanguage();

  return (
    <AppShell>
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/')}
        >
          <Ionicons name="arrow-back" size={16} color={Colors.docPurple.DEFAULT} />
          <Text style={styles.backText}>{t('backToHome')}</Text>
        </TouchableOpacity>

        <Text style={styles.title}>{t('selectFeature')}</Text>

        <View style={styles.featuresContainer}>
          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => router.push('/scan')}
          >
            <View style={styles.featureContent}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="document-text-outline" size={30} color={Colors.docPurple.DEFAULT} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>{t('medicationNotesScanner')}</Text>
                <Text style={styles.featureDescription}>
                  {t('medicationNotesScannerDescription')}
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => {
              // Navigate directly to pills screen with camera step
              router.push({
                pathname: '/pills',
                params: { step: 'camera' }
              });
            }}
          >
            <View style={styles.featureContent}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="calculator-outline" size={30} color={Colors.docPurple.DEFAULT} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>{t('pillCounter')}</Text>
                <Text style={styles.featureDescription}>
                  {t('pillCounterDescription')}
                </Text>
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.featureCard}
            onPress={() => router.push('/livepills')}
          >
            <View style={styles.featureContent}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="videocam-outline" size={30} color={Colors.docPurple.DEFAULT} />
              </View>
              <View style={styles.featureTextContainer}>
                <Text style={styles.featureTitle}>{t('livePillCount')}</Text>
                <Text style={styles.featureDescription}>
                  {t('livePillCountDescription')}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </AppShell>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.md,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  backText: {
    marginLeft: Spacing.xs,
    color: Colors.docPurple.DEFAULT,
    fontSize: 14,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: Spacing.md,
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  featuresContainer: {
    alignItems: 'center',
    gap: Spacing.sm,
  },
  featureCard: {
    width: '100%',
    maxWidth: 320,
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 2,
    borderColor: Colors.docBlue.DEFAULT,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: Spacing.sm,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  featureIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.docBlue.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: Spacing.xs,
    color: Colors.textPrimary,
  },
  featureDescription: {
    fontSize: 13,
    color: Colors.textSecondary,
  },
});

export default FeatureSelect;

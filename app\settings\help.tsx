import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  Alert,
  ScrollView,
  TextInput,
  Linking,
  ActivityIndicator
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { Colors, Spacing, BorderRadius } from '../../constants/DocAidDesign';
import { useLanguage } from '../../contexts/LanguageContext';
import { sendSupportEmail } from '../../lib/resend';
import { useCaptcha } from '../../hooks/useCaptcha';
import CaptchaModal from '../../components/CaptchaModal';
import { LEGAL_URLS } from '../../constants/env';

// FAQ data
const faqData = [
  {
    question: 'How do I reset my password?',
    answer: 'Go to the Account Settings page and tap on "Change Password". You will receive an email with instructions to reset your password.'
  },
  {
    question: 'How do I change my notification settings?',
    answer: 'Go to the Account Settings page and scroll down to the "Notification Preferences" section. There you can toggle email and app notifications.'
  },
  {
    question: 'Is my data secure?',
    answer: 'Yes, we use industry-standard encryption and security practices to protect your data. You can adjust your privacy settings in the Account Settings page.'
  },
  {
    question: 'How do I use the Pill Counter feature?',
    answer: 'Select the Pill Counter feature from the home screen, then take a photo of the pills you want to count. The app will automatically detect and count the pills in the image.'
  },
  {
    question: 'How does the Medication Notes Scanner work?',
    answer: 'The Medication Notes Scanner uses AI to extract medication information from doctor\'s notes or prescriptions. Take a photo of the document, and the app will identify medications, dosages, and other relevant information.'
  },
  {
    question: 'How accurate is the pill counting feature?',
    answer: 'The pill counting feature is designed to be highly accurate, but results may vary based on lighting conditions, pill arrangement, and contrast with the background. For best results, arrange pills with space between them on a contrasting background.'
  },
  {
    question: 'How do I delete my account?',
    answer: 'You can delete your account by going to Account Settings and scrolling down to the "Account Management" section. Tap "Delete Account" and follow the confirmation prompts. This action permanently removes all your data and cannot be undone.'
  },
  {
    question: 'What happens when I delete my account?',
    answer: 'When you delete your account, all your personal data, usage history, and account information are permanently removed from our servers. This includes your profile, medication scans, pill count history, and any other data associated with your account.'
  },
  {
    question: 'I signed up with Apple Sign-In. What happens when I delete my account?',
    answer: 'If you signed up using Apple Sign-In, deleting your account will also revoke the authorization token with Apple. This ensures that PillLogic no longer has any connection to your Apple ID. Your Apple ID itself remains unaffected.'
  },
  {
    question: 'Can I recover my account after deletion?',
    answer: 'No, account deletion is permanent and cannot be undone. All your data is immediately and permanently removed from our systems. If you want to use PillLogic again, you would need to create a new account.'
  },
  {
    question: 'How do I contact support?',
    answer: 'You can use the contact form in the Help section to send us a message, or email us <NAME_EMAIL>.'
  }
];

export default function HelpScreen() {
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useLanguage();
  const {
    isCaptchaModalVisible,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  } = useCaptcha();

  // State for contact form
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [subjectError, setSubjectError] = useState('');
  const [messageError, setMessageError] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  // State for FAQ accordion
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  // Handle going back
  const handleGoBack = () => {
    router.back();
  };

  // Handle sending support message
  const handleSendMessage = async () => {
    // Validate inputs
    let isValid = true;

    if (subject.trim().length < 3) {
      setSubjectError('Subject must be at least 3 characters');
      isValid = false;
    }

    if (message.trim().length < 10) {
      setMessageError('Message must be at least 10 characters');
      isValid = false;
    }

    if (!user?.email) {
      Alert.alert('Error', 'You must be logged in to send a support message');
      return;
    }

    if (!isValid) return;

    try {
      // Show CAPTCHA modal and wait for verification
      try {
        // Show the CAPTCHA modal and get the token
        const captchaToken = await showCaptchaModal();
        console.log('CAPTCHA verified, proceeding with message submission');

        setSendingMessage(true);

        // Send the support email using Resend
        const result = await sendSupportEmail(
          user.email,
          subject,
          message,
          user.user_metadata?.name || 'User'
        );

        if (result.success) {
          Alert.alert(
            'Message Sent',
            'Thank you for your message. We will get back to you as soon as possible.',
            [
              {
                text: 'OK',
                onPress: () => {
                  setSubject('');
                  setMessage('');
                }
              }
            ]
          );
        } else {
          Alert.alert(
            'Error',
            'There was a problem sending your message. Please try again later.',
            [{ text: 'OK' }]
          );
          console.error('Failed to send support email:', result.error);
        }
      } catch (captchaError) {
        // This will happen if the user cancels the CAPTCHA
        console.log('CAPTCHA verification cancelled or failed');
        Alert.alert('Message Cancelled', 'CAPTCHA verification is required to send a message.');
        return;
      }
    } catch (error) {
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again later.',
        [{ text: 'OK' }]
      );
      console.error('Exception sending support email:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  // Handle opening terms of service
  const handleOpenTerms = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.TERMS_OF_SERVICE);
    } catch (error) {
      console.error('Error opening Terms of Service:', error);
      Alert.alert('Error', 'Unable to open Terms of Service. Please try again later.');
    }
  };

  // Handle opening privacy policy
  const handleOpenPrivacy = async () => {
    try {
      await Linking.openURL(LEGAL_URLS.PRIVACY_POLICY);
    } catch (error) {
      console.error('Error opening Privacy Policy:', error);
      Alert.alert('Error', 'Unable to open Privacy Policy. Please try again later.');
    }
  };

  // Handle opening YouTube tutorial
  const handleOpenTutorial = () => {
    // This is a placeholder - you'll need to replace with your actual YouTube tutorial URLs
    Alert.alert(
      'Video Tutorial',
      'This is a placeholder for tutorial videos. In the production app, this would open a specific tutorial video.',
      [
        {
          text: 'Open Sample Video',
          onPress: () => Linking.openURL('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };

  // Toggle FAQ item
  const toggleFaq = (index: number) => {
    if (expandedFaq === index) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(index);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.title}>{t('help')}</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content}>
        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>

          {faqData.map((faq, index) => (
            <TouchableOpacity
              key={index}
              style={styles.faqItem}
              onPress={() => toggleFaq(index)}
            >
              <View style={styles.faqHeader}>
                <Text style={styles.faqQuestion}>{faq.question}</Text>
                <Ionicons
                  name={expandedFaq === index ? "chevron-up" : "chevron-down"}
                  size={20}
                  color={Colors.textSecondary}
                />
              </View>

              {expandedFaq === index && (
                <Text style={styles.faqAnswer}>{faq.answer}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Contact Support Form */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Support</Text>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Subject</Text>
            <TextInput
              style={styles.input}
              value={subject}
              onChangeText={(text) => {
                setSubject(text);
                setSubjectError('');
              }}
              placeholder="Enter subject"
            />
            {subjectError ? (
              <Text style={styles.errorText}>{subjectError}</Text>
            ) : null}
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Message</Text>
            <TextInput
              style={styles.textArea}
              value={message}
              onChangeText={(text) => {
                setMessage(text);
                setMessageError('');
              }}
              placeholder="Enter your message"
              multiline
              numberOfLines={5}
              textAlignVertical="top"
            />
            {messageError ? (
              <Text style={styles.errorText}>{messageError}</Text>
            ) : null}
          </View>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleSendMessage}
            disabled={sendingMessage}
          >
            {sendingMessage ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator color={Colors.white} size="small" />
                <Text style={[styles.actionButtonText, styles.loadingText]}>Sending...</Text>
              </View>
            ) : (
              <Text style={styles.actionButtonText}>Send Message</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Troubleshooting Guides */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Troubleshooting Guides</Text>

          <TouchableOpacity style={styles.guideItem}>
            <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
            <Text style={styles.guideText}>Getting Started with PillLogic</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.guideItem}>
            <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
            <Text style={styles.guideText}>Pill Counter Troubleshooting</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.guideItem}>
            <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
            <Text style={styles.guideText}>Medication Notes Scanner Tips</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.guideItem}>
            <Ionicons name="document-text-outline" size={24} color={Colors.docPurple.DEFAULT} />
            <Text style={styles.guideText}>Account and Authentication Help</Text>
          </TouchableOpacity>
        </View>

        {/* Tutorial Videos */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tutorial Videos</Text>

          <TouchableOpacity
            style={styles.videoItem}
            onPress={handleOpenTutorial}
          >
            <View style={styles.videoThumbnail}>
              <Ionicons name="play-circle" size={40} color={Colors.white} />
            </View>
            <Text style={styles.videoTitle}>Getting Started with PillLogic</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.videoItem}
            onPress={handleOpenTutorial}
          >
            <View style={styles.videoThumbnail}>
              <Ionicons name="play-circle" size={40} color={Colors.white} />
            </View>
            <Text style={styles.videoTitle}>How to Use the Pill Counter</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.videoItem}
            onPress={handleOpenTutorial}
          >
            <View style={styles.videoThumbnail}>
              <Ionicons name="play-circle" size={40} color={Colors.white} />
            </View>
            <Text style={styles.videoTitle}>Analyzing Doctor's Notes</Text>
          </TouchableOpacity>
        </View>

        {/* Legal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Legal</Text>

          <TouchableOpacity
            style={styles.legalItem}
            onPress={handleOpenTerms}
          >
            <Text style={styles.legalText}>Terms of Service</Text>
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.legalItem}
            onPress={handleOpenPrivacy}
          >
            <Text style={styles.legalText}>Privacy Policy</Text>
            <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* CAPTCHA Modal */}
      <CaptchaModal
        visible={isCaptchaModalVisible}
        onVerify={handleCaptchaVerify}
        onClose={closeCaptchaModal}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  section: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingVertical: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.textPrimary,
    flex: 1,
    marginRight: Spacing.sm,
  },
  faqAnswer: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: Spacing.sm,
    lineHeight: 20,
  },
  formGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  input: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    padding: Spacing.sm,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  textArea: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.sm,
    padding: Spacing.sm,
    fontSize: 16,
    color: Colors.textPrimary,
    minHeight: 100,
  },
  errorText: {
    fontSize: 12,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  actionButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  guideItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  guideText: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginLeft: Spacing.md,
  },
  videoItem: {
    marginBottom: Spacing.md,
  },
  videoThumbnail: {
    height: 180,
    backgroundColor: Colors.docPurple.DEFAULT + '80', // 50% opacity
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  videoTitle: {
    fontSize: 16,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  legalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  legalText: {
    fontSize: 16,
    color: Colors.textPrimary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginLeft: Spacing.sm,
  },
});

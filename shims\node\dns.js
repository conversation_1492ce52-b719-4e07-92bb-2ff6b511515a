// Minimal implementation of the Node.js dns module for React Native

// Mock DNS resolution functions
function lookup(hostname, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  options = options || {};
  
  process.nextTick(() => {
    callback(null, '127.0.0.1', 4);
  });
}

function lookupService(address, port, callback) {
  process.nextTick(() => {
    callback(null, 'localhost', 'http');
  });
}

function resolve(hostname, rrtype, callback) {
  if (typeof rrtype === 'function') {
    callback = rrtype;
    rrtype = 'A';
  }
  
  process.nextTick(() => {
    if (rrtype === 'A') {
      callback(null, ['127.0.0.1']);
    } else if (rrtype === 'AAAA') {
      callback(null, ['::1']);
    } else if (rrtype === 'CNAME') {
      callback(null, ['example.com']);
    } else if (rrtype === 'MX') {
      callback(null, [{ priority: 10, exchange: 'mail.example.com' }]);
    } else if (rrtype === 'NS') {
      callback(null, ['ns1.example.com', 'ns2.example.com']);
    } else if (rrtype === 'PTR') {
      callback(null, ['example.com']);
    } else if (rrtype === 'SOA') {
      callback(null, [{
        nsname: 'ns1.example.com',
        hostmaster: 'admin.example.com',
        serial: 2019010101,
        refresh: 3600,
        retry: 1800,
        expire: 604800,
        minttl: 86400
      }]);
    } else if (rrtype === 'SRV') {
      callback(null, [{
        priority: 10,
        weight: 10,
        port: 80,
        name: 'www.example.com'
      }]);
    } else if (rrtype === 'TXT') {
      callback(null, [['v=spf1 -all']]);
    } else {
      callback(new Error(`Unsupported rrtype: ${rrtype}`));
    }
  });
}

function resolve4(hostname, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    callback(null, ['127.0.0.1']);
  });
}

function resolve6(hostname, options, callback) {
  if (typeof options === 'function') {
    callback = options;
    options = {};
  }
  
  process.nextTick(() => {
    callback(null, ['::1']);
  });
}

function resolveCname(hostname, callback) {
  process.nextTick(() => {
    callback(null, ['example.com']);
  });
}

function resolveMx(hostname, callback) {
  process.nextTick(() => {
    callback(null, [{ priority: 10, exchange: 'mail.example.com' }]);
  });
}

function resolveNs(hostname, callback) {
  process.nextTick(() => {
    callback(null, ['ns1.example.com', 'ns2.example.com']);
  });
}

function resolvePtr(hostname, callback) {
  process.nextTick(() => {
    callback(null, ['example.com']);
  });
}

function resolveSoa(hostname, callback) {
  process.nextTick(() => {
    callback(null, {
      nsname: 'ns1.example.com',
      hostmaster: 'admin.example.com',
      serial: 2019010101,
      refresh: 3600,
      retry: 1800,
      expire: 604800,
      minttl: 86400
    });
  });
}

function resolveSrv(hostname, callback) {
  process.nextTick(() => {
    callback(null, [{
      priority: 10,
      weight: 10,
      port: 80,
      name: 'www.example.com'
    }]);
  });
}

function resolveTxt(hostname, callback) {
  process.nextTick(() => {
    callback(null, [['v=spf1 -all']]);
  });
}

function reverse(ip, callback) {
  process.nextTick(() => {
    callback(null, ['example.com']);
  });
}

// Synchronous versions (not actually synchronous, just for API compatibility)
function lookupSync(hostname, options) {
  return { address: '127.0.0.1', family: 4 };
}

module.exports = {
  lookup,
  lookupService,
  resolve,
  resolve4,
  resolve6,
  resolveCname,
  resolveMx,
  resolveNs,
  resolvePtr,
  resolveSoa,
  resolveSrv,
  resolveTxt,
  reverse,
  // Synchronous versions
  lookupSync,
  // Constants
  NODATA: 'ENODATA',
  FORMERR: 'EFORMERR',
  SERVFAIL: 'ESERVFAIL',
  NOTFOUND: 'ENOTFOUND',
  NOTIMP: 'ENOTIMP',
  REFUSED: 'EREFUSED',
  BADQUERY: 'EBADQUERY',
  BADNAME: 'EBADNAME',
  BADFAMILY: 'EBADFAMILY',
  BADRESP: 'EBADRESP',
  CONNREFUSED: 'ECONNREFUSED',
  TIMEOUT: 'ETIMEOUT',
  EOF: 'EOF',
  FILE: 'EFILE',
  NOMEM: 'ENOMEM',
  DESTRUCTION: 'EDESTRUCTION',
  BADSTR: 'EBADSTR',
  BADFLAGS: 'EBADFLAGS',
  NONAME: 'ENONAME',
  BADHINTS: 'EBADHINTS',
  NOTINITIALIZED: 'ENOTINITIALIZED',
  LOADIPHLPAPI: 'ELOADIPHLPAPI',
  ADDRGETNETWORKPARAMS: 'EADDRGETNETWORKPARAMS',
  CANCELLED: 'ECANCELLED'
};

import OpenAI from 'openai';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import Constants from 'expo-constants';

// DeepSeek API configuration from environment variables
const DEEPSEEK_API_KEY = Constants.expoConfig?.extra?.deepseekApiKey ||
                        process.env.EXPO_PUBLIC_DEEPSEEK_API_KEY ||
                        '';
const DEEPSEEK_BASE_URL = Constants.expoConfig?.extra?.deepseekBaseUrl ||
                         process.env.EXPO_PUBLIC_DEEPSEEK_BASE_URL ||
                         'https://api.deepseek.com';

// Initialize OpenAI client with DeepSeek configuration
const openai = new OpenAI({
  apiKey: DEEPSEEK_API_KEY,
  baseURL: DEEPSEEK_BASE_URL,
});

/**
 * Analyzes an image using the DeepSeek API
 * @param imageUri - The URI of the image to analyze
 * @param prompt - The prompt to use for analysis (default: "recap the image")
 * @returns The analysis result from DeepSeek
 */
export const analyzeImage = async (
  imageUri: string,
  prompt: string = 'recap the image'
): Promise<string> => {
  try {
    // For now, we'll return a simulated analysis since we're having issues with the DeepSeek API
    // This is a temporary solution until we can properly integrate with an image analysis API

    // Simulate a delay to make it feel like an API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    return (
      "Based on the image you've provided, I can see what appears to be a photograph. " +
      "While I can't actually see the image (as this is a simulated response), in a real implementation, " +
      "this would be replaced with actual AI analysis from a vision-capable model. " +
      "\n\nThe DeepSeek API we're currently using doesn't support direct image analysis through their standard API. " +
      "In a production app, we would integrate with a vision-capable AI model to provide real image analysis."
    );

    /* Commented out actual API call due to issues
    const response = await openai.chat.completions.create({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant that provides image analysis descriptions.'
        },
        {
          role: 'user',
          content: `I've taken a photo and would like you to provide a sample analysis. ${prompt}`
        },
      ],
      stream: false,
    });

    return response.choices[0]?.message?.content || 'No analysis available';
    */
  } catch (error) {
    console.error('Error analyzing image:', error);

    // Provide more user-friendly error messages
    if (error instanceof Error) {
      const errorMessage = error.message;
      if (errorMessage.includes('Insufficient Balance')) {
        return 'The API key has insufficient balance. Please contact the app administrator to resolve this issue.';
      } else if (errorMessage.includes('Failed to deserialize')) {
        return 'There was an issue with the API request format. The development team has been notified.';
      } else {
        return `Error analyzing image: ${errorMessage}`;
      }
    }

    return `Error analyzing image: ${String(error)}`;
  }
};

/**
 * Converts an image URI to base64
 * @param uri - The URI of the image
 * @returns The base64 representation of the image
 *
 * Note: This function is currently not used as DeepSeek API doesn't support
 * direct image analysis through their API. Keeping it for future use.
 */
const getBase64FromUri = async (uri: string): Promise<string> => {
  // For web platform
  if (Platform.OS === 'web') {
    const response = await fetch(uri);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        // Remove the data URL prefix (e.g., "data:image/jpeg;base64,")
        const base64 = base64String.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // For native platforms (iOS, Android)
  const base64 = await FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });

  return base64;
};

// Minimal implementation of the Node.js stream module for React Native
const EventEmitter = require('./events');

class Stream extends EventEmitter {
  constructor() {
    super();
  }

  pipe(dest) {
    return dest;
  }
}

class Readable extends Stream {
  constructor(options) {
    super();
    this._readableState = {
      flowing: null,
      ended: false,
      objectMode: options && options.objectMode
    };
  }

  _read() {}

  push(chunk) {
    if (chunk === null) {
      this._readableState.ended = true;
      this.emit('end');
    } else if (chunk) {
      this.emit('data', chunk);
    }
    return true;
  }

  pipe(dest) {
    this.on('data', (chunk) => {
      dest.write(chunk);
    });
    this.on('end', () => {
      dest.end();
    });
    return dest;
  }
}

class Writable extends Stream {
  constructor(options) {
    super();
    this._writableState = {
      ended: false,
      finished: false,
      objectMode: options && options.objectMode
    };
  }

  _write() {}

  write(chunk) {
    this._write(chunk);
    return true;
  }

  end(data) {
    if (data) {
      this.write(data);
    }
    this._writableState.ended = true;
    this._writableState.finished = true;
    this.emit('finish');
    return this;
  }
}

class Duplex extends Readable {
  constructor(options) {
    super(options);
    Writable.call(this, options);
    this._writableState = {
      ended: false,
      finished: false,
      objectMode: options && options.objectMode
    };
  }

  write(chunk) {
    return Writable.prototype.write.call(this, chunk);
  }

  end(data) {
    Writable.prototype.end.call(this, data);
    return this;
  }
}

class Transform extends Duplex {
  constructor(options) {
    super(options);
  }

  _transform() {}
}

class PassThrough extends Transform {
  constructor(options) {
    super(options);
  }

  _transform(chunk, encoding, callback) {
    this.push(chunk);
    callback();
  }
}

module.exports = Stream;
module.exports.Stream = Stream;
module.exports.Readable = Readable;
module.exports.Writable = Writable;
module.exports.Duplex = Duplex;
module.exports.Transform = Transform;
module.exports.PassThrough = PassThrough;

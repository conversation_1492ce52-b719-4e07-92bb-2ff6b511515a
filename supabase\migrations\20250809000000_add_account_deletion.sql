-- Account Deletion Migration
-- This migration adds functionality for users to delete their accounts
-- as required by Apple App Store Review Guidelines 5.1.1(v)

-- Create a function to delete all user data and account
CREATE OR REPLACE FUNCTION public.delete_user_account(
  user_uuid UUID
)
R<PERSON><PERSON>NS BOOLEAN AS $$
DECLARE
  user_exists BOOLEAN;
  deletion_successful BOOLEAN := FALSE;
BEGIN
  -- Check if user exists
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = user_uuid) INTO user_exists;
  
  IF NOT user_exists THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Start transaction for data deletion
  BEGIN
    -- Delete user data from all tables (in order to respect foreign key constraints)
    
    -- 1. Delete purchase validation logs
    DELETE FROM public.purchase_validation_log WHERE user_id = user_uuid;
    
    -- 2. Delete live session tracking
    DELETE FROM public.live_session_tracking WHERE user_id = user_uuid;
    
    -- 3. Delete daily feature usage
    DELETE FROM public.daily_feature_usage WHERE user_id = user_uuid;
    
    -- 4. Delete feature usage records
    DELETE FROM public.feature_usage WHERE user_id = user_uuid;
    
    -- 5. Delete usage logs (if exists)
    DELETE FROM public.usage_logs WHERE user_id = user_uuid;
    
    -- 6. Delete subscriptions (if exists)
    DELETE FROM public.subscriptions WHERE user_id = user_uuid;
    
    -- 7. Delete profile (this will cascade to other related data)
    DELETE FROM public.profiles WHERE id = user_uuid;
    
    -- 8. Finally, delete the auth user (this is the main account)
    -- Note: This will also trigger any CASCADE deletes we might have missed
    DELETE FROM auth.users WHERE id = user_uuid;
    
    deletion_successful := TRUE;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't expose details to client
      RAISE LOG 'Error deleting user account %: %', user_uuid, SQLERRM;
      deletion_successful := FALSE;
  END;
  
  RETURN deletion_successful;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to schedule account deletion (for users who want to delay deletion)
CREATE OR REPLACE FUNCTION public.schedule_account_deletion(
  user_uuid UUID,
  deletion_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Create or update scheduled deletion
  INSERT INTO public.scheduled_deletions (user_id, scheduled_for, created_at)
  VALUES (user_uuid, deletion_date, NOW())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    scheduled_for = deletion_date,
    updated_at = NOW();
    
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create table to track scheduled deletions
CREATE TABLE IF NOT EXISTS public.scheduled_deletions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for scheduled_deletions
ALTER TABLE public.scheduled_deletions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own scheduled deletion
CREATE POLICY "Users can view their own scheduled deletion"
  ON public.scheduled_deletions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can only insert their own scheduled deletion
CREATE POLICY "Users can insert their own scheduled deletion"
  ON public.scheduled_deletions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own scheduled deletion
CREATE POLICY "Users can update their own scheduled deletion"
  ON public.scheduled_deletions
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own scheduled deletion (to cancel)
CREATE POLICY "Users can delete their own scheduled deletion"
  ON public.scheduled_deletions
  FOR DELETE
  USING (auth.uid() = user_id);

-- Function to cancel scheduled deletion
CREATE OR REPLACE FUNCTION public.cancel_scheduled_deletion(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM public.scheduled_deletions WHERE user_id = user_uuid;
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process scheduled deletions (to be called by a cron job)
CREATE OR REPLACE FUNCTION public.process_scheduled_deletions()
RETURNS INTEGER AS $$
DECLARE
  deletion_count INTEGER := 0;
  user_record RECORD;
BEGIN
  -- Find all users scheduled for deletion
  FOR user_record IN 
    SELECT user_id 
    FROM public.scheduled_deletions 
    WHERE scheduled_for <= NOW()
  LOOP
    -- Attempt to delete the user account
    IF public.delete_user_account(user_record.user_id) THEN
      deletion_count := deletion_count + 1;
    END IF;
  END LOOP;
  
  RETURN deletion_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.delete_user_account(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.schedule_account_deletion(UUID, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION public.cancel_scheduled_deletion(UUID) TO authenticated;

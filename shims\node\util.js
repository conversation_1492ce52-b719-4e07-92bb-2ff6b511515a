// Minimal implementation of the Node.js util module for React Native

function format(format, ...args) {
  if (typeof format !== 'string') {
    const objects = [format, ...args];
    return objects.map(obj => inspect(obj)).join(' ');
  }
  
  let i = 0;
  return format.replace(/%[sdjifoO%]/g, match => {
    if (match === '%%') return '%';
    if (i >= args.length) return match;
    
    const arg = args[i++];
    switch (match) {
      case '%s': return String(arg);
      case '%d': return Number(arg).toString();
      case '%i': return parseInt(arg).toString();
      case '%f': return parseFloat(arg).toString();
      case '%j': return JSON.stringify(arg);
      case '%o': case '%O': return inspect(arg);
      default: return match;
    }
  });
}

function inspect(obj, options = {}) {
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';
  
  if (typeof obj === 'string') {
    return `'${obj}'`;
  }
  
  if (typeof obj === 'number' || typeof obj === 'boolean' || typeof obj === 'symbol') {
    return obj.toString();
  }
  
  if (typeof obj === 'function') {
    return `[Function: ${obj.name || 'anonymous'}]`;
  }
  
  if (Array.isArray(obj)) {
    const items = obj.map(item => inspect(item, options));
    return `[ ${items.join(', ')} ]`;
  }
  
  if (obj instanceof Date) {
    return obj.toISOString();
  }
  
  if (obj instanceof RegExp) {
    return obj.toString();
  }
  
  if (obj instanceof Error) {
    return `${obj.name}: ${obj.message}`;
  }
  
  if (typeof obj === 'object') {
    const keys = Object.keys(obj);
    const props = keys.map(key => `${key}: ${inspect(obj[key], options)}`);
    return `{ ${props.join(', ')} }`;
  }
  
  return String(obj);
}

function inherits(constructor, superConstructor) {
  if (constructor === undefined || constructor === null) {
    throw new TypeError('The constructor to "inherits" must not be null or undefined');
  }
  
  if (superConstructor === undefined || superConstructor === null) {
    throw new TypeError('The super constructor to "inherits" must not be null or undefined');
  }
  
  if (superConstructor.prototype === undefined) {
    throw new TypeError('The super constructor to "inherits" must have a prototype');
  }
  
  constructor.super_ = superConstructor;
  Object.setPrototypeOf(constructor.prototype, superConstructor.prototype);
}

function deprecate(fn, msg) {
  let warned = false;
  
  return function(...args) {
    if (!warned) {
      console.warn(msg);
      warned = true;
    }
    return fn.apply(this, args);
  };
}

function isArray(obj) {
  return Array.isArray(obj);
}

function isBoolean(obj) {
  return typeof obj === 'boolean';
}

function isBuffer(obj) {
  return obj && typeof obj === 'object' && obj.constructor && obj.constructor.name === 'Buffer';
}

function isDate(obj) {
  return obj instanceof Date;
}

function isError(obj) {
  return obj instanceof Error;
}

function isFunction(obj) {
  return typeof obj === 'function';
}

function isNull(obj) {
  return obj === null;
}

function isNullOrUndefined(obj) {
  return obj === null || obj === undefined;
}

function isNumber(obj) {
  return typeof obj === 'number';
}

function isObject(obj) {
  return typeof obj === 'object' && obj !== null;
}

function isPrimitive(obj) {
  return obj === null || 
         typeof obj === 'boolean' ||
         typeof obj === 'number' ||
         typeof obj === 'string' ||
         typeof obj === 'symbol' ||
         typeof obj === 'undefined';
}

function isRegExp(obj) {
  return obj instanceof RegExp;
}

function isString(obj) {
  return typeof obj === 'string';
}

function isSymbol(obj) {
  return typeof obj === 'symbol';
}

function isUndefined(obj) {
  return obj === undefined;
}

function promisify(fn) {
  return function(...args) {
    return new Promise((resolve, reject) => {
      fn.call(this, ...args, (err, ...values) => {
        if (err) {
          reject(err);
        } else {
          resolve(values.length === 1 ? values[0] : values);
        }
      });
    });
  };
}

const callbackify = (fn) => {
  return function(...args) {
    const callback = args.pop();
    if (typeof callback !== 'function') {
      throw new TypeError('The last argument must be a callback function');
    }
    
    fn.apply(this, args)
      .then(result => {
        process.nextTick(() => callback(null, result));
      })
      .catch(err => {
        process.nextTick(() => callback(err));
      });
  };
};

module.exports = {
  format,
  inspect,
  inherits,
  deprecate,
  isArray,
  isBoolean,
  isBuffer,
  isDate,
  isError,
  isFunction,
  isNull,
  isNullOrUndefined,
  isNumber,
  isObject,
  isPrimitive,
  isRegExp,
  isString,
  isSymbol,
  isUndefined,
  promisify,
  callbackify,
  // Add other util functions as needed
  types: {
    isAnyArrayBuffer: (obj) => obj instanceof ArrayBuffer,
    isArrayBufferView: (obj) => ArrayBuffer.isView(obj),
    isAsyncFunction: (obj) => Object.prototype.toString.call(obj) === '[object AsyncFunction]',
    isBigInt64Array: (obj) => false, // Not supported in React Native
    isBigUint64Array: (obj) => false, // Not supported in React Native
    isDataView: (obj) => obj instanceof DataView,
    isDate: isDate,
    isExternal: (obj) => false, // Not supported in React Native
    isFloat32Array: (obj) => obj instanceof Float32Array,
    isFloat64Array: (obj) => obj instanceof Float64Array,
    isGeneratorFunction: (obj) => Object.prototype.toString.call(obj) === '[object GeneratorFunction]',
    isGeneratorObject: (obj) => Object.prototype.toString.call(obj) === '[object Generator]',
    isInt8Array: (obj) => obj instanceof Int8Array,
    isInt16Array: (obj) => obj instanceof Int16Array,
    isInt32Array: (obj) => obj instanceof Int32Array,
    isMap: (obj) => obj instanceof Map,
    isMapIterator: (obj) => false, // Not easily detectable in React Native
    isModuleNamespaceObject: (obj) => false, // Not easily detectable in React Native
    isNativeError: (obj) => obj instanceof Error,
    isPromise: (obj) => obj instanceof Promise,
    isProxy: (obj) => false, // Not easily detectable in React Native
    isRegExp: isRegExp,
    isSet: (obj) => obj instanceof Set,
    isSetIterator: (obj) => false, // Not easily detectable in React Native
    isSharedArrayBuffer: (obj) => false, // Not supported in React Native
    isTypedArray: (obj) => ArrayBuffer.isView(obj) && !(obj instanceof DataView),
    isUint8Array: (obj) => obj instanceof Uint8Array,
    isUint8ClampedArray: (obj) => obj instanceof Uint8ClampedArray,
    isUint16Array: (obj) => obj instanceof Uint16Array,
    isUint32Array: (obj) => obj instanceof Uint32Array,
    isWeakMap: (obj) => obj instanceof WeakMap,
    isWeakSet: (obj) => obj instanceof WeakSet
  }
};

import Constants from 'expo-constants';

// Initialize with API key from environment variables
const resendApiKey = Constants.expoConfig?.extra?.resendApiKey || process.env.EXPO_PUBLIC_RESEND_API_KEY;

// Default sender email - using verified domain
export const DEFAULT_FROM_EMAIL = '<EMAIL>';

// Support email address
export const SUPPORT_EMAIL = '<EMAIL>';

/**
 * Simple function to send an email using Resend API directly
 * This avoids using the @react-email/render package which has Node.js dependencies
 */
export const sendEmail = async (options: {
  from: string;
  to: string;
  subject: string;
  html: string;
  replyTo?: string;
}) => {
  try {
    if (!resendApiKey) {
      console.warn('Resend API key is not set. Email functionality will not work.');
      return { success: false, error: 'API key not set' };
    }

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${resendApiKey}`
      },
      body: JSON.stringify({
        from: options.from,
        to: options.to,
        subject: options.subject,
        html: options.html,
        reply_to: options.replyTo
      })
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Error sending email:', data);
      return { success: false, error: data };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception sending email:', error);
    return { success: false, error };
  }
};

// Email templates
export const sendWelcomeEmail = async (to: string, name: string = 'there') => {
  return sendEmail({
    from: DEFAULT_FROM_EMAIL,
    to,
    subject: 'Welcome to PillLogic!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #6941C6;">Welcome to PillLogic!</h1>
        <p>Hi ${name},</p>
        <p>Thank you for signing up for PillLogic. We're excited to have you on board!</p>
        <p>If you have any questions, feel free to reply to this email.</p>
        <p>Best regards,<br>The PillLogic Team</p>
      </div>
    `
  });
};

// Password reset email
export const sendPasswordResetEmail = async (to: string, resetLink: string) => {
  return sendEmail({
    from: DEFAULT_FROM_EMAIL,
    to,
    subject: 'Reset Your PillLogic Password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #6941C6;">Reset Your Password</h1>
        <p>You requested a password reset for your PillLogic account.</p>
        <p>Click the button below to reset your password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetLink}" style="background-color: #6941C6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
        </div>
        <p>If you didn't request this, you can safely ignore this email.</p>
        <p>This link will expire in 24 hours.</p>
        <p>Best regards,<br>The PillLogic Team</p>
      </div>
    `
  });
};

// Email verification
export const sendVerificationEmail = async (to: string, verificationLink: string) => {
  return sendEmail({
    from: DEFAULT_FROM_EMAIL,
    to,
    subject: 'Verify Your PillLogic Email',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #6941C6;">Verify Your Email</h1>
        <p>Thank you for signing up for PillLogic. Please verify your email address to complete your registration.</p>
        <p>Click the button below to verify your email:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationLink}" style="background-color: #6941C6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
        </div>
        <p>If you didn't create an account, you can safely ignore this email.</p>
        <p>This link will expire in 24 hours.</p>
        <p>Best regards,<br>The PillLogic Team</p>
      </div>
    `
  });
};

// Support email
export const sendSupportEmail = async (
  userEmail: string,
  subject: string,
  message: string,
  userName: string = 'User'
) => {
  try {
    // Send to support
    const supportResult = await sendEmail({
      from: DEFAULT_FROM_EMAIL,
      to: SUPPORT_EMAIL,
      replyTo: userEmail,
      subject: `PillLogic Support: ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">PillLogic Support Request</h1>
          <p><strong>From:</strong> ${userName} (${userEmail})</p>
          <p><strong>Subject:</strong> ${subject}</p>
          <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-left: 4px solid #6941C6;">
            <p><strong>Message:</strong></p>
            <p>${message.replace(/\\n/g, '<br>')}</p>
          </div>
          <p style="color: #666; font-size: 12px;">This message was sent from the PillLogic app's support form.</p>
        </div>
      `
    });

    if (!supportResult.success) {
      return supportResult;
    }

    // Send confirmation to user
    await sendEmail({
      from: DEFAULT_FROM_EMAIL,
      to: userEmail,
      subject: 'Your PillLogic Support Request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6941C6;">Support Request Received</h1>
          <p>Hi ${userName},</p>
          <p>We've received your support request with the subject: <strong>${subject}</strong></p>
          <p>Our team will review your message and get back to you as soon as possible.</p>
          <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-left: 4px solid #6941C6;">
            <p><strong>Your message:</strong></p>
            <p>${message.replace(/\\n/g, '<br>')}</p>
          </div>
          <p>Best regards,<br>The PillLogic Support Team</p>
        </div>
      `
    });

    return { success: true, data: supportResult.data };
  } catch (error) {
    console.error('Exception sending support email:', error);
    return { success: false, error };
  }
};

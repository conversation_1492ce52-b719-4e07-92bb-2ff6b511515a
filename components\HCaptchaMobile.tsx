import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { WebView } from 'react-native-webview';
import Constants from 'expo-constants';
import { Colors } from '../constants/PillLogicDesign';

// Define the props interface
interface HCaptchaMobileProps {
  siteKey: string;
  onVerify: (token: string) => void;
  onError?: (error: Error) => void;
  onExpire?: () => void;
  theme?: 'light' | 'dark';
}

// Define the ref interface
export interface HCaptchaMobileRef {
  resetCaptcha: () => void;
  execute: () => void;
}

/**
 * A WebView-based hCaptcha component for mobile platforms
 */
const HCaptchaMobile = forwardRef<HCaptchaMobileRef, HCaptchaMobileProps>((props, ref) => {
  const {
    siteKey,
    onVerify,
    onError,
    onExpire,
    theme = 'light'
  } = props;

  const [loading, setLoading] = useState(true);
  const webViewRef = useRef<WebView>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    resetCaptcha: () => {
      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(`
          try {
            if (window.hcaptcha) {
              window.hcaptcha.reset();
              true;
            } else {
              console.log('hCaptcha not found');
              false;
            }
          } catch (e) {
            console.error('Error resetting hCaptcha:', e);
            false;
          }
        `);
      }
    },
    execute: () => {
      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(`
          try {
            if (window.hcaptcha) {
              window.hcaptcha.execute();
              true;
            } else {
              console.log('hCaptcha not found');
              false;
            }
          } catch (e) {
            console.error('Error executing hCaptcha:', e);
            false;
          }
        `);
      }
    }
  }));

  // Handle messages from the WebView
  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      if (data.type === 'verify') {
        console.log('hCaptcha verified with token:', data.token.substring(0, 10) + '...');
        onVerify(data.token);
      } else if (data.type === 'error') {
        console.error('hCaptcha error:', data.error);
        if (onError) {
          onError(new Error(data.error));
        }
      } else if (data.type === 'expire') {
        console.log('hCaptcha token expired');
        if (onExpire) {
          onExpire();
        }
      } else if (data.type === 'load') {
        console.log('hCaptcha loaded');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error parsing message from WebView:', error);
    }
  };

  // Create HTML content with hCaptcha
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
      <meta http-equiv="Content-Security-Policy" content="default-src * 'self' data: gap: https://ssl.gstatic.com 'unsafe-eval' 'unsafe-inline'; connect-src * 'self'; style-src * 'self' 'unsafe-inline'; media-src * 'self'; img-src * data: blob: 'self'; font-src * 'self'; script-src * 'self' https://js.hcaptcha.com https://*.hcaptcha.com 'unsafe-inline' 'unsafe-eval';">
      <style>
        body {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0;
          padding: 0;
          height: 100vh;
          background-color: ${theme === 'dark' ? '#1a1a1a' : '#ffffff'};
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }
        .h-captcha {
          display: inline-block;
          margin: 0 auto;
          min-height: 80px;
          min-width: 300px;
          border: 1px dashed #ccc;
        }
        .container {
          width: 100%;
          text-align: center;
          padding: 20px;
        }
        #captcha-container {
          min-height: 80px;
          margin: 20px auto;
          width: 100%;
        }
        .loading {
          margin-top: 20px;
          font-size: 14px;
          color: #666;
        }
        .error {
          margin-top: 20px;
          color: red;
          font-size: 14px;
          padding: 10px;
          background-color: rgba(255, 0, 0, 0.1);
          border-radius: 4px;
        }
        .debug {
          margin-top: 10px;
          font-size: 12px;
          color: #999;
          text-align: center;
          padding: 5px;
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div id="captcha-container">
          <div
            class="h-captcha"
            data-sitekey="${siteKey}"
            data-theme="${theme}"
            data-callback="onVerify"
            data-error-callback="onError"
            data-expired-callback="onExpire"
            data-size="normal"
          ></div>
        </div>
        <div id="loading" class="loading">Loading CAPTCHA...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="debug" class="debug"></div>
      </div>

      <script>
        // Debug info
        document.getElementById('debug').textContent = 'Site Key: ' + '${siteKey.substring(0, 8)}...';

        // Function to load hCaptcha script
        function loadHCaptchaScript() {
          try {
            // Use the standard API script without explicit rendering
            var script = document.createElement('script');
            script.src = 'https://js.hcaptcha.com/1/api.js';
            script.async = true;
            script.defer = true;
            script.onerror = function(e) {
              console.error('Script load error:', e);
              document.getElementById('error').textContent = 'Failed to load CAPTCHA. Please try again.';
              document.getElementById('error').style.display = 'block';
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'error',
                error: 'Failed to load hCaptcha script'
              }));
            };

            // When the script loads, hide the loading message
            script.onload = function() {
              console.log('hCaptcha script loaded');
              document.getElementById('loading').style.display = 'none';

              // Check if the widget is visible
              setTimeout(function() {
                var captchaIframe = document.querySelector('iframe[src*="hcaptcha.com"]');
                if (captchaIframe) {
                  console.log('hCaptcha iframe found');
                  document.getElementById('debug').textContent = 'Site Key: ${siteKey.substring(0, 8)}... | hCaptcha loaded successfully';
                } else {
                  console.error('hCaptcha iframe not found');
                  document.getElementById('debug').textContent = 'Site Key: ${siteKey.substring(0, 8)}... | Widget not visible';

                  // Try to force render the widget
                  try {
                    document.getElementById('captcha-container').innerHTML = '';
                    var newCaptchaDiv = document.createElement('div');
                    newCaptchaDiv.className = 'h-captcha';
                    newCaptchaDiv.setAttribute('data-sitekey', '${siteKey}');
                    newCaptchaDiv.setAttribute('data-callback', 'onVerify');
                    newCaptchaDiv.setAttribute('data-error-callback', 'onError');
                    newCaptchaDiv.setAttribute('data-expired-callback', 'onExpire');
                    document.getElementById('captcha-container').appendChild(newCaptchaDiv);

                    // Try to render manually if hcaptcha is available
                    if (window.hcaptcha) {
                      window.hcaptcha.render(newCaptchaDiv, {
                        sitekey: '${siteKey}',
                        callback: onVerify,
                        'error-callback': onError,
                        'expired-callback': onExpire
                      });
                    }
                  } catch (renderErr) {
                    console.error('Error forcing widget render:', renderErr);
                  }
                }
              }, 1000);

              // Notify React Native that hCaptcha script is loaded
              try {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'load'
                }));
                console.log('Load message posted to React Native');
              } catch (err) {
                console.error('Error posting load message:', err);
              }
            };

            document.head.appendChild(script);
            console.log('hCaptcha script added to DOM');
          } catch (err) {
            console.error('Error adding script:', err);
            document.getElementById('error').textContent = 'Error setting up CAPTCHA: ' + err.message;
            document.getElementById('error').style.display = 'block';
          }
        }

        // Load hCaptcha script when the page is loaded
        document.addEventListener('DOMContentLoaded', function() {
          console.log('DOM content loaded, loading hCaptcha script');
          loadHCaptchaScript();
        });

        // Callback functions for hCaptcha
        function onVerify(token) {
          console.log('Verification callback fired with token');
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'verify',
              token: token
            }));
            console.log('Verification message posted to React Native');
          } catch (err) {
            console.error('Error posting verification message:', err);
            document.getElementById('error').textContent = 'Error sending verification: ' + err.message;
            document.getElementById('error').style.display = 'block';
          }
        }

        function onError(error) {
          console.error('hCaptcha error:', error);

          // Display detailed error message
          let errorMessage = 'CAPTCHA error: ' + error;

          // Add specific guidance for common errors
          if (error === 'invalid-data') {
            errorMessage += '. This may be due to domain restrictions. Please ensure the site key is configured for this domain.';
            console.error('Domain validation issue detected. Current URL:', window.location.href);
            console.error('Site key may not be authorized for this domain or environment');

            // Try to log additional debugging information
            try {
              const siteInfo = {
                href: window.location.href,
                origin: window.location.origin,
                host: window.location.host,
                pathname: window.location.pathname,
                userAgent: navigator.userAgent
              };
              console.log('Site information:', JSON.stringify(siteInfo));
            } catch (e) {
              console.error('Error logging site info:', e);
            }
          }

          document.getElementById('error').textContent = errorMessage;
          document.getElementById('error').style.display = 'block';

          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'error',
              error: error,
              details: errorMessage
            }));
          } catch (err) {
            console.error('Error posting error message:', err);
          }
        }

        function onExpire() {
          console.log('Token expired callback fired');
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'expire'
            }));
          } catch (err) {
            console.error('Error posting expire message:', err);
          }
        }

        // Additional error handling
        window.onerror = function(message, source, lineno, colno, error) {
          console.error('Global error:', message, 'at', source, ':', lineno);
          document.getElementById('error').textContent = 'Error: ' + message;
          document.getElementById('error').style.display = 'block';
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'error',
              error: message
            }));
          } catch (err) {
            console.error('Error posting global error message:', err);
          }
          return true;
        };
      </script>
    </body>
    </html>
  `;

  return (
    <View style={styles.container}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
        </View>
      )}
      <WebView
        ref={webViewRef}
        originWhitelist={['*']}
        source={{
          html: htmlContent,
          baseUrl: 'https://pilllogic.app/' // Add a trusted base URL to help with domain validation
        }}
        onMessage={handleMessage}
        style={styles.webView}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        allowFileAccess={true}
        cacheEnabled={false} // Disable cache to prevent stale content
        allowUniversalAccessFromFileURLs={true}
        allowFileAccessFromFileURLs={true}
        mixedContentMode="always"
        userAgent="Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1" // Use a standard mobile user agent
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('WebView error:', nativeEvent);
          if (onError) {
            onError(new Error(`WebView error: ${nativeEvent.description}`));
          }
        }}
        onHttpError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('WebView HTTP error:', nativeEvent);
          if (onError) {
            onError(new Error(`WebView HTTP error: ${nativeEvent.statusCode}`));
          }
        }}
        onLoadEnd={() => {
          console.log('WebView load ended');
          // Inject additional script to check if hCaptcha is loaded
          webViewRef.current?.injectJavaScript(`
            console.log('Injecting hCaptcha check script');
            setTimeout(function() {
              try {
                if (typeof window.hcaptcha === 'undefined') {
                  console.error('hCaptcha not found in window object');
                  document.getElementById('error').textContent = 'hCaptcha not loaded. Please try again.';
                  document.getElementById('error').style.display = 'block';
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'error',
                    error: 'hCaptcha not loaded'
                  }));
                } else {
                  console.log('hCaptcha is loaded and available');
                  document.getElementById('debug').textContent += ' | hCaptcha loaded successfully';
                }
              } catch (err) {
                console.error('Error checking hCaptcha:', err);
                document.getElementById('error').textContent = 'Error checking hCaptcha: ' + err.message;
                document.getElementById('error').style.display = 'block';
              }
            }, 3000);
            true;
          `);
        }}
        onNavigationStateChange={(navState) => {
          // Log navigation state changes for debugging
          console.log('WebView navigation state changed:', navState.url);
        }}
        // Additional script injection is handled in the first onLoadEnd
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 400, // Further increased height to ensure the widget is fully visible
    overflow: 'hidden',
    borderWidth: 0,
  },
  webView: {
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    zIndex: 1,
  },
});

export default HCaptchaMobile;

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface MedicationData {
  id: string;
  name: string;
  dosage: string;
  purpose: string;
  fillDate: string;
  dateAdded: string;
  imageUri?: string;
}

export interface StoredAnalysis {
  id: string;
  name: string;
  date: string;
  medications: MedicationData[];
  imageUri?: string;
  type?: string; // For distinguishing between medication analyses and pill counts
}

// Key for storing all analyses
const STORAGE_KEY = 'medication_analyses';

/**
 * Save a new medication analysis
 * @param data - Either an array of medications or a complete analysis object
 * @param analysisName - Optional name for the analysis (used only when medications array is provided)
 * @param imageUri - Optional URI of the analyzed image (used only when medications array is provided)
 * @param type - Optional type of analysis ('medication' or 'pill-count'). Default is 'medication'
 * @returns The saved analysis object
 */
export const saveMedicationAnalysis = async (
  data: { name: string; dosage: string; purpose: string; fillDate: string }[] | {
    name: string;
    date: string;
    medications: { name: string; dosage: string; purpose: string; fillDate: string }[];
    imageUri?: string;
    type?: string;
  },
  analysisName: string = '',
  imageUri?: string,
  type: string = 'medication' // Default type is 'medication'
): Promise<StoredAnalysis> => {
  try {
    // Get existing analyses
    const existingData = await getMedicationAnalyses();
    let newAnalysis: StoredAnalysis;

    // Check if data is an array (old format) or an object (new format)
    if (Array.isArray(data)) {
      // Old format: array of medications
      const medications = data;

      // Generate a default name if none provided
      const defaultName = analysisName ||
        (medications.length > 0 ?
          `${medications[0].name} + ${medications.length - 1} more` :
          `Analysis ${new Date().toLocaleDateString()}`);

      newAnalysis = {
        id: Date.now().toString(),
        name: defaultName,
        date: new Date().toISOString(),
        medications: medications.map(med => ({
          ...med,
          id: Math.random().toString(36).substring(2, 15),
          dateAdded: new Date().toISOString(),
          imageUri
        })),
        imageUri,
        type // Use the provided type parameter
      };
    } else {
      // New format: complete analysis object
      const analysisData = data;

      // Create medications array with proper format
      const formattedMedications = analysisData.medications?.map(med => ({
        ...med,
        id: Math.random().toString(36).substring(2, 15),
        dateAdded: new Date().toISOString(),
        imageUri: analysisData.imageUri
      })) || [];

      newAnalysis = {
        id: Date.now().toString(),
        name: analysisData.name,
        date: analysisData.date || new Date().toISOString(),
        medications: formattedMedications,
        imageUri: analysisData.imageUri,
        type: analysisData.type
      };
    }

    // Add new analysis to the list
    const updatedData = [newAnalysis, ...existingData];

    // Save to AsyncStorage
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));

    return newAnalysis;
  } catch (error) {
    console.error('Error saving medication analysis:', error);
    throw error;
  }
};

/**
 * Get all saved medication analyses
 * @returns Array of stored analyses
 */
export const getMedicationAnalyses = async (): Promise<StoredAnalysis[]> => {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEY);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error getting medication analyses:', error);
    return [];
  }
};

/**
 * Get a specific medication analysis by ID
 * @param id - The ID of the analysis to retrieve
 * @returns The analysis object or null if not found
 */
export const getMedicationAnalysisById = async (id: string): Promise<StoredAnalysis | null> => {
  try {
    console.log('Getting medication analysis by ID:', id);
    const analyses = await getMedicationAnalyses();
    console.log('Found', analyses.length, 'total analyses');
    const analysis = analyses.find(analysis => analysis.id === id);
    console.log('Analysis found:', analysis ? 'Yes' : 'No');
    if (analysis) {
      console.log('Analysis type:', analysis.type || 'not specified');
      console.log('Analysis medications count:', analysis.medications?.length || 0);
    }
    return analysis || null;
  } catch (error) {
    console.error('Error getting medication analysis by ID:', error);
    return null;
  }
};

/**
 * Delete a medication analysis by ID
 * @param id - The ID of the analysis to delete
 * @returns True if successful, false otherwise
 */
export const deleteMedicationAnalysis = async (id: string): Promise<boolean> => {
  try {
    const analyses = await getMedicationAnalyses();
    const updatedAnalyses = analyses.filter(analysis => analysis.id !== id);

    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedAnalyses));
    return true;
  } catch (error) {
    console.error('Error deleting medication analysis:', error);
    return false;
  }
};

/**
 * Clear all saved medication analyses
 * @returns True if successful, false otherwise
 */
export const clearAllMedicationAnalyses = async (): Promise<boolean> => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEY);
    console.log('All medication analyses cleared');
    return true;
  } catch (error) {
    console.error('Error clearing medication analyses:', error);
    return false;
  }
};

/**
 * Search medication analyses by name or date
 * @param query - The search query
 * @returns Array of analyses matching the query
 */
export const searchMedicationAnalyses = async (query: string): Promise<StoredAnalysis[]> => {
  try {
    const allAnalyses = await getMedicationAnalyses();
    if (!query) return allAnalyses;

    const lowerQuery = query.toLowerCase();

    return allAnalyses.filter(analysis => {
      try {
        // Search by name (safely check for undefined)
        if (analysis.name && typeof analysis.name === 'string' && analysis.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }

        // Search by date
        if (analysis.date && typeof analysis.date === 'string') {
          const date = new Date(analysis.date);
          if (!isNaN(date.getTime())) { // Check if date is valid
            const dateStr = date.toLocaleDateString();
            if (dateStr.includes(lowerQuery)) return true;
          }
        }

        // Search by medication names (safely check for undefined)
        if (analysis.medications && Array.isArray(analysis.medications) && analysis.medications.length > 0) {
          const hasMedication = analysis.medications.some(med =>
            med && med.name && typeof med.name === 'string' && med.name.toLowerCase().includes(lowerQuery));
          if (hasMedication) return true;
        }

        return false;
      } catch (innerError) {
        console.error('Error filtering analysis:', innerError);
        return false; // Skip this item if there's an error
      }
    });
  } catch (error) {
    console.error('Error searching medication analyses:', error);
    return [];
  }
};

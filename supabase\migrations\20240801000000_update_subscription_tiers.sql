-- Update subscription tier system
-- This migration adds support for multiple subscription tiers with different feature limits

-- First, let's update the can_use_feature function to handle different tiers
CREATE OR REPLACE FUNCTION public.can_use_feature(
  user_uuid UUID, 
  feature TEXT
) 
RETURNS BOOLEAN AS $$
DECLARE
  user_tier TEXT;
  current_count INTEGER;
  max_limit INTEGER;
BEGIN
  -- Get user's subscription tier
  SELECT COALESCE(subscription_tier, 'free') INTO user_tier
  FROM public.profiles 
  WHERE id = user_uuid;
  
  -- Admin users have no limits
  IF user_tier = 'admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Get current usage count
  SELECT usage_count INTO current_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid 
    AND feature_type = feature 
    AND usage_date = CURRENT_DATE;
    
  -- If no record exists, count is 0
  IF current_count IS NULL THEN
    current_count := 0;
  END IF;
  
  -- Set max limit based on feature type and subscription tier
  IF feature = 'pill_scan' THEN
    -- Pill scan limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 10; -- Free users: 10 per day
    ELSIF user_tier = 'pro' OR user_tier = 'premium' THEN
      max_limit := 2147483647; -- Pro/Premium: unlimited (using max int)
    ELSE
      max_limit := 10; -- Default
    END IF;
  ELSIF feature = 'live_pill_scan' THEN
    -- Live pill scan limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 0; -- Free users: not available
    ELSIF user_tier = 'pro' THEN
      max_limit := 5; -- Pro: 5 per day
    ELSIF user_tier = 'premium' THEN
      max_limit := 2147483647; -- Premium: unlimited
    ELSE
      max_limit := 0; -- Default
    END IF;
  ELSIF feature = 'note_analysis' THEN
    -- Note analysis limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 10; -- Free users: 10 per day
    ELSIF user_tier = 'pro' THEN
      max_limit := 0; -- Pro: not available
    ELSIF user_tier = 'premium' THEN
      max_limit := 2147483647; -- Premium: unlimited
    ELSE
      max_limit := 10; -- Default
    END IF;
  ELSE
    -- Default for other features
    max_limit := 10;
  END IF;
  
  -- Return whether user can use the feature
  RETURN current_count < max_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the track_daily_feature_usage function to handle different tiers
CREATE OR REPLACE FUNCTION public.track_daily_feature_usage(
  user_uuid UUID, 
  feature TEXT
) 
RETURNS BOOLEAN AS $$
DECLARE
  user_tier TEXT;
  current_count INTEGER;
  max_limit INTEGER;
  can_use BOOLEAN;
BEGIN
  -- Get user's subscription tier
  SELECT COALESCE(subscription_tier, 'free') INTO user_tier
  FROM public.profiles 
  WHERE id = user_uuid;
  
  -- Admin users have no limits but we still track usage
  IF user_tier = 'admin' THEN
    -- Insert or update usage record (for tracking purposes)
    INSERT INTO public.daily_feature_usage (user_id, feature_type, usage_count, usage_date, last_used_at)
    VALUES (user_uuid, feature, 1, CURRENT_DATE, NOW())
    ON CONFLICT (user_id, feature_type, usage_date) 
    DO UPDATE SET 
      usage_count = daily_feature_usage.usage_count + 1,
      last_used_at = NOW();
      
    RETURN TRUE;
  END IF;
  
  -- Get current usage count
  SELECT usage_count INTO current_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid 
    AND feature_type = feature 
    AND usage_date = CURRENT_DATE;
    
  -- If no record exists, count is 0
  IF current_count IS NULL THEN
    current_count := 0;
  END IF;
  
  -- Set max limit based on feature type and subscription tier
  IF feature = 'pill_scan' THEN
    -- Pill scan limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 10; -- Free users: 10 per day
    ELSIF user_tier = 'pro' OR user_tier = 'premium' THEN
      max_limit := 2147483647; -- Pro/Premium: unlimited (using max int)
    ELSE
      max_limit := 10; -- Default
    END IF;
  ELSIF feature = 'live_pill_scan' THEN
    -- Live pill scan limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 0; -- Free users: not available
    ELSIF user_tier = 'pro' THEN
      max_limit := 5; -- Pro: 5 per day
    ELSIF user_tier = 'premium' THEN
      max_limit := 2147483647; -- Premium: unlimited
    ELSE
      max_limit := 0; -- Default
    END IF;
  ELSIF feature = 'note_analysis' THEN
    -- Note analysis limits
    IF user_tier = 'free' OR user_tier IS NULL THEN
      max_limit := 10; -- Free users: 10 per day
    ELSIF user_tier = 'pro' THEN
      max_limit := 0; -- Pro: not available
    ELSIF user_tier = 'premium' THEN
      max_limit := 2147483647; -- Premium: unlimited
    ELSE
      max_limit := 10; -- Default
    END IF;
  ELSE
    -- Default for other features
    max_limit := 10;
  END IF;
  
  -- Check if user can use the feature
  can_use := current_count < max_limit;
  
  -- If user can use the feature, increment the count
  IF can_use THEN
    INSERT INTO public.daily_feature_usage (user_id, feature_type, usage_count, usage_date, last_used_at)
    VALUES (user_uuid, feature, 1, CURRENT_DATE, NOW())
    ON CONFLICT (user_id, feature_type, usage_date) 
    DO UPDATE SET 
      usage_count = daily_feature_usage.usage_count + 1,
      last_used_at = NOW();
  END IF;
  
  RETURN can_use;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the start_live_session function to handle different tiers
CREATE OR REPLACE FUNCTION public.start_live_session(
  user_uuid UUID
) 
RETURNS UUID AS $$
DECLARE
  user_tier TEXT;
  session_count INTEGER;
  max_sessions INTEGER;
  new_session_id UUID;
BEGIN
  -- Get user's subscription tier
  SELECT COALESCE(subscription_tier, 'free') INTO user_tier
  FROM public.profiles 
  WHERE id = user_uuid;
  
  -- Set max sessions based on subscription tier
  IF user_tier = 'free' OR user_tier IS NULL THEN
    max_sessions := 0; -- Free users: not available
  ELSIF user_tier = 'pro' THEN
    max_sessions := 5; -- Pro: 5 per day
  ELSIF user_tier = 'premium' OR user_tier = 'admin' THEN
    max_sessions := 2147483647; -- Premium/Admin: unlimited
  ELSE
    max_sessions := 0; -- Default
  END IF;
  
  -- If tier doesn't allow live sessions, return NULL
  IF max_sessions = 0 THEN
    RETURN NULL;
  END IF;
  
  -- Check daily session count
  SELECT COUNT(*) INTO session_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid 
    AND feature_type = 'live_pill_scan' 
    AND usage_date = CURRENT_DATE;
    
  -- If limit reached, return NULL
  IF session_count >= max_sessions THEN
    RETURN NULL;
  END IF;
  
  -- Create new session
  INSERT INTO public.live_session_tracking (user_id, session_start, is_active)
  VALUES (user_uuid, NOW(), TRUE)
  RETURNING id INTO new_session_id;
  
  -- Track usage
  PERFORM public.track_daily_feature_usage(user_uuid, 'live_pill_scan');
  
  RETURN new_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

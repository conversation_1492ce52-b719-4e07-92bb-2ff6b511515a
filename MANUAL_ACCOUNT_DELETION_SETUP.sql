-- MANUAL ACCOUNT DELETION SETUP
-- Run this SQL script in your Supabase SQL Editor to enable account deletion functionality
-- This is the same content as the migration file, but formatted for manual execution

-- ============================================================================
-- STEP 1: Create the main account deletion function
-- ============================================================================

CREATE OR REPLACE FUNCTION public.delete_user_account(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  user_exists BOOLEAN;
  deletion_successful BOOLEAN := FALSE;
BEGIN
  -- Check if user exists
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = user_uuid) INTO user_exists;
  
  IF NOT user_exists THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Start transaction for data deletion
  BEGIN
    -- Delete user data from all tables (in order to respect foreign key constraints)
    
    -- 1. Delete purchase validation logs (if table exists)
    DELETE FROM public.purchase_validation_log WHERE user_id = user_uuid;
    
    -- 2. Delete live session tracking
    DELETE FROM public.live_session_tracking WHERE user_id = user_uuid;
    
    -- 3. Delete daily feature usage
    DELETE FROM public.daily_feature_usage WHERE user_id = user_uuid;
    
    -- 4. Delete feature usage records
    DELETE FROM public.feature_usage WHERE user_id = user_uuid;
    
    -- 5. Delete usage logs (if exists)
    DELETE FROM public.usage_logs WHERE user_id = user_uuid;
    
    -- 6. Delete subscriptions (if exists)
    DELETE FROM public.subscriptions WHERE user_id = user_uuid;
    
    -- 7. Delete profile (this will cascade to other related data)
    DELETE FROM public.profiles WHERE id = user_uuid;
    
    -- 8. Finally, delete the auth user (this is the main account)
    -- Note: This will also trigger any CASCADE deletes we might have missed
    DELETE FROM auth.users WHERE id = user_uuid;
    
    deletion_successful := TRUE;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't expose details to client
      RAISE LOG 'Error deleting user account %: %', user_uuid, SQLERRM;
      deletion_successful := FALSE;
  END;
  
  RETURN deletion_successful;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 2: Create scheduled deletion table and functions (optional)
-- ============================================================================

-- Create table to track scheduled deletions
CREATE TABLE IF NOT EXISTS public.scheduled_deletions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.scheduled_deletions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own scheduled deletion"
  ON public.scheduled_deletions
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own scheduled deletion"
  ON public.scheduled_deletions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scheduled deletion"
  ON public.scheduled_deletions
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scheduled deletion"
  ON public.scheduled_deletions
  FOR DELETE
  USING (auth.uid() = user_id);

-- ============================================================================
-- STEP 3: Create helper functions
-- ============================================================================

-- Function to schedule account deletion
CREATE OR REPLACE FUNCTION public.schedule_account_deletion(
  user_uuid UUID,
  deletion_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
)
RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO public.scheduled_deletions (user_id, scheduled_for, created_at)
  VALUES (user_uuid, deletion_date, NOW())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    scheduled_for = deletion_date,
    updated_at = NOW();
    
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cancel scheduled deletion
CREATE OR REPLACE FUNCTION public.cancel_scheduled_deletion(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM public.scheduled_deletions WHERE user_id = user_uuid;
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 4: Grant permissions
-- ============================================================================

GRANT EXECUTE ON FUNCTION public.delete_user_account(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.schedule_account_deletion(UUID, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION public.cancel_scheduled_deletion(UUID) TO authenticated;

-- ============================================================================
-- STEP 5: Test the function (optional)
-- ============================================================================

-- You can test with a dummy user ID (replace with actual test user ID):
-- SELECT public.delete_user_account('********-0000-0000-0000-************');

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Check if the function was created successfully:
SELECT 
  routine_name, 
  routine_type,
  security_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name = 'delete_user_account';

-- This should return one row showing the function exists

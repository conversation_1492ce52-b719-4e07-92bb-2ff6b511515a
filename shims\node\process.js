// Minimal implementation of the Node.js process module for React Native
const EventEmitter = require('./events');

class Process extends EventEmitter {
  constructor() {
    super();
    this.env = {};
    this.argv = ['node', 'script.js'];
    this.version = 'v16.0.0';
    this.versions = {
      node: '16.0.0',
      v8: '9.0.0',
      uv: '1.0.0',
      zlib: '1.0.0',
      brotli: '1.0.0',
      ares: '1.0.0',
      modules: '93',
      nghttp2: '1.0.0',
      napi: '8',
      llhttp: '1.0.0',
      openssl: '1.0.0',
      cldr: '1.0.0',
      icu: '1.0.0',
      tz: '1.0.0',
      unicode: '1.0.0'
    };
    this.arch = 'x64';
    this.platform = 'darwin';
    this.release = {
      name: 'node',
      sourceUrl: '',
      headersUrl: '',
      libUrl: ''
    };
    this._events = {};
    this._eventsCount = 0;
    this._maxListeners = undefined;
    this.pid = 1;
    this.ppid = 0;
    this.title = 'node';
    this.browser = true;
    this.connected = false;
    this.exitCode = 0;
    this._exiting = false;
  }

  cwd() {
    return '/';
  }

  chdir(directory) {
    // No-op in this shim
  }

  umask(mask) {
    return 0;
  }

  getuid() {
    return 0;
  }

  geteuid() {
    return 0;
  }

  getgid() {
    return 0;
  }

  getegid() {
    return 0;
  }

  getgroups() {
    return [];
  }

  pid() {
    return 1;
  }

  ppid() {
    return 0;
  }

  uptime() {
    return 0;
  }

  hrtime(time) {
    const now = performance.now();
    const nowInNanoSeconds = Math.floor(now * 1e6);
    if (time) {
      const [seconds, nanoseconds] = time;
      const previousInNanoSeconds = seconds * 1e9 + nanoseconds;
      const diffInNanoSeconds = nowInNanoSeconds - previousInNanoSeconds;
      return [
        Math.floor(diffInNanoSeconds / 1e9),
        diffInNanoSeconds % 1e9
      ];
    }
    return [
      Math.floor(nowInNanoSeconds / 1e9),
      nowInNanoSeconds % 1e9
    ];
  }

  memoryUsage() {
    return {
      rss: 0,
      heapTotal: 0,
      heapUsed: 0,
      external: 0,
      arrayBuffers: 0
    };
  }

  kill(pid, signal) {
    // No-op in this shim
  }

  exit(code) {
    this.exitCode = code || 0;
    this._exiting = true;
    this.emit('exit', this.exitCode);
  }

  abort() {
    this.exit(1);
  }

  nextTick(callback, ...args) {
    setTimeout(() => {
      callback(...args);
    }, 0);
  }
}

const process = new Process();

module.exports = process;

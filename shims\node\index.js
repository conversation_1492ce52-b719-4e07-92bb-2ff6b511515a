// Export all Node.js module shims
module.exports = {
  buffer: require('./buffer'),
  crypto: require('./crypto'),
  dgram: require('./dgram'),
  dns: require('./dns'),
  events: require('./events'),
  fs: require('./fs'),
  http: require('./http'),
  https: require('./https'),
  net: require('./net'),
  os: require('./os'),
  path: require('./path'),
  process: require('./process'),
  querystring: require('./querystring'),
  stream: require('./stream'),
  tls: require('./tls'),
  url: require('./url'),
  util: require('./util'),
  zlib: require('./zlib')
};

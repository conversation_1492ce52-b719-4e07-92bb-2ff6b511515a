import { countPills } from '../services/roboflowService';
import axios from 'axios';

// Set NODE_ENV to test
process.env.NODE_ENV = 'test';

// Mock axios
jest.mock('axios');

describe('roboflowService', () => {
  describe('countPills', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return the correct pill count from Roboflow API', async () => {
      // Mock successful API response
      axios.mockResolvedValueOnce({
        data: {
          predictions: [
            { class: 'pill', confidence: 0.95 },
            { class: 'pill', confidence: 0.92 },
            { class: 'pill', confidence: 0.88 }
          ]
        }
      });

      const result = await countPills('fake-image-uri');

      // Verify the result
      expect(result).toEqual({
        count: 3,
        isEstimate: false,
        predictions: [
          { class: 'pill', confidence: 0.95 },
          { class: 'pill', confidence: 0.92 },
          { class: 'pill', confidence: 0.88 }
        ]
      });

      // Verify axios was called correctly
      expect(axios).toHaveBeenCalledWith({
        method: 'POST',
        url: 'https://detect.roboflow.com/chinese-cv-2-trgoh/1',
        params: {
          api_key: 'RY8gN6VbwSYSDQWZA01b',
          confidence: 40,
          overlap: 30,
          format: 'json'
        },
        data: 'test-base64-string',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
    });

    it('should handle API errors gracefully', async () => {
      // Mock API error
      axios.mockRejectedValueOnce(new Error('API error'));

      const result = await countPills('fake-image-uri');

      // Should return 0 count and mark as estimate
      expect(result).toEqual({
        count: 0,
        isEstimate: true,
        predictions: []
      });
    });

    it('should handle empty predictions array', async () => {
      // Mock API response with no predictions
      axios.mockResolvedValueOnce({
        data: {
          predictions: []
        }
      });

      const result = await countPills('fake-image-uri');

      // Should return 0 count but not mark as estimate
      expect(result).toEqual({
        count: 0,
        isEstimate: false,
        predictions: []
      });
    });
  });
});

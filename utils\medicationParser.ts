import { Medication } from '../components/MedicationTable';

/**
 * Parses the OpenAI response to extract medication information
 * @param response - The text response from OpenAI
 * @returns An array of medication objects
 */
export function parseMedicationInfo(response: string): Medication[] {
  if (!response) return [];

  // Check if the response indicates no medications were found
  if (
    response.includes('no medication') ||
    response.includes('no references to medication') ||
    response.includes('unable to extract any medication')
  ) {
    return [];
  }

  const medications: Medication[] = [];

  // Try to parse the response in different formats

  // First, try to parse the standard format (Medication: X, Dosage: Y, etc.)
  const standardFormatMeds = parseStandardFormat(response);
  if (standardFormatMeds.length > 0) {
    return standardFormatMeds;
  }

  // If standard format fails, try to parse the numbered list format (1. Medication: X, etc.)
  const numberedListMeds = parseNumberedListFormat(response);
  if (numberedListMeds.length > 0) {
    return numberedListMeds;
  }

  // If both formats fail, try to parse the bulleted format with asterisks
  const bulletedMeds = parseBulletedFormat(response);
  if (bulletedMeds.length > 0) {
    return bulletedMeds;
  }

  // If all parsing methods fail, return an empty array
  return [];
}

/**
 * Parses medication information in the standard format
 * @param response - The text response from OpenAI
 * @returns An array of medication objects
 */
function parseStandardFormat(response: string): Medication[] {
  const medications: Medication[] = [];

  // Split the response into lines
  const lines = response.split('\n');

  let currentMedication: Partial<Medication> = {};

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip empty lines
    if (!trimmedLine) continue;

    // Check for medication name
    if (trimmedLine.startsWith('Medication:') || trimmedLine.startsWith('Medication Name:')) {
      // If we already have a medication being processed, add it to the array
      if (currentMedication.name) {
        medications.push(currentMedication as Medication);
        currentMedication = {};
      }

      currentMedication.name = (trimmedLine.split(':')[1]?.trim() || '').replace(/\*\*/g, '');
    }
    // Check for dosage
    else if (trimmedLine.startsWith('Dosage:') || trimmedLine.startsWith('Dose:')) {
      currentMedication.dosage = (trimmedLine.split(':')[1]?.trim() || '').replace(/\*\*/g, '');
    }
    // Check for purpose
    else if (
      trimmedLine.startsWith('Purpose:') ||
      trimmedLine.startsWith('Used for:') ||
      trimmedLine.startsWith('For:') ||
      trimmedLine.startsWith('Indication:')
    ) {
      currentMedication.purpose = (trimmedLine.split(':')[1]?.trim() || '').replace(/\*\*/g, '');
    }
    // Check for fill date
    else if (
      trimmedLine.startsWith('Fill Date:') ||
      trimmedLine.startsWith('Filled:') ||
      trimmedLine.startsWith('Date Filled:') ||
      trimmedLine.startsWith('Filled Date:')
    ) {
      currentMedication.fillDate = (trimmedLine.split(':')[1]?.trim() || '').replace(/\*\*/g, '');
    }
  }

  // Add the last medication if it exists
  if (currentMedication.name) {
    medications.push(currentMedication as Medication);
  }

  return medications;
}

/**
 * Parses medication information in a numbered list format
 * @param response - The text response from OpenAI
 * @returns An array of medication objects
 */
function parseNumberedListFormat(response: string): Medication[] {
  const medications: Medication[] = [];

  // Look for patterns like "1. Medication: X" or "1. **Medication**: X"
  const medicationBlocks = response.split(/\d+\.\s+/);

  // Skip the first element as it's usually empty or contains intro text
  for (let i = 1; i < medicationBlocks.length; i++) {
    const block = medicationBlocks[i];
    if (!block.trim()) continue;

    const medication: Partial<Medication> = {};

    // Extract medication name
    const nameMatch = block.match(/(?:\*\*)?(?:Medication|Medication Name)(?:\*\*)?\s*[:;]\s*([^\n]+)/);
    if (nameMatch) {
      medication.name = nameMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract dosage
    const dosageMatch = block.match(/(?:\*\*)?(?:Dosage|Dose)(?:\*\*)?\s*[:;]\s*([^\n]+)/);
    if (dosageMatch) {
      medication.dosage = dosageMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract purpose
    const purposeMatch = block.match(/(?:\*\*)?(?:Purpose|Used for|For|Indication)(?:\*\*)?\s*[:;]\s*([^\n]+)/);
    if (purposeMatch) {
      medication.purpose = purposeMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract fill date
    const fillDateMatch = block.match(/(?:\*\*)?(?:Fill Date|Filled|Date Filled|Filled Date)(?:\*\*)?\s*[:;]\s*([^\n]+)/);
    if (fillDateMatch) {
      medication.fillDate = fillDateMatch[1].trim().replace(/\*\*/g, '');
    }

    // Only add if we have at least a name
    if (medication.name) {
      medications.push(medication as Medication);
    }
  }

  return medications;
}

/**
 * Parses medication information in a bulleted format
 * @param response - The text response from OpenAI
 * @returns An array of medication objects
 */
function parseBulletedFormat(response: string): Medication[] {
  const medications: Medication[] = [];

  // Split by medication sections (usually separated by blank lines)
  const sections = response.split(/\n\s*\n/);

  for (const section of sections) {
    if (!section.trim()) continue;

    const medication: Partial<Medication> = {};

    // Extract medication name
    const nameMatch = section.match(/\*\s*(?:Medication|Medication Name)\s*:\s*([^\n]+)/);
    if (nameMatch) {
      medication.name = nameMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract dosage
    const dosageMatch = section.match(/\*\s*(?:Dosage|Dose)\s*:\s*([^\n]+)/);
    if (dosageMatch) {
      medication.dosage = dosageMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract purpose
    const purposeMatch = section.match(/\*\s*(?:Purpose|Used for|For|Indication)\s*:\s*([^\n]+)/);
    if (purposeMatch) {
      medication.purpose = purposeMatch[1].trim().replace(/\*\*/g, '');
    }

    // Extract fill date
    const fillDateMatch = section.match(/\*\s*(?:Fill Date|Filled|Date Filled|Filled Date)\s*:\s*([^\n]+)/);
    if (fillDateMatch) {
      medication.fillDate = fillDateMatch[1].trim().replace(/\*\*/g, '');
    }

    // Only add if we have at least a name
    if (medication.name) {
      medications.push(medication as Medication);
    }
  }

  return medications;
}

/**
 * Generates a simulated list of medications for testing
 * @returns An array of medication objects
 */
export function getSimulatedMedications(): Medication[] {
  return [
    {
      name: 'Lisinopril',
      dosage: '10mg, once daily',
      purpose: 'Treatment of hypertension (high blood pressure)',
      fillDate: '2023-06-15',
    },
    {
      name: 'Metformin',
      dosage: '500mg, twice daily',
      purpose: 'Management of type 2 diabetes',
      fillDate: '2023-06-15',
    },
    {
      name: 'Atorvastatin',
      dosage: '20mg, once daily at bedtime',
      purpose: 'Lowering cholesterol levels',
      fillDate: '2023-06-10',
    },
  ];
}

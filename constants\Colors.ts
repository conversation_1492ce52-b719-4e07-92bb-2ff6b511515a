// Professional medical color palette
const primaryColor = '#1976D2';  // Professional blue
const secondaryColor = '#388E3C'; // Success green
const accentColor = '#D32F2F';    // Alert/error red
const neutralDark = '#424242';    // Dark gray for text
const neutralLight = '#F5F5F5';   // Light gray for backgrounds

export default {
  light: {
    text: neutralDark,
    background: '#FFFFFF',
    tint: primaryColor,
    tabIconDefault: '#BDBDBD',
    tabIconSelected: primaryColor,
    card: '#FFFFFF',
    border: '#E0E0E0',
    notification: accentColor,
    success: secondaryColor,
    error: accentColor,
    warning: '#FFA000',
    info: primaryColor,
    surface: '#FFFFFF',
    surfaceVariant: '#F5F7FA',
    divider: '#EEEEEE',
  },
  dark: {
    text: '#FFFFFF',
    background: '#121212',
    tint: '#90CAF9',  // Lighter blue for dark mode
    tabIconDefault: '#757575',
    tabIconSelected: '#90CAF9',
    card: '#1E1E1E',
    border: '#333333',
    notification: '#EF5350',
    success: '#66BB6A',
    error: '#EF5350',
    warning: '#FFB74D',
    info: '#64B5F6',
    surface: '#1E1E1E',
    surfaceVariant: '#2D2D2D',
    divider: '#333333',
  },
};

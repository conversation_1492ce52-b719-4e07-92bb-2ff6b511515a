import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../lib/supabase';
import { Colors, Spacing, BorderRadius } from '../../constants/PillLogicDesign';
import { useLanguage } from '../../contexts/LanguageContext';

export default function VerifyScreen() {
  const router = useRouter();
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  
  const [verifying, setVerifying] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  
  // Get email and token from URL params
  const email = params.email as string;
  const token = params.token as string;
  const autoconfirm = params.autoconfirm as string;
  
  useEffect(() => {
    const verifyEmail = async () => {
      try {
        setVerifying(true);
        
        // If autoconfirm is set to true, we'll skip verification
        // This is only for development purposes
        if (autoconfirm === 'true') {
          console.log('Autoconfirm is enabled, skipping verification');
          setSuccess(true);
          setVerifying(false);
          return;
        }
        
        // Check if we have email and token parameters
        if (!email && !token) {
          console.log('No email or token parameters found in URL');
          console.log('This is likely because the Expo auth proxy stripped the parameters');
          console.log('Attempting to verify using the current session...');
          
          // Try to get the current session
          const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
          
          if (sessionError) {
            console.error('Error getting session:', sessionError);
            setError('Failed to verify your email. Please try signing in to complete verification.');
            setVerifying(false);
            return;
          }
          
          if (sessionData.session) {
            console.log('Session found, user is authenticated');
            setSuccess(true);
            setVerifying(false);
            return;
          }
          
          // If we don't have a session, we'll show a message asking the user to sign in
          console.log('No session found, asking user to sign in');
          setError('Please sign in to complete email verification.');
          setVerifying(false);
          return;
        }
        
        // In a real app, you would validate the token here
        // For now, we'll just simulate a successful verification
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Set success state
        setSuccess(true);
      } catch (error) {
        console.error('Error verifying email:', error);
        setError('Failed to verify your email. Please try again or contact support.');
      } finally {
        setVerifying(false);
      }
    };
    
    verifyEmail();
  }, [email, token, autoconfirm]);
  
  const handleBackToLogin = () => {
    router.replace('/auth/login');
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      <View style={styles.content}>
        {verifying ? (
          // Loading state
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.docPurple.DEFAULT} />
            <Text style={styles.loadingText}>Verifying your email...</Text>
          </View>
        ) : success ? (
          // Success state
          <View style={styles.successContainer}>
            <Ionicons name="checkmark-circle" size={60} color={Colors.success} style={styles.successIcon} />
            <Text style={styles.successTitle}>Email Verified</Text>
            <Text style={styles.successText}>
              Your email has been successfully verified. You can now sign in to your account.
            </Text>
            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={handleBackToLogin}
            >
              <Text style={styles.backToLoginText}>Sign In</Text>
            </TouchableOpacity>
          </View>
        ) : (
          // Error state
          <View style={styles.errorContainer}>
            <Ionicons name="close-circle" size={60} color={Colors.error} style={styles.errorIcon} />
            <Text style={styles.errorTitle}>Verification Failed</Text>
            <Text style={styles.errorText}>
              {error || 'There was an error verifying your email. Please try again or contact support.'}
            </Text>
            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={handleBackToLogin}
            >
              <Text style={styles.backToLoginText}>Back to Login</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  // Loading state styles
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textPrimary,
    marginTop: Spacing.lg,
    textAlign: 'center',
  },
  // Success state styles
  successContainer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  successIcon: {
    marginBottom: Spacing.lg,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  successText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  backToLoginButton: {
    backgroundColor: Colors.docPurple.DEFAULT,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.xl,
    alignItems: 'center',
    marginTop: Spacing.md,
  },
  backToLoginText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  // Error state styles
  errorContainer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
  },
  errorIcon: {
    marginBottom: Spacing.lg,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.md,
  },
  errorText: {
    fontSize: 16,
    color: Colors.textPrimary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
});

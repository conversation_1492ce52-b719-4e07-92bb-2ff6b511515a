import { useState, useCallback, useRef } from 'react';

/**
 * Hook to manage CAPTCHA verification
 * Provides functions to show/hide the CAPTCHA modal and handle verification
 */
export const useCaptcha = () => {
  const [isCaptchaModalVisible, setIsCaptchaModalVisible] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [captchaVerificationCallback, setCaptchaVerificationCallback] = useState<
    ((token: string) => void) | null
  >(null);

  // Show the CAPTCHA modal and return a promise that resolves with the token
  const showCaptchaModal = useCallback((): Promise<string> => {
    console.log('showCaptchaModal called - preparing to show CAPTCHA modal');

    return new Promise((resolve, reject) => {
      // Clear any existing timeout
      if (timeoutRef.current) {
        console.log('Clearing existing timeout');
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Set the callback function to resolve the promise when verification is complete
      setCaptchaVerificationCallback(() => (token: string) => {
        console.log('Verification callback executed with token:', token ? `${token.substring(0, 10)}...` : 'null');

        if (!token || token.trim() === '') {
          console.error('Empty token received in verification callback');
          reject(new Error('Empty CAPTCHA token'));
          return;
        }

        console.log('Valid token received in verification callback, length:', token.length);

        // Clear the timeout since we got a valid response
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Store the token for future reference
        setCaptchaToken(token);

        // Resolve the promise with the token
        console.log('Resolving promise with valid token');
        resolve(token);

        // Hide the modal
        console.log('Hiding CAPTCHA modal after successful verification');
        setIsCaptchaModalVisible(false);
      });

      // Show the modal
      console.log('Setting CAPTCHA modal to visible');
      setIsCaptchaModalVisible(true);

      // Add a timeout to reject the promise after 3 minutes (increased from 2 minutes)
      console.log('Setting timeout for CAPTCHA verification (3 minutes)');
      timeoutRef.current = setTimeout(() => {
        console.log('CAPTCHA verification timed out after 3 minutes');
        timeoutRef.current = null;
        reject(new Error('CAPTCHA verification timed out'));
        setIsCaptchaModalVisible(false);
        setCaptchaVerificationCallback(null);
      }, 3 * 60 * 1000);
    });
  }, []);

  // Handle CAPTCHA verification
  const handleCaptchaVerify = useCallback((token: string) => {
    console.log('handleCaptchaVerify called with token:', token ? `${token.substring(0, 10)}...` : 'null');
    console.log('Token length:', token?.length || 0);

    // Validate the token
    if (!token || token.trim() === '') {
      console.error('Empty token received in handleCaptchaVerify');
      return;
    }

    // Store the token
    setCaptchaToken(token);

    // Call the verification callback if it exists
    if (captchaVerificationCallback) {
      console.log('Verification callback exists, calling it with token');
      try {
        captchaVerificationCallback(token);
        console.log('Verification callback executed successfully');
      } catch (error) {
        console.error('Error in verification callback:', error);
      }
      setCaptchaVerificationCallback(null);
    } else {
      console.log('No verification callback available, token will be stored but not used');
    }
  }, [captchaVerificationCallback]);

  // Reference to store timeout ID
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close the CAPTCHA modal
  const closeCaptchaModal = useCallback(() => {
    console.log('closeCaptchaModal called - user manually closed the CAPTCHA modal');

    // Clear any pending timeout
    if (timeoutRef.current) {
      console.log('Clearing timeout in closeCaptchaModal');
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Hide the modal and clear the callback
    console.log('Setting modal to invisible and clearing callback');
    setIsCaptchaModalVisible(false);
    setCaptchaVerificationCallback(null);
  }, []);

  return {
    isCaptchaModalVisible,
    captchaToken,
    showCaptchaModal,
    handleCaptchaVerify,
    closeCaptchaModal
  };
};

import os
import sys
import base64
import replicate
from flask import Flask, request, jsonify
from flask_cors import CORS
import tempfile

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Replace with your actual Replicate API token
# In a production app, this should be stored securely as an environment variable
os.environ["REPLICATE_API_TOKEN"] = ""  # Set your token here

@app.route('/analyze', methods=['POST'])
def analyze_image():
    try:
        # Get the base64 image and prompt from the request
        data = request.json
        if not data or 'image' not in data:
            return jsonify({'error': 'No image provided'}), 400

        base64_image = data['image']
        prompt = data.get('prompt', 'Describe this image in detail')

        # If the image is a data URL, extract the base64 part
        if base64_image.startswith('data:image'):
            base64_image = base64_image.split(',')[1]

        # Save the base64 image to a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            temp_file.write(base64.b64decode(base64_image))
            temp_file_path = temp_file.name

        try:
            # Run the DeepSeek-VL2 model using Replicate
            output = replicate.run(
                "deepseek-ai/deepseek-vl2:e5caf557dd9e5dcee46442e1315291ef1867f027991ede8ff95e304d4f734200",
                input={
                    "image": open(temp_file_path, "rb"),
                    "prompt": prompt
                }
            )

            # Return the analysis result
            return jsonify({'result': output})
        finally:
            # Clean up the temporary file
            os.unlink(temp_file_path)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

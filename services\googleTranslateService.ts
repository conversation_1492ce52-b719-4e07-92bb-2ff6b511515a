// Import language types from separate file to avoid circular dependencies
import { Language, LanguageNames } from './languageTypes';
import { tierTranslations } from './translations';

// Re-export Language enum and LanguageNames for backward compatibility
export { Language, LanguageNames };

// Simple in-memory cache for translations
interface TranslationCache {
  [key: string]: {
    [language: string]: string;
  };
}

const translationCache: TranslationCache = {};

/**
 * Translates text to the specified language using Google Translate API
 * @param text - The text to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated text
 */
export const translateText = async (text: string, targetLanguage: Language): Promise<string> => {
  // Handle undefined or null text
  if (!text) {
    console.log('Warning: Attempted to translate undefined or empty text');
    return '';
  }

  // If the target language is English, return the original text
  if (targetLanguage === Language.ENGLISH) {
    return text;
  }

  // Create a cache key based on the text and target language
  const cacheKey = text.substring(0, 100); // Use first 100 chars as key to avoid huge keys

  // Check if we have a cached translation
  if (translationCache[cacheKey] && translationCache[cacheKey][targetLanguage]) {
    console.log('Using cached translation');
    return translationCache[cacheKey][targetLanguage];
  }

  try {
    console.log(`Translating text to ${getLanguageName(targetLanguage)}`);

    // Use free Google Translate API
    const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLanguage}&dt=t&q=${encodeURIComponent(text)}`;

    const response = await fetch(url);
    const data = await response.json();

    // Extract the translated text from the response
    let translatedText = '';
    if (data && data[0]) {
      for (let i = 0; i < data[0].length; i++) {
        translatedText += data[0][i][0];
      }
    } else {
      translatedText = text; // Fallback to original text
    }

    // Cache the translation
    if (!translationCache[cacheKey]) {
      translationCache[cacheKey] = {};
    }
    translationCache[cacheKey][targetLanguage] = translatedText;

    console.log('Translation result:', translatedText);
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    return text; // Return original text if translation fails
  }
};

/**
 * Translates medication information to the specified language
 * @param medications - The medication objects to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated medication objects
 */
export const translateMedications = async (medications: any[], targetLanguage: Language): Promise<any[]> => {
  // If the target language is English, return the original medications
  if (targetLanguage === Language.ENGLISH || medications.length === 0) {
    return medications;
  }

  console.log('Translating medications to', targetLanguage);
  const translatedMedications = [];

  // Translate each medication individually
  for (const med of medications) {
    try {
      // Create a simple prompt for each medication - handle undefined fields
      const namePromise = translateText(med.name || '', targetLanguage);
      const dosagePromise = translateText(med.dosage || '', targetLanguage);
      const purposePromise = translateText(med.purpose || '', targetLanguage);
      const fillDatePromise = translateText(med.fillDate || '', targetLanguage);

      // Wait for all translations to complete
      const [name, dosage, purpose, fillDate] = await Promise.all([namePromise, dosagePromise, purposePromise, fillDatePromise]);

      // Log the translations for debugging - safely handle undefined fields
      console.log(`[${targetLanguage}] Translated medication: ${med.name || ''} -> ${name}`);
      console.log(`[${targetLanguage}] Translated purpose: ${med.purpose || ''} -> ${purpose}`);
      console.log(`[${targetLanguage}] Translated date: ${med.fillDate || ''} -> ${fillDate}`);

      translatedMedications.push({
        name,
        dosage,
        purpose,
        fillDate,
      });
    } catch (error) {
      console.error('Error translating medication:', error);
      translatedMedications.push(med); // Use original if translation fails
    }
  }

  console.log('Translated medications:', translatedMedications);
  return translatedMedications;
};

/**
 * Gets the full name of a language
 * @param language - The language code
 * @returns The language name
 */
export const getLanguageName = (language: Language): string => {
  return LanguageNames[language] || 'Unknown';
};

/**
 * Translates UI text based on the language
 * @param key - The text key
 * @param language - The target language
 * @returns The translated text
 */
export const translateUI = (key: string, language: Language, params?: Record<string, any>): string => {
  // Combine our standard translations with the tier-specific translations
  const translations: Record<string, Record<Language, string>> = {
    ...tierTranslations,
    'medicationExtractor': {
      [Language.ENGLISH]: 'Medication Extractor',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षक',
      [Language.CHINESE]: '药物提取器'
    },
    'poweredBy': {
      [Language.ENGLISH]: 'Powered by OpenAI Vision',
      [Language.VIETNAMESE]: 'Được hỗ trợ bởi OpenAI Vision',
      [Language.HINDI]: 'OpenAI Vision द्वारा संचालित',
      [Language.CHINESE]: '由 OpenAI Vision 提供支持'
    },
    'extractedMedications': {
      [Language.ENGLISH]: 'Extracted Medications',
      [Language.VIETNAMESE]: 'Thuốc Đã Trích Xuất',
      [Language.HINDI]: 'निकाली गई दवाएं',
      [Language.CHINESE]: '提取的药物'
    },
    'showRawResponse': {
      [Language.ENGLISH]: 'Show Raw Response',
      [Language.VIETNAMESE]: 'Hiển Thị Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया दिखाएं',
      [Language.CHINESE]: '显示原始响应'
    },
    'hideRawResponse': {
      [Language.ENGLISH]: 'Hide Raw Response',
      [Language.VIETNAMESE]: 'Ẩn Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया छिपाएं',
      [Language.CHINESE]: '隐藏原始响应'
    },
    'rawApiResponse': {
      [Language.ENGLISH]: 'Raw API Response:',
      [Language.VIETNAMESE]: 'Phản Hồi API Gốc:',
      [Language.HINDI]: 'कच्ची API प्रतिक्रिया:',
      [Language.CHINESE]: '原始 API 响应:'
    },
    'noMedicationsFound': {
      [Language.ENGLISH]: 'No medications found in the image.',
      [Language.VIETNAMESE]: 'Không tìm thấy thuốc trong hình ảnh.',
      [Language.HINDI]: 'छवि में कोई दवा नहीं मिली।',
      [Language.CHINESE]: '图像中未找到药物。'
    },
    'note': {
      [Language.ENGLISH]: 'Note: This app extracts medication information from doctor notes. If you encounter any errors, please wait a few moments before trying again.',
      [Language.VIETNAMESE]: 'Lưu ý: Ứng dụng này trích xuất thông tin thuốc từ ghi chú của bác sĩ. Nếu bạn gặp lỗi, vui lòng đợi một lát trước khi thử lại.',
      [Language.HINDI]: 'नोट: यह ऐप डॉक्टर के नोट्स से दवा की जानकारी निकालती है। यदि आपको कोई त्रुटियां मिलती हैं, तो कृपया फिर से प्रयास करने से पहले कुछ क्षण प्रतीक्षा करें।',
      [Language.CHINESE]: '注意：此应用程序从医生笔记中提取药物信息。如果遇到任何错误，请稍等片刻再重试。'
    },
    'takeAnotherPhoto': {
      [Language.ENGLISH]: 'Take Another Photo',
      [Language.VIETNAMESE]: 'Chụp Ảnh Khác',
      [Language.HINDI]: 'एक और फोटो लें',
      [Language.CHINESE]: '拍摄另一张照片'
    },
    'analyzing': {
      [Language.ENGLISH]: 'Analyzing image...',
      [Language.VIETNAMESE]: 'Đang phân tích hình ảnh...',
      [Language.HINDI]: 'छवि का विश्लेषण कर रहा है...',
      [Language.CHINESE]: '正在分析图像...'
    },
    'translating': {
      [Language.ENGLISH]: 'Translating content...',
      [Language.VIETNAMESE]: 'Đang dịch nội dung...',
      [Language.HINDI]: 'सामग्री का अनुवाद कर रहा है...',
      [Language.CHINESE]: '正在翻译内容...'
    },
    'medication': {
      [Language.ENGLISH]: 'Medication',
      [Language.VIETNAMESE]: 'Thuốc',
      [Language.HINDI]: 'दवा',
      [Language.CHINESE]: '药物'
    },
    'dosage': {
      [Language.ENGLISH]: 'Dosage',
      [Language.VIETNAMESE]: 'Liều Lượng',
      [Language.HINDI]: 'खुराक',
      [Language.CHINESE]: '剂量'
    },
    'purpose': {
      [Language.ENGLISH]: 'Purpose',
      [Language.VIETNAMESE]: 'Mục Đích',
      [Language.HINDI]: 'उद्देश्य',
      [Language.CHINESE]: '用途'
    },
    'fillDate': {
      [Language.ENGLISH]: 'Fill Date',
      [Language.VIETNAMESE]: 'Ngày Cấp',
      [Language.HINDI]: 'भरने की तारीख',
      [Language.CHINESE]: '配药日期'
    },
    'noMedicationInfo': {
      [Language.ENGLISH]: 'No medication information found.',
      [Language.VIETNAMESE]: 'Không tìm thấy thông tin thuốc.',
      [Language.HINDI]: 'कोई दवा जानकारी नहीं मिली।',
      [Language.CHINESE]: '未找到药物信息。'
    },
    'selectLanguage': {
      [Language.ENGLISH]: 'Select Language',
      [Language.VIETNAMESE]: 'Chọn Ngôn Ngữ',
      [Language.HINDI]: 'भाषा चुनें',
      [Language.CHINESE]: '选择语言'
    },
    'backToHome': {
      [Language.ENGLISH]: 'Back to Home',
      [Language.VIETNAMESE]: 'Trở về Trang Chủ',
      [Language.HINDI]: 'होम पर वापस जाएं',
      [Language.CHINESE]: '返回首页'
    },
    'selectFeature': {
      [Language.ENGLISH]: 'Select a Feature',
      [Language.VIETNAMESE]: 'Chọn Tính Năng',
      [Language.HINDI]: 'एक सुविधा चुनें',
      [Language.CHINESE]: '选择功能'
    },
    'medicationNotesScanner': {
      [Language.ENGLISH]: 'Medication Notes Scanner',
      [Language.VIETNAMESE]: 'Quét Ghi Chú Thuốc',
      [Language.HINDI]: 'दवा नोट्स स्कैनर',
      [Language.CHINESE]: '药物笔记扫描仪'
    },
    'medicationNotesScannerDescription': {
      [Language.ENGLISH]: 'Take a photo of medication notes to analyze and organize',
      [Language.VIETNAMESE]: 'Chụp ảnh ghi chú thuốc để phân tích và tổ chức',
      [Language.HINDI]: 'दवा नोट्स का फोटो लेकर विश्लेषण और व्यवस्थित करें',
      [Language.CHINESE]: '拍摄药物笔记照片进行分析和整理'
    },
    'pillCounterDescription': {
      [Language.ENGLISH]: 'Take a photo of pills to count their total number',
      [Language.VIETNAMESE]: 'Chụp ảnh các viên thuốc để đếm tổng số',
      [Language.HINDI]: 'गोलियों की कुल संख्या गिनने के लिए उनकी फोटो लें',
      [Language.CHINESE]: '拍摄药丸照片以计算它们的总数'
    },
    'livePillCount': {
      [Language.ENGLISH]: 'Live Pill Count',
      [Language.VIETNAMESE]: 'Đếm Thuốc Trực Tiếp',
      [Language.HINDI]: 'लाइव गोली गिनती',
      [Language.CHINESE]: '实时药丸计数'
    },
    'livePillCountDescription': {
      [Language.ENGLISH]: 'Count pills in real-time using your camera',
      [Language.VIETNAMESE]: 'Đếm thuốc theo thời gian thực bằng camera',
      [Language.HINDI]: 'अपने कैमरे का उपयोग करके वास्तविक समय में गोलियां गिनें',
      [Language.CHINESE]: '使用相机实时计数药丸'
    },
    'livePillCounter': {
      [Language.ENGLISH]: 'Live Pill Counter',
      [Language.VIETNAMESE]: 'Bộ Đếm Thuốc Trực Tiếp',
      [Language.HINDI]: 'लाइव गोली काउंटर',
      [Language.CHINESE]: '实时药丸计数器'
    },
    'liveMode': {
      [Language.ENGLISH]: 'Live Mode',
      [Language.VIETNAMESE]: 'Chế Độ Trực Tiếp',
      [Language.HINDI]: 'लाइव मोड',
      [Language.CHINESE]: '实时模式'
    },
    'singleCaptureMode': {
      [Language.ENGLISH]: 'Single Capture Mode',
      [Language.VIETNAMESE]: 'Chế Độ Chụp Đơn',
      [Language.HINDI]: 'एकल कैप्चर मोड',
      [Language.CHINESE]: '单次捕获模式'
    },
    'updatesEvery5Seconds': {
      [Language.ENGLISH]: 'Updates every 5 seconds',
      [Language.VIETNAMESE]: 'Cập nhật mỗi 5 giây',
      [Language.HINDI]: 'हर 5 सेकंड में अपडेट होता है',
      [Language.CHINESE]: '每5秒更新一次'
    },
    // Authentication translations
    'profile': {
      [Language.ENGLISH]: 'Profile',
      [Language.VIETNAMESE]: 'Hồ sơ',
      [Language.HINDI]: 'प्रोफ़ाइल',
      [Language.CHINESE]: '个人资料'
    },
    'login': {
      [Language.ENGLISH]: 'Login',
      [Language.VIETNAMESE]: 'Đăng nhập',
      [Language.HINDI]: 'लॉग इन',
      [Language.CHINESE]: '登录'
    },
    'signOut': {
      [Language.ENGLISH]: 'Sign Out',
      [Language.VIETNAMESE]: 'Đăng xuất',
      [Language.HINDI]: 'साइन आउट',
      [Language.CHINESE]: '退出登录'
    },
    'welcomeToDocAid': {
      [Language.ENGLISH]: 'Welcome to PillLogic',
      [Language.VIETNAMESE]: 'Chào mừng đến với PillLogic',
      [Language.HINDI]: 'PillLogic में आपका स्वागत है',
      [Language.CHINESE]: '欢迎使用PillLogic'
    },
    'loginToAccessAllFeatures': {
      [Language.ENGLISH]: 'Login to access all features',
      [Language.VIETNAMESE]: 'Đăng nhập để truy cập tất cả các tính năng',
      [Language.HINDI]: 'सभी सुविधाओं का उपयोग करने के लिए लॉगिन करें',
      [Language.CHINESE]: '登录以访问所有功能'
    },
    'continueWithGoogle': {
      [Language.ENGLISH]: 'Continue with Google',
      [Language.VIETNAMESE]: 'Tiếp tục với Google',
      [Language.HINDI]: 'Google के साथ जारी रखें',
      [Language.CHINESE]: '使用Google继续'
    },
    'signInWithGoogleToAccess': {
      [Language.ENGLISH]: 'Sign in with Google to access all features',
      [Language.VIETNAMESE]: 'Đăng nhập bằng Google để truy cập tất cả tính năng',
      [Language.HINDI]: 'सभी सुविधाओं का उपयोग करने के लिए Google से साइन इन करें',
      [Language.CHINESE]: '使用Google登录以访问所有功能'
    },
    'passwordResetInDevelopment': {
      [Language.ENGLISH]: 'Note: Password reset is still in development. We recommend also signing up with Google to secure your account.',
      [Language.VIETNAMESE]: 'Lưu ý: Tính năng đặt lại mật khẩu vẫn đang được phát triển. Chúng tôi khuyên bạn nên đăng ký bằng Google để bảo mật tài khoản.',
      [Language.HINDI]: 'नोट: पासवर्ड रीसेट अभी भी विकसित हो रहा है। हम आपके खाते को सुरक्षित करने के लिए Google के साथ साइन अप करने की सलाह देते हैं।',
      [Language.CHINESE]: '注意：密码重置功能仍在开发中。我们建议您也使用Google注册以保护您的账户。'
    },
    'recommendGoogleForSecurity': {
      [Language.ENGLISH]: 'We recommend Google Sign-in for better security and account recovery.',
      [Language.VIETNAMESE]: 'Chúng tôi khuyên bạn nên đăng nhập bằng Google để có bảo mật tốt hơn và khôi phục tài khoản.',
      [Language.HINDI]: 'हम बेहतर सुरक्षा और खाता पुनर्प्राप्ति के लिए Google साइन-इन की सिफारिश करते हैं।',
      [Language.CHINESE]: '我们建议使用Google登录以获得更好的安全性和账户恢复功能。'
    },
    'byLoggingInYouAgree': {
      [Language.ENGLISH]: 'By logging in, you agree to our',
      [Language.VIETNAMESE]: 'Bằng cách đăng nhập, bạn đồng ý với',
      [Language.HINDI]: 'लॉग इन करके, आप हमारे',
      [Language.CHINESE]: '登录即表示您同意我们的'
    },
    'termsOfService': {
      [Language.ENGLISH]: 'Terms of Service',
      [Language.VIETNAMESE]: 'Điều khoản dịch vụ',
      [Language.HINDI]: 'सेवा की शर्तें',
      [Language.CHINESE]: '服务条款'
    },
    'and': {
      [Language.ENGLISH]: 'and',
      [Language.VIETNAMESE]: 'và',
      [Language.HINDI]: 'और',
      [Language.CHINESE]: '和'
    },
    'privacyPolicy': {
      [Language.ENGLISH]: 'Privacy Policy',
      [Language.VIETNAMESE]: 'Chính sách bảo mật',
      [Language.HINDI]: 'गोपनीयता नीति',
      [Language.CHINESE]: '隐私政策'
    },
    'skip': {
      [Language.ENGLISH]: 'Skip',
      [Language.VIETNAMESE]: 'Bỏ qua',
      [Language.HINDI]: 'छोड़ें',
      [Language.CHINESE]: '跳过'
    },
    'notLoggedIn': {
      [Language.ENGLISH]: 'Not Logged In',
      [Language.VIETNAMESE]: 'Chưa đăng nhập',
      [Language.HINDI]: 'लॉग इन नहीं है',
      [Language.CHINESE]: '未登录'
    },
    'loginToAccessYourProfile': {
      [Language.ENGLISH]: 'Login to access your profile and usage statistics',
      [Language.VIETNAMESE]: 'Đăng nhập để truy cập hồ sơ và thống kê sử dụng của bạn',
      [Language.HINDI]: 'अपनी प्रोफ़ाइल और उपयोग आंकड़े देखने के लिए लॉगिन करें',
      [Language.CHINESE]: '登录以访问您的个人资料和使用统计信息'
    },
    'memberSince': {
      [Language.ENGLISH]: 'Member since',
      [Language.VIETNAMESE]: 'Thành viên từ',
      [Language.HINDI]: 'सदस्य बने',
      [Language.CHINESE]: '会员自'
    },
    'usageCount': {
      [Language.ENGLISH]: 'Usage Count',
      [Language.VIETNAMESE]: 'Số lần sử dụng',
      [Language.HINDI]: 'उपयोग संख्या',
      [Language.CHINESE]: '使用次数'
    },
    'subscriptionTier': {
      [Language.ENGLISH]: 'Subscription',
      [Language.VIETNAMESE]: 'Gói đăng ký',
      [Language.HINDI]: 'सदस्यता',
      [Language.CHINESE]: '订阅等级'
    },
    'accountSettings': {
      [Language.ENGLISH]: 'Account Settings',
      [Language.VIETNAMESE]: 'Cài đặt tài khoản',
      [Language.HINDI]: 'खाता सेटिंग',
      [Language.CHINESE]: '账户设置'
    },
    'subscription': {
      [Language.ENGLISH]: 'Subscription',
      [Language.VIETNAMESE]: 'Đăng ký',
      [Language.HINDI]: 'सदस्यता',
      [Language.CHINESE]: '订阅'
    },
    'help': {
      [Language.ENGLISH]: 'Help',
      [Language.VIETNAMESE]: 'Trợ giúp',
      [Language.HINDI]: 'सहायता',
      [Language.CHINESE]: '帮助'
    },
    'areYouSureYouWantToSignOut': {
      [Language.ENGLISH]: 'Are you sure you want to sign out?',
      [Language.VIETNAMESE]: 'Bạn có chắc chắn muốn đăng xuất không?',
      [Language.HINDI]: 'क्या आप वाकई साइन आउट करना चाहते हैं?',
      [Language.CHINESE]: '您确定要退出登录吗？'
    },
    'cancel': {
      [Language.ENGLISH]: 'Cancel',
      [Language.VIETNAMESE]: 'Hủy',
      [Language.HINDI]: 'रद्द करें',
      [Language.CHINESE]: '取消'
    },
    'loading': {
      [Language.ENGLISH]: 'Loading...',
      [Language.VIETNAMESE]: 'Đang tải...',
      [Language.HINDI]: 'लोड हो रहा है...',
      [Language.CHINESE]: '加载中...'
    },
    'completingLogin': {
      [Language.ENGLISH]: 'Completing login...',
      [Language.VIETNAMESE]: 'Đang hoàn tất đăng nhập...',
      [Language.HINDI]: 'लॉगिन पूरा हो रहा है...',
      [Language.CHINESE]: '正在完成登录...'
    },
    'medicationAssistant': {
      [Language.ENGLISH]: 'Medication Assistant',
      [Language.VIETNAMESE]: 'Trợ Lý Thuốc',
      [Language.HINDI]: 'दवा सहायक',
      [Language.CHINESE]: '药物助手'
    },
    'startScanning': {
      [Language.ENGLISH]: 'Start Scanning',
      [Language.VIETNAMESE]: 'Bắt Đầu Quét',
      [Language.HINDI]: 'स्कैनिंग शुरू करें',
      [Language.CHINESE]: '开始扫描'
    },
    'viewSavedMedications': {
      [Language.ENGLISH]: 'View your saved medication records',
      [Language.VIETNAMESE]: 'Xem các bản ghi thuốc đã lưu',
      [Language.HINDI]: 'अपने सहेजे गए दवा रिकॉर्ड देखें',
      [Language.CHINESE]: '查看您保存的药物记录'
    },
    'all': {
      [Language.ENGLISH]: 'All',
      [Language.VIETNAMESE]: 'Tất Cả',
      [Language.HINDI]: 'सभी',
      [Language.CHINESE]: '全部'
    },
    'pillCounts': {
      [Language.ENGLISH]: 'Pill Counts',
      [Language.VIETNAMESE]: 'Đếm Viên Thuốc',
      [Language.HINDI]: 'गोली गिनती',
      [Language.CHINESE]: '药丸计数'
    },
    'features': {
      [Language.ENGLISH]: 'Features',
      [Language.VIETNAMESE]: 'Tính Năng',
      [Language.HINDI]: 'विशेषताएँ',
      [Language.CHINESE]: '功能'
    },
    'searchAnalyses': {
      [Language.ENGLISH]: 'Search analyses',
      [Language.VIETNAMESE]: 'Tìm kiếm phân tích',
      [Language.HINDI]: 'विश्लेषण खोजें',
      [Language.CHINESE]: '搜索分析'
    },
    'noSearchResults': {
      [Language.ENGLISH]: 'No search results',
      [Language.VIETNAMESE]: 'Không có kết quả tìm kiếm',
      [Language.HINDI]: 'कोई खोज परिणाम नहीं',
      [Language.CHINESE]: '没有搜索结果'
    },
    // Usage limits translations
    'usageLimitTitle': {
      [Language.ENGLISH]: 'Usage Limit',
      [Language.VIETNAMESE]: 'Giới Hạn Sử Dụng',
      [Language.HINDI]: 'उपयोग सीमा',
      [Language.CHINESE]: '使用限制'
    },
    'usageLimitReached': {
      [Language.ENGLISH]: 'You have reached your daily limit for {{feature}} ({{limit}} per day). Please try again tomorrow or upgrade to a paid plan for unlimited usage.',
      [Language.VIETNAMESE]: 'Bạn đã đạt đến giới hạn hàng ngày cho {{feature}} ({{limit}} mỗi ngày). Vui lòng thử lại vào ngày mai hoặc nâng cấp lên gói trả phí để sử dụng không giới hạn.',
      [Language.HINDI]: 'आपने {{feature}} के लिए अपनी दैनिक सीमा (प्रति दिन {{limit}}) तक पहुंच गए हैं। कृपया कल फिर से प्रयास करें या असीमित उपयोग के लिए भुगतान योजना में अपग्रेड करें।',
      [Language.CHINESE]: '您已达到{{feature}}的每日限制（每天{{limit}}次）。请明天再试或升级到付费计划以获得无限使用权。'
    },
    'liveScanLimitReached': {
      [Language.ENGLISH]: 'You have reached your daily limit for live pill scans ({{limit}} per day). Please try again tomorrow or upgrade to a paid plan for unlimited usage.',
      [Language.VIETNAMESE]: 'Bạn đã đạt đến giới hạn hàng ngày cho quét thuốc trực tiếp ({{limit}} mỗi ngày). Vui lòng thử lại vào ngày mai hoặc nâng cấp lên gói trả phí để sử dụng không giới hạn.',
      [Language.HINDI]: 'आपने लाइव पिल स्कैन के लिए अपनी दैनिक सीमा (प्रति दिन {{limit}}) तक पहुंच गए हैं। कृपया कल फिर से प्रयास करें या असीमित उपयोग के लिए भुगतान योजना में अपग्रेड करें।',
      [Language.CHINESE]: '您已达到实时药丸扫描的每日限制（每天{{limit}}次）。请明天再试或升级到付费计划以获得无限使用权。'
    },
    'liveScanTimeLimit': {
      [Language.ENGLISH]: 'Your live scan session has ended. Free users are limited to 2 minutes per session. Please upgrade to a paid plan for unlimited session duration.',
      [Language.VIETNAMESE]: 'Phiên quét trực tiếp của bạn đã kết thúc. Người dùng miễn phí bị giới hạn 2 phút mỗi phiên. Vui lòng nâng cấp lên gói trả phí để có thời lượng phiên không giới hạn.',
      [Language.HINDI]: 'आपका लाइव स्कैन सत्र समाप्त हो गया है। मुफ्त उपयोगकर्ताओं को प्रति सत्र 2 मिनट तक सीमित किया गया है। असीमित सत्र अवधि के लिए कृपया भुगतान योजना में अपग्रेड करें।',
      [Language.CHINESE]: '您的实时扫描会话已结束。免费用户每次会话限制为2分钟。请升级到付费计划以获得无限会话时长。'
    },
    'sessionEndedTitle': {
      [Language.ENGLISH]: 'Session Ended',
      [Language.VIETNAMESE]: 'Phiên Đã Kết Thúc',
      [Language.HINDI]: 'सत्र समाप्त',
      [Language.CHINESE]: '会话已结束'
    },
    'sessionTimeWarningTitle': {
      [Language.ENGLISH]: 'Session Time Warning',
      [Language.VIETNAMESE]: 'Cảnh Báo Thời Gian Phiên',
      [Language.HINDI]: 'सत्र समय चेतावनी',
      [Language.CHINESE]: '会话时间警告'
    },
    'sessionTimeWarning': {
      [Language.ENGLISH]: 'Your session will end in {{seconds}} seconds. Upgrade to a paid plan for unlimited session duration.',
      [Language.VIETNAMESE]: 'Phiên của bạn sẽ kết thúc trong {{seconds}} giây. Nâng cấp lên gói trả phí để có thời lượng phiên không giới hạn.',
      [Language.HINDI]: 'आपका सत्र {{seconds}} सेकंड में समाप्त हो जाएगा। असीमित सत्र अवधि के लिए भुगतान योजना में अपग्रेड करें।',
      [Language.CHINESE]: '您的会话将在{{seconds}}秒内结束。升级到付费计划以获得无限会话时长。'
    },
    'timeRemaining': {
      [Language.ENGLISH]: '{{minutes}}:{{seconds}} remaining',
      [Language.VIETNAMESE]: 'Còn {{minutes}}:{{seconds}}',
      [Language.HINDI]: '{{minutes}}:{{seconds}} शेष',
      [Language.CHINESE]: '剩余{{minutes}}:{{seconds}}'
    },
    'pillScan': {
      [Language.ENGLISH]: 'Pill Count',
      [Language.VIETNAMESE]: 'Đếm Thuốc',
      [Language.HINDI]: 'गोली गिनती',
      [Language.CHINESE]: '药丸计数'
    },
    'livePillScan': {
      [Language.ENGLISH]: 'Live Pill Count',
      [Language.VIETNAMESE]: 'Đếm Thuốc Trực Tiếp',
      [Language.HINDI]: 'लाइव गोली गिनती',
      [Language.CHINESE]: '实时药丸计数'
    },
    'pillScanUsageInfo': {
      [Language.ENGLISH]: '{{remaining}}/{{total}} scans remaining today',
      [Language.VIETNAMESE]: 'Còn {{remaining}}/{{total}} lần quét hôm nay',
      [Language.HINDI]: 'आज {{remaining}}/{{total}} स्कैन शेष हैं',
      [Language.CHINESE]: '今天剩余{{remaining}}/{{total}}次扫描'
    },
    'liveScanUsageInfo': {
      [Language.ENGLISH]: 'Free users: {{limit}} sessions/day, {{timeLimit}} min/session',
      [Language.VIETNAMESE]: 'Người dùng miễn phí: {{limit}} phiên/ngày, {{timeLimit}} phút/phiên',
      [Language.HINDI]: 'मुफ्त उपयोगकर्ता: {{limit}} सत्र/दिन, {{timeLimit}} मिनट/सत्र',
      [Language.CHINESE]: '免费用户：{{limit}}次/天，{{timeLimit}}分钟/次'
    },
    'signInToUseFeature': {
      [Language.ENGLISH]: 'Please sign in to use this feature',
      [Language.VIETNAMESE]: 'Vui lòng đăng nhập để sử dụng tính năng này',
      [Language.HINDI]: 'इस सुविधा का उपयोग करने के लिए कृपया साइन इन करें',
      [Language.CHINESE]: '请登录以使用此功能'
    },
    'anonymousAuthFailed': {
      [Language.ENGLISH]: 'Failed to create anonymous account. Please try again or sign in.',
      [Language.VIETNAMESE]: 'Không thể tạo tài khoản ẩn danh. Vui lòng thử lại hoặc đăng nhập.',
      [Language.HINDI]: 'अनाम खाता बनाने में विफल। कृपया पुनः प्रयास करें या साइन इन करें।',
      [Language.CHINESE]: '无法创建匿名账户。请重试或登录。'
    },
    'anonymousUser': {
      [Language.ENGLISH]: 'Anonymous User',
      [Language.VIETNAMESE]: 'Người Dùng Ẩn Danh',
      [Language.HINDI]: 'अनाम उपयोगकर्ता',
      [Language.CHINESE]: '匿名用户'
    },

    'showDetections': {
      [Language.ENGLISH]: 'Show Detections',
      [Language.VIETNAMESE]: 'Hiển Thị Phát Hiện',
      [Language.HINDI]: 'डिटेक्शन दिखाएं',
      [Language.CHINESE]: '显示检测结果'
    },
    'ok': {
      [Language.ENGLISH]: 'OK',
      [Language.VIETNAMESE]: 'OK',
      [Language.HINDI]: 'ठीक है',
      [Language.CHINESE]: '确定'
    },
    'tryDifferentSearch': {
      [Language.ENGLISH]: 'Try a different search term',
      [Language.VIETNAMESE]: 'Thử từ khóa tìm kiếm khác',
      [Language.HINDI]: 'कोई अलग खोज शब्द आज़माएं',
      [Language.CHINESE]: '尝试不同的搜索关键词'
    },
    'cameraCapture': {
      [Language.ENGLISH]: 'Camera Capture',
      [Language.VIETNAMESE]: 'Chụp Ảnh',
      [Language.HINDI]: 'कैमरा कैप्चर',
      [Language.CHINESE]: '相机拍摄'
    },
    'takePhotos': {
      [Language.ENGLISH]: 'Take photos directly from the app',
      [Language.VIETNAMESE]: 'Chụp ảnh trực tiếp từ ứng dụng',
      [Language.HINDI]: 'ऐप से सीधे फोटो लें',
      [Language.CHINESE]: '直接从应用拍照'
    },
    'gallerySelection': {
      [Language.ENGLISH]: 'Gallery Selection',
      [Language.VIETNAMESE]: 'Chọn Từ Thư Viện',
      [Language.HINDI]: 'गैलरी चयन',
      [Language.CHINESE]: '图库选择'
    },
    'selectImages': {
      [Language.ENGLISH]: 'Select images from your gallery',
      [Language.VIETNAMESE]: 'Chọn hình ảnh từ thư viện của bạn',
      [Language.HINDI]: 'अपनी गैलरी से छवियां चुनें',
      [Language.CHINESE]: '从图库中选择图像'
    },
    'medicationExtraction': {
      [Language.ENGLISH]: 'Medication Extraction',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षण',
      [Language.CHINESE]: '药物提取'
    },
    'extractDetails': {
      [Language.ENGLISH]: 'Extract medication details from doctor notes',
      [Language.VIETNAMESE]: 'Trích xuất chi tiết thuốc từ ghi chú của bác sĩ',
      [Language.HINDI]: 'डॉक्टर के नोट्स से दवा के विवरण निकालें',
      [Language.CHINESE]: '从医生笔记中提取药物详情'
    },
    'startUsingCamera': {
      [Language.ENGLISH]: 'Start Using Camera',
      [Language.VIETNAMESE]: 'Bắt Đầu Sử Dụng Máy Ảnh',
      [Language.HINDI]: 'कैमरा का उपयोग शुरू करें',
      [Language.CHINESE]: '开始使用相机'
    },
    'saveAnalysis': {
      [Language.ENGLISH]: 'Save Analysis',
      [Language.VIETNAMESE]: 'Lưu Phân Tích',
      [Language.HINDI]: 'विश्लेषण सहेजें',
      [Language.CHINESE]: '保存分析'
    },
    'viewInSummary': {
      [Language.ENGLISH]: 'View in Summary',
      [Language.VIETNAMESE]: 'Xem trong Tóm Tắt',
      [Language.HINDI]: 'सारांश में देखें',
      [Language.CHINESE]: '在摘要中查看'
    },
    'medicationSummary': {
      [Language.ENGLISH]: 'Medication Summary',
      [Language.VIETNAMESE]: 'Tóm Tắt Thuốc',
      [Language.HINDI]: 'दवा सारांश',
      [Language.CHINESE]: '药物摘要'
    },
    'loadingData': {
      [Language.ENGLISH]: 'Loading data...',
      [Language.VIETNAMESE]: 'Đang tải dữ liệu...',
      [Language.HINDI]: 'डेटा लोड हो रहा है...',
      [Language.CHINESE]: '正在加载数据...'
    },
    'pillCounter': {
      [Language.ENGLISH]: 'Pill Counter',
      [Language.VIETNAMESE]: 'Đếm Viên Thuốc',
      [Language.HINDI]: 'गोली काउंटर',
      [Language.CHINESE]: '药丸计数器'
    },
    'countingPills': {
      [Language.ENGLISH]: 'Counting pills...',
      [Language.VIETNAMESE]: 'Đang đếm viên thuốc...',
      [Language.HINDI]: 'गोलियों की गिनती...',
      [Language.CHINESE]: '正在计数药丸...'
    },
    'pillDetected': {
      [Language.ENGLISH]: 'Pill Detected',
      [Language.VIETNAMESE]: 'Viên Thuốc Được Phát Hiện',
      [Language.HINDI]: 'गोली का पता चला',
      [Language.CHINESE]: '检测到药丸'
    },
    'pillsDetected': {
      [Language.ENGLISH]: 'Pills Detected',
      [Language.VIETNAMESE]: 'Viên Thuốc Được Phát Hiện',
      [Language.HINDI]: 'गोलियों का पता चला',
      [Language.CHINESE]: '检测到药丸'
    },
    'pillCountNote': {
      [Language.ENGLISH]: 'Counted from image',
      [Language.VIETNAMESE]: 'Đếm từ hình ảnh',
      [Language.HINDI]: 'छवि से गिना गया',
      [Language.CHINESE]: '从图像中计数'
    },
    'pillCountEstimateNote': {
      [Language.ENGLISH]: 'Estimated count from image',
      [Language.VIETNAMESE]: 'Ước tính từ hình ảnh',
      [Language.HINDI]: 'छवि से अनुमानित गणना',
      [Language.CHINESE]: '从图像估计的计数'
    },
    'estimate': {
      [Language.ENGLISH]: 'Estimate',
      [Language.VIETNAMESE]: 'Ước tính',
      [Language.HINDI]: 'अनुमान',
      [Language.CHINESE]: '估计'
    },
    'totalCount': {
      [Language.ENGLISH]: 'Total Count',
      [Language.VIETNAMESE]: 'Tổng số',
      [Language.HINDI]: 'कुल गिनती',
      [Language.CHINESE]: '总计数'
    },
    'prescriptionMode': {
      [Language.ENGLISH]: 'Prescription Mode',
      [Language.VIETNAMESE]: 'Chế Độ Đơn Thuốc',
      [Language.HINDI]: 'प्रिस्क्रिप्शन मोड',
      [Language.CHINESE]: '处方模式'
    },
    'pillCountingMode': {
      [Language.ENGLISH]: 'Pill Counting Mode',
      [Language.VIETNAMESE]: 'Chế Độ Đếm Viên Thuốc',
      [Language.HINDI]: 'गोली गिनती मोड',
      [Language.CHINESE]: '药丸计数模式'
    },
    'captureInstructionPrescription': {
      [Language.ENGLISH]: 'Position the prescription in the frame',
      [Language.VIETNAMESE]: 'Đặt đơn thuốc vào khung hình',
      [Language.HINDI]: 'प्रिस्क्रिप्शन को फ्रेम में रखें',
      [Language.CHINESE]: '将处方放在框架中'
    },
    'captureInstructionPills': {
      [Language.ENGLISH]: 'Position the pills clearly in the frame',
      [Language.VIETNAMESE]: 'Đặt các viên thuốc rõ ràng trong khung hình',
      [Language.HINDI]: 'गोलियों को स्पष्ट रूप से फ्रेम में रखें',
      [Language.CHINESE]: '将药丸清晰地放在框架中'
    },
    'totalPills': {
      [Language.ENGLISH]: 'Total Pills',
      [Language.VIETNAMESE]: 'Tổng Số Viên Thuốc',
      [Language.HINDI]: 'कुल गोलियां',
      [Language.CHINESE]: '药丸总数'
    },
    'reanalyze': {
      [Language.ENGLISH]: 'Re-analyze',
      [Language.VIETNAMESE]: 'Phân tích lại',
      [Language.HINDI]: 'पुन: विश्लेषण करें',
      [Language.CHINESE]: '重新分析'
    },
    'recordNameOptional': {
      [Language.ENGLISH]: 'Record name (optional)',
      [Language.VIETNAMESE]: 'Tên bản ghi (tùy chọn)',
      [Language.HINDI]: 'रिकॉर्ड नाम (वैकल्पिक)',
      [Language.CHINESE]: '记录名称（可选）'
    },
    'saveCount': {
      [Language.ENGLISH]: 'Save Count',
      [Language.VIETNAMESE]: 'Lưu Số Lượng',
      [Language.HINDI]: 'गिनती सहेजें',
      [Language.CHINESE]: '保存计数'
    },
    'pillCountSaved': {
      [Language.ENGLISH]: 'Pill count saved successfully!',
      [Language.VIETNAMESE]: 'Đã lưu số lượng viên thuốc thành công!',
      [Language.HINDI]: 'गोली की गिनती सफलतापूर्वक सहेजी गई!',
      [Language.CHINESE]: '药丸计数保存成功！'
    },
    'errorSavingPillCount': {
      [Language.ENGLISH]: 'Error saving pill count',
      [Language.VIETNAMESE]: 'Lỗi khi lưu số lượng viên thuốc',
      [Language.HINDI]: 'गोली की गिनती सहेजने में त्रुटि',
      [Language.CHINESE]: '保存药丸计数时出错'
    },
    'takePhoto': {
      [Language.ENGLISH]: 'Take Photo',
      [Language.VIETNAMESE]: 'Chụp Ảnh',
      [Language.HINDI]: 'फोटो लें',
      [Language.CHINESE]: '拍照'
    },
    'countPills': {
      [Language.ENGLISH]: 'Count Pills',
      [Language.VIETNAMESE]: 'Đếm Viên Thuốc',
      [Language.HINDI]: 'गोलियों की गिनती करें',
      [Language.CHINESE]: '计数药丸'
    },
    'retake': {
      [Language.ENGLISH]: 'Retake',
      [Language.VIETNAMESE]: 'Chụp lại',
      [Language.HINDI]: 'फिर से लें',
      [Language.CHINESE]: '重拍'
    },
    'noSavedAnalyses': {
      [Language.ENGLISH]: 'No saved analyses',
      [Language.VIETNAMESE]: 'Không có phân tích đã lưu',
      [Language.HINDI]: 'कोई सहेजा गया विश्लेषण नहीं',
      [Language.CHINESE]: '没有保存的分析'
    },
    'takePhotoToStart': {
      [Language.ENGLISH]: 'Take a photo of a doctor\'s note to get started',
      [Language.VIETNAMESE]: 'Chụp ảnh ghi chú của bác sĩ để bắt đầu',
      [Language.HINDI]: 'शुरू करने के लिए डॉक्टर के नोट की फोटो लें',
      [Language.CHINESE]: '拍摄医生笔记的照片以开始'
    },
    'analysisDetails': {
      [Language.ENGLISH]: 'Analysis Details',
      [Language.VIETNAMESE]: 'Chi Tiết Phân Tích',
      [Language.HINDI]: 'विश्लेषण विवरण',
      [Language.CHINESE]: '分析详情'
    },
    'medications': {
      [Language.ENGLISH]: 'medications',
      [Language.VIETNAMESE]: 'thuốc',
      [Language.HINDI]: 'दवाएं',
      [Language.CHINESE]: '药物'
    },
    'andMore': {
      [Language.ENGLISH]: 'and {count} more...',
      [Language.VIETNAMESE]: 'và {count} thuốc khác...',
      [Language.HINDI]: 'और {count} अधिक...',
      [Language.CHINESE]: '还有 {count} 个更多...'
    },
    'confirmDelete': {
      [Language.ENGLISH]: 'Confirm Delete',
      [Language.VIETNAMESE]: 'Xác Nhận Xóa',
      [Language.HINDI]: 'हटाने की पुष्टि करें',
      [Language.CHINESE]: '确认删除'
    },
    'deleteAnalysisConfirm': {
      [Language.ENGLISH]: 'Are you sure you want to delete this analysis?',
      [Language.VIETNAMESE]: 'Bạn có chắc chắn muốn xóa phân tích này không?',
      [Language.HINDI]: 'क्या आप वाकई इस विश्लेषण को हटाना चाहते हैं?',
      [Language.CHINESE]: '您确定要删除此分析吗？'
    },
    'cancel': {
      [Language.ENGLISH]: 'Cancel',
      [Language.VIETNAMESE]: 'Hủy',
      [Language.HINDI]: 'रद्द करें',
      [Language.CHINESE]: '取消'
    },
    'delete': {
      [Language.ENGLISH]: 'Delete',
      [Language.VIETNAMESE]: 'Xóa',
      [Language.HINDI]: 'हटाएं',
      [Language.CHINESE]: '删除'
    },
    'showDetails': {
      [Language.ENGLISH]: 'Show Details',
      [Language.VIETNAMESE]: 'Hiển Thị Chi Tiết',
      [Language.HINDI]: 'विवरण दिखाएं',
      [Language.CHINESE]: '显示详情'
    },
    'nameYourAnalysis': {
      [Language.ENGLISH]: 'Name Your Analysis',
      [Language.VIETNAMESE]: 'Đặt Tên Cho Phân Tích',
      [Language.HINDI]: 'अपने विश्लेषण का नाम दें',
      [Language.CHINESE]: '命名您的分析'
    },
    'enterAnalysisName': {
      [Language.ENGLISH]: 'Enter a name for this analysis',
      [Language.VIETNAMESE]: 'Nhập tên cho phân tích này',
      [Language.HINDI]: 'इस विश्लेषण के लिए एक नाम दर्ज करें',
      [Language.CHINESE]: '输入此分析的名称'
    },
    'webCameraNotAvailable': {
      [Language.ENGLISH]: 'Camera Not Available in Web Browser',
      [Language.VIETNAMESE]: 'Máy Ảnh Không Khả Dụng Trong Trình Duyệt Web',
      [Language.HINDI]: 'वेब ब्राउज़र में कैमरा उपलब्ध नहीं है',
      [Language.CHINESE]: '网页浏览器中相机不可用'
    },
    'webCameraMessagePrescription': {
      [Language.ENGLISH]: 'The camera feature is not available in web browsers. Please use the gallery option to select a photo of your prescription.',
      [Language.VIETNAMESE]: 'Tính năng máy ảnh không khả dụng trong trình duyệt web. Vui lòng sử dụng tùy chọn thư viện để chọn ảnh đơn thuốc của bạn.',
      [Language.HINDI]: 'कैमरा सुविधा वेब ब्राउज़र में उपलब्ध नहीं है। कृपया अपने प्रिस्क्रिप्शन की फोटो चुनने के लिए गैलरी विकल्प का उपयोग करें।',
      [Language.CHINESE]: '相机功能在网页浏览器中不可用。请使用图库选项选择您的处方照片。'
    },
    'webCameraMessagePills': {
      [Language.ENGLISH]: 'The camera feature is not available in web browsers. Please use the gallery option to select a photo of your pills.',
      [Language.VIETNAMESE]: 'Tính năng máy ảnh không khả dụng trong trình duyệt web. Vui lòng sử dụng tùy chọn thư viện để chọn ảnh viên thuốc của bạn.',
      [Language.HINDI]: 'कैमरा सुविधा वेब ब्राउज़र में उपलब्ध नहीं है। कृपया अपनी गोलियों की फोटो चुनने के लिए गैलरी विकल्प का उपयोग करें।',
      [Language.CHINESE]: '相机功能在网页浏览器中不可用。请使用图库选项选择您的药丸照片。'
    },
    'selectFromGallery': {
      [Language.ENGLISH]: 'Select from Gallery',
      [Language.VIETNAMESE]: 'Chọn từ Thư Viện',
      [Language.HINDI]: 'गैलरी से चुनें',
      [Language.CHINESE]: '从图库中选择'
    },
    'requestPermission': {
      [Language.ENGLISH]: 'Continue',
      [Language.VIETNAMESE]: 'Tiếp tục',
      [Language.HINDI]: 'जारी रखें',
      [Language.CHINESE]: '继续'
    },
    'cameraPermissionRequired': {
      [Language.ENGLISH]: 'Camera permission is required to use this feature',
      [Language.VIETNAMESE]: 'Cần quyền truy cập camera để sử dụng tính năng này',
      [Language.HINDI]: 'इस सुविधा का उपयोग करने के लिए कैमरा अनुमति आवश्यक है',
      [Language.CHINESE]: '使用此功能需要相机权限'
    },
    'goBack': {
      [Language.ENGLISH]: 'Go Back',
      [Language.VIETNAMESE]: 'Quay Lại',
      [Language.HINDI]: 'वापस जाएं',
      [Language.CHINESE]: '返回'
    },
    // CAPTCHA related translations
    'verifyYouAreHuman': {
      [Language.ENGLISH]: 'Verify You Are Human',
      [Language.VIETNAMESE]: 'Xác Minh Bạn Là Người',
      [Language.HINDI]: 'सत्यापित करें कि आप मानव हैं',
      [Language.CHINESE]: '验证您是人类'
    },
    'captchaDescription': {
      [Language.ENGLISH]: 'Please complete the CAPTCHA verification to continue',
      [Language.VIETNAMESE]: 'Vui lòng hoàn thành xác minh CAPTCHA để tiếp tục',
      [Language.HINDI]: 'जारी रखने के लिए कृपया CAPTCHA सत्यापन पूरा करें',
      [Language.CHINESE]: '请完成验证码验证以继续'
    },
    'captchaExpired': {
      [Language.ENGLISH]: 'CAPTCHA verification expired. Please try again.',
      [Language.VIETNAMESE]: 'Xác minh CAPTCHA đã hết hạn. Vui lòng thử lại.',
      [Language.HINDI]: 'CAPTCHA सत्यापन समाप्त हो गया। कृपया पुन: प्रयास करें।',
      [Language.CHINESE]: '验证码验证已过期。请重试。'
    },
    'captchaError': {
      [Language.ENGLISH]: 'Error during CAPTCHA verification. Please try again.',
      [Language.VIETNAMESE]: 'Lỗi trong quá trình xác minh CAPTCHA. Vui lòng thử lại.',
      [Language.HINDI]: 'CAPTCHA सत्यापन के दौरान त्रुटि। कृपया पुन: प्रयास करें।',
      [Language.CHINESE]: '验证码验证期间出错。请重试。'
    },
    'captchaLoading': {
      [Language.ENGLISH]: 'Loading CAPTCHA verification...',
      [Language.VIETNAMESE]: 'Đang tải xác minh CAPTCHA...',
      [Language.HINDI]: 'CAPTCHA सत्यापन लोड हो रहा है...',
      [Language.CHINESE]: '正在加载验证码验证...'
    },
    'verifying': {
      [Language.ENGLISH]: 'Verifying...',
      [Language.VIETNAMESE]: 'Đang xác minh...',
      [Language.HINDI]: 'सत्यापित कर रहा है...',
      [Language.CHINESE]: '正在验证...'
    },
    'captchaVerified': {
      [Language.ENGLISH]: 'Verify I\'m Human',
      [Language.VIETNAMESE]: 'Xác Minh Tôi Là Người',
      [Language.HINDI]: 'सत्यापित करें कि मैं मानव हूँ',
      [Language.CHINESE]: '验证我是人类'
    },
    'save': {
      [Language.ENGLISH]: 'Save',
      [Language.VIETNAMESE]: 'Lưu',
      [Language.HINDI]: 'सहेजें',
      [Language.CHINESE]: '保存'
    },
    'deleteAll': {
      [Language.ENGLISH]: 'Delete All',
      [Language.VIETNAMESE]: 'Xóa Tất Cả',
      [Language.HINDI]: 'सभी हटाएं',
      [Language.CHINESE]: '删除全部'
    },
    'confirmDeleteAll': {
      [Language.ENGLISH]: 'Confirm Delete All',
      [Language.VIETNAMESE]: 'Xác Nhận Xóa Tất Cả',
      [Language.HINDI]: 'सभी हटाने की पुष्टि करें',
      [Language.CHINESE]: '确认删除全部'
    },
    'deleteAllConfirmMessage': {
      [Language.ENGLISH]: 'Are you sure you want to delete all saved analyses? This action cannot be undone.',
      [Language.VIETNAMESE]: 'Bạn có chắc chắn muốn xóa tất cả các phân tích đã lưu không? Hành động này không thể hoàn tác.',
      [Language.HINDI]: 'क्या आप वाकई सभी सहेजे गए विश्लेषण हटाना चाहते हैं? यह कार्रवाई वापस नहीं की जा सकती है।',
      [Language.CHINESE]: '您确定要删除所有保存的分析吗？此操作无法撤消。'
    },
    'searchAnalyses': {
      [Language.ENGLISH]: 'Search analyses...',
      [Language.VIETNAMESE]: 'Tìm kiếm phân tích...',
      [Language.HINDI]: 'विश्लेषण खोजें...',
      [Language.CHINESE]: '搜索分析...'
    },
    'noSearchResults': {
      [Language.ENGLISH]: 'No matching analyses found',
      [Language.VIETNAMESE]: 'Không tìm thấy phân tích phù hợp',
      [Language.HINDI]: 'कोई मेल खाते विश्लेषण नहीं मिले',
      [Language.CHINESE]: '未找到匹配的分析'
    },
    'tryDifferentSearch': {
      [Language.ENGLISH]: 'Try a different search term',
      [Language.VIETNAMESE]: 'Thử với từ khóa tìm kiếm khác',
      [Language.HINDI]: 'कोई अलग खोज शब्द आज़माएं',
      [Language.CHINESE]: '尝试不同的搜索关键词'
    },
    'clearSearch': {
      [Language.ENGLISH]: 'Clear Search',
      [Language.VIETNAMESE]: 'Xóa Tìm Kiếm',
      [Language.HINDI]: 'खोज साफ़ करें',
      [Language.CHINESE]: '清除搜索'
    },
    'error': {
      [Language.ENGLISH]: 'Error',
      [Language.VIETNAMESE]: 'Lỗi',
      [Language.HINDI]: 'त्रुटि',
      [Language.CHINESE]: '错误'
    },
    'errorClearingAnalyses': {
      [Language.ENGLISH]: 'There was an error clearing the analyses. Please try again.',
      [Language.VIETNAMESE]: 'Đã xảy ra lỗi khi xóa các phân tích. Vui lòng thử lại.',
      [Language.HINDI]: 'विश्लेषण साफ़ करने में त्रुटि हुई। कृपया पुन: प्रयास करें।',
      [Language.CHINESE]: '清除分析时出错。请重试。'
    },
    'translate': {
      [Language.ENGLISH]: 'Translate',
      [Language.VIETNAMESE]: 'Dịch',
      [Language.HINDI]: 'अनुवाद करें',
      [Language.CHINESE]: '翻译'
    },
    'translatingMedications': {
      [Language.ENGLISH]: 'Translating medication information to your selected language...',
      [Language.VIETNAMESE]: 'Đang dịch thông tin thuốc sang ngôn ngữ bạn đã chọn...',
      [Language.HINDI]: 'आपकी चुनी हुई भाषा में दवा की जानकारी का अनुवाद किया जा रहा है...',
      [Language.CHINESE]: '正在将药物信息翻译成您选择的语言...'
    },
    // Account Settings translations
    'displayName': {
      [Language.ENGLISH]: 'Display Name',
      [Language.VIETNAMESE]: 'Tên Hiển Thị',
      [Language.HINDI]: 'प्रदर्शन नाम',
      [Language.CHINESE]: '显示名称'
    },
    'email': {
      [Language.ENGLISH]: 'Email',
      [Language.VIETNAMESE]: 'Email',
      [Language.HINDI]: 'ईमेल',
      [Language.CHINESE]: '电子邮件'
    },
    'updateProfile': {
      [Language.ENGLISH]: 'Update Profile',
      [Language.VIETNAMESE]: 'Cập Nhật Hồ Sơ',
      [Language.HINDI]: 'प्रोफ़ाइल अपडेट करें',
      [Language.CHINESE]: '更新个人资料'
    },
    'changePassword': {
      [Language.ENGLISH]: 'Change Password',
      [Language.VIETNAMESE]: 'Đổi Mật Khẩu',
      [Language.HINDI]: 'पासवर्ड बदलें',
      [Language.CHINESE]: '更改密码'
    },
    'notificationPreferences': {
      [Language.ENGLISH]: 'Notification Preferences',
      [Language.VIETNAMESE]: 'Tùy Chọn Thông Báo',
      [Language.HINDI]: 'अधिसूचना प्राथमिकताएँ',
      [Language.CHINESE]: '通知首选项'
    },
    'emailNotifications': {
      [Language.ENGLISH]: 'Email Notifications',
      [Language.VIETNAMESE]: 'Thông Báo Email',
      [Language.HINDI]: 'ईमेल सूचनाएं',
      [Language.CHINESE]: '电子邮件通知'
    },
    'appNotifications': {
      [Language.ENGLISH]: 'App Notifications',
      [Language.VIETNAMESE]: 'Thông Báo Ứng Dụng',
      [Language.HINDI]: 'ऐप सूचनाएं',
      [Language.CHINESE]: '应用通知'
    },
    'saveNotificationSettings': {
      [Language.ENGLISH]: 'Save Notification Settings',
      [Language.VIETNAMESE]: 'Lưu Cài Đặt Thông Báo',
      [Language.HINDI]: 'अधिसूचना सेटिंग्स सहेजें',
      [Language.CHINESE]: '保存通知设置'
    },
    'privacyAndSecurity': {
      [Language.ENGLISH]: 'Privacy & Security',
      [Language.VIETNAMESE]: 'Quyền Riêng Tư & Bảo Mật',
      [Language.HINDI]: 'गोपनीयता और सुरक्षा',
      [Language.CHINESE]: '隐私与安全'
    },
    'dataCollection': {
      [Language.ENGLISH]: 'Data Collection',
      [Language.VIETNAMESE]: 'Thu Thập Dữ Liệu',
      [Language.HINDI]: 'डेटा संग्रह',
      [Language.CHINESE]: '数据收集'
    },
    'shareUsageData': {
      [Language.ENGLISH]: 'Share Usage Data',
      [Language.VIETNAMESE]: 'Chia Sẻ Dữ Liệu Sử Dụng',
      [Language.HINDI]: 'उपयोग डेटा साझा करें',
      [Language.CHINESE]: '共享使用数据'
    },
    'savePrivacySettings': {
      [Language.ENGLISH]: 'Save Privacy Settings',
      [Language.VIETNAMESE]: 'Lưu Cài Đặt Quyền Riêng Tư',
      [Language.HINDI]: 'गोपनीयता सेटिंग्स सहेजें',
      [Language.CHINESE]: '保存隐私设置'
    },
    // Help translations
    'frequentlyAskedQuestions': {
      [Language.ENGLISH]: 'Frequently Asked Questions',
      [Language.VIETNAMESE]: 'Câu Hỏi Thường Gặp',
      [Language.HINDI]: 'अक्सर पूछे जाने वाले प्रश्न',
      [Language.CHINESE]: '常见问题'
    },
    'contactSupport': {
      [Language.ENGLISH]: 'Contact Support',
      [Language.VIETNAMESE]: 'Liên Hệ Hỗ Trợ',
      [Language.HINDI]: 'सहायता से संपर्क करें',
      [Language.CHINESE]: '联系支持'
    },
    'subject': {
      [Language.ENGLISH]: 'Subject',
      [Language.VIETNAMESE]: 'Chủ Đề',
      [Language.HINDI]: 'विषय',
      [Language.CHINESE]: '主题'
    },
    'message': {
      [Language.ENGLISH]: 'Message',
      [Language.VIETNAMESE]: 'Tin Nhắn',
      [Language.HINDI]: 'संदेश',
      [Language.CHINESE]: '消息'
    },
    'sendMessage': {
      [Language.ENGLISH]: 'Send Message',
      [Language.VIETNAMESE]: 'Gửi Tin Nhắn',
      [Language.HINDI]: 'संदेश भेजें',
      [Language.CHINESE]: '发送消息'
    },
    'troubleshootingGuides': {
      [Language.ENGLISH]: 'Troubleshooting Guides',
      [Language.VIETNAMESE]: 'Hướng Dẫn Khắc Phục Sự Cố',
      [Language.HINDI]: 'समस्या निवारण गाइड',
      [Language.CHINESE]: '故障排除指南'
    },
    'tutorialVideos': {
      [Language.ENGLISH]: 'Tutorial Videos',
      [Language.VIETNAMESE]: 'Video Hướng Dẫn',
      [Language.HINDI]: 'ट्यूटोरियल वीडियो',
      [Language.CHINESE]: '教程视频'
    },
    'legal': {
      [Language.ENGLISH]: 'Legal',
      [Language.VIETNAMESE]: 'Pháp Lý',
      [Language.HINDI]: 'कानूनी',
      [Language.CHINESE]: '法律'
    }
  };

  // Get the translation or fall back to the key itself
  let translation = translations[key]?.[language] || key;

  // Replace parameters if provided
  if (params) {
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      translation = translation.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(paramValue));
    });
  }

  return translation;
};

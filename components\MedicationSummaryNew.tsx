import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Image, ActivityIndicator, Alert, useWindowDimensions, RefreshControl, TextInput, Modal } from 'react-native';
import { getMedicationAnalyses, StoredAnalysis, deleteMedicationAnalysis, clearAllMedicationAnalyses, searchMedicationAnalyses } from '../services/storageService';
import { useLanguage } from '../contexts/LanguageContext';
import MedicationTable from './MedicationTable';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect, useRouter } from 'expo-router';
import { Text, Card, Container, Header, Button, Divider, Badge } from './ui';
import { Colors, Spacing, BorderRadius, Typography } from '../constants/DesignSystem';

const MedicationSummaryNew = () => {
  const [analyses, setAnalyses] = useState<StoredAnalysis[]>([]);
  const [filteredAnalyses, setFilteredAnalyses] = useState<StoredAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searching, setSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAnalysis, setSelectedAnalysis] = useState<StoredAnalysis | null>(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false);
  const { t } = useLanguage();
  const { width } = useWindowDimensions();
  const isTablet = width > 768;
  const router = useRouter();

  // Refresh data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Summary screen focused - refreshing data');
      loadAnalyses();
      return () => {};
    }, [])
  );

  // Initial load
  useEffect(() => {
    loadAnalyses();
  }, []);

  const loadAnalyses = async () => {
    if (!refreshing) setLoading(true);
    try {
      console.log('Loading medication analyses from storage');
      const data = await getMedicationAnalyses();
      console.log(`Found ${data.length} saved analyses`);
      setAnalyses(data);

      // Apply any existing search filter
      if (searchQuery) {
        handleSearch(searchQuery, data);
      } else {
        setFilteredAnalyses(data);
      }

      // If we have a selected analysis, make sure it's still in the list
      if (selectedAnalysis) {
        const stillExists = data.find(a => a.id === selectedAnalysis.id);
        if (!stillExists) {
          setSelectedAnalysis(null);
          setDetailsVisible(false);
        }
      }
    } catch (error) {
      console.error('Error loading analyses:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setSearching(false);
    }
  };

  const handleSearch = async (query: string, dataToSearch?: StoredAnalysis[]) => {
    setSearchQuery(query);
    setSearching(true);

    try {
      // Use provided data or search in all analyses
      const data = dataToSearch || analyses;

      if (!query.trim()) {
        setFilteredAnalyses(data);
        setSearching(false);
        return;
      }

      // Filter locally for instant results
      const lowerQuery = query.toLowerCase();
      const filtered = data.filter(analysis => {
        // Search by name (safely check for undefined)
        if (analysis.name && analysis.name.toLowerCase().includes(lowerQuery)) return true;

        // Search by date
        if (analysis.date) {
          const date = new Date(analysis.date);
          const dateStr = date.toLocaleDateString();
          if (dateStr.includes(lowerQuery)) return true;
        }

        // Search by medication names (safely check for undefined)
        if (analysis.medications && analysis.medications.length > 0) {
          const hasMedication = analysis.medications.some(med =>
            med && med.name && med.name.toLowerCase().includes(lowerQuery));
          if (hasMedication) return true;
        }

        return false;
      });

      setFilteredAnalyses(filtered);
    } catch (error) {
      console.error('Error searching analyses:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setFilteredAnalyses(analyses);
  };

  const handleDeleteAll = async () => {
    setShowDeleteAllModal(false);
    setLoading(true);
    try {
      await clearAllMedicationAnalyses();
      setAnalyses([]);
      setFilteredAnalyses([]);
      setSelectedAnalysis(null);
      setDetailsVisible(false);
    } catch (error) {
      console.error('Error clearing all analyses:', error);
      Alert.alert(t('error'), t('errorClearingAnalyses'));
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadAnalyses();
  }, []);

  const handleDeleteAnalysis = async (id: string) => {
    Alert.alert(
      t('confirmDelete'),
      t('deleteAnalysisConfirm'),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteMedicationAnalysis(id);
              // If the deleted analysis was selected, clear the selection
              if (selectedAnalysis?.id === id) {
                setSelectedAnalysis(null);
              }
              // Refresh the list
              loadAnalyses();
            } catch (error) {
              console.error('Error deleting analysis:', error);
            }
          },
        },
      ]
    );
  };

  const handleViewDetails = (analysis: StoredAnalysis) => {
    router.push(`/medication/${analysis.id}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Render the delete all confirmation modal
  const renderDeleteAllModal = () => (
    <Modal
      visible={showDeleteAllModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowDeleteAllModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text variant="h3" weight="semibold" style={styles.modalTitle}>
            {t('confirmDeleteAll')}
          </Text>
          <Text variant="body" color="secondary" style={styles.modalMessage}>
            {t('deleteAllConfirmMessage')}
          </Text>
          <View style={styles.modalButtons}>
            <Button
              title={t('cancel')}
              variant="outline"
              size="medium"
              onPress={() => setShowDeleteAllModal(false)}
              style={styles.modalButton}
            />
            <Button
              title={t('deleteAll')}
              variant="error"
              size="medium"
              onPress={handleDeleteAll}
              style={styles.modalButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  // Render an analysis item in the list
  const renderAnalysisItem = ({ item }: { item: StoredAnalysis }) => (
    <Card
      style={styles.analysisCard}
      elevation="sm"
    >
      <View style={styles.analysisHeader}>
        <TouchableOpacity
          style={styles.analysisInfo}
          onPress={() => handleViewDetails(item)}
        >
          <Text variant="subtitle" weight="semibold" numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.analysisSubheader}>
            <View style={styles.dateContainer}>
              <Ionicons name="calendar-outline" size={14} color={Colors.textTertiary} />
              <Text variant="caption" color="tertiary" style={styles.dateText}>
                {formatDate(item.date)}
              </Text>
            </View>
            <Badge
              label={`${item.medications.length} ${
                item.medications.length === 1 ? t('medication') : t('medications')
              }`}
              variant="primary"
              size="small"
            />
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteAnalysis(item.id)}
        >
          <Ionicons name="close" size={20} color={Colors.error} />
        </TouchableOpacity>
      </View>

      <Divider spacing={Spacing.xs} />

      <TouchableOpacity
        style={styles.analysisContent}
        onPress={() => handleViewDetails(item)}
      >
        {item.imageUri && (
          <Image source={{ uri: item.imageUri }} style={styles.thumbnailImage} resizeMode="cover" />
        )}

        <View style={styles.medicationPreview}>
          <MedicationTable
            medications={item.medications}
            compact={true}
          />
        </View>
      </TouchableOpacity>
    </Card>
  );

  // Render the search bar
  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Ionicons name="search-outline" size={20} color={Colors.textTertiary} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={t('searchAnalyses')}
          value={searchQuery}
          onChangeText={handleSearch}
          clearButtonMode="while-editing"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
            <Ionicons name="close-circle" size={18} color={Colors.textTertiary} />
          </TouchableOpacity>
        )}
      </View>
      {searching && <ActivityIndicator size="small" color={Colors.primary} style={styles.searchingIndicator} />}
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <Card style={styles.emptyCard}>
      <Ionicons
        name={searchQuery ? "search-outline" : "medical-outline"}
        size={48}
        color={Colors.textTertiary}
      />
      <Text variant="subtitle" weight="medium" color="secondary" style={styles.emptyText}>
        {searchQuery ? t('noSearchResults') : t('noSavedAnalyses')}
      </Text>
      <Text variant="caption" color="tertiary" style={styles.emptySubtext}>
        {searchQuery ? t('tryDifferentSearch') : t('takePhotoToStart')}
      </Text>
      {searchQuery && (
        <Button
          title={t('clearSearch')}
          variant="outline"
          size="small"
          onPress={handleClearSearch}
          style={styles.clearSearchButton}
        />
      )}
    </Card>
  );

  return (
    <Container scrollable={false} padded={false}>
      {renderDeleteAllModal()}

      <Header
        title={t('medicationSummary')}
        rightComponent={
          analyses.length > 0 ? (
            <TouchableOpacity
              style={styles.deleteAllButton}
              onPress={() => setShowDeleteAllModal(true)}
            >
              <Ionicons name="trash-outline" size={20} color={Colors.error} />
            </TouchableOpacity>
          ) : undefined
        }
      />

      <View style={styles.content}>
        {analyses.length > 0 && renderSearchBar()}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text variant="body" color="secondary" style={styles.loadingText}>
              {t('loadingData')}
            </Text>
          </View>
        ) : analyses.length === 0 || (filteredAnalyses.length === 0 && searchQuery) ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={filteredAnalyses}
            renderItem={renderAnalysisItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[Colors.primary]}
                tintColor={Colors.primary}
              />
            }
            ListEmptyComponent={
              searchQuery ? (
                <View style={styles.inlineEmptySearch}>
                  <Text variant="body" color="secondary" style={styles.inlineEmptyText}>
                    {t('noSearchResults')}
                  </Text>
                  <Button
                    title={t('clearSearch')}
                    variant="text"
                    size="small"
                    onPress={handleClearSearch}
                  />
                </View>
              ) : null
            }
          />
        )}
      </View>
    </Container>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    padding: Spacing.md,
  },
  deleteAllButton: {
    padding: Spacing.xs,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    height: 40,
  },
  searchIcon: {
    marginRight: Spacing.xs,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: Typography.fontSize.md,
    color: Colors.textPrimary,
  },
  clearButton: {
    padding: Spacing.xs,
  },
  searchingIndicator: {
    marginLeft: Spacing.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
  },
  emptyCard: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
    margin: Spacing.md,
  },
  emptyText: {
    marginTop: Spacing.md,
    marginBottom: Spacing.xs,
  },
  emptySubtext: {
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  clearSearchButton: {
    marginTop: Spacing.sm,
  },
  inlineEmptySearch: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  inlineEmptyText: {
    marginBottom: Spacing.sm,
  },
  listContent: {
    paddingBottom: Spacing.lg,
  },
  analysisCard: {
    marginBottom: Spacing.md,
  },
  analysisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  analysisInfo: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  analysisSubheader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    marginLeft: Spacing.xs,
  },
  deleteButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.errorLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.error,
  },
  analysisContent: {
    flexDirection: 'row',
    marginTop: Spacing.sm,
  },
  thumbnailImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.sm,
  },
  medicationPreview: {
    flex: 1,
  },
  previewText: {
    marginBottom: Spacing.xs,
  },
  moreText: {
    marginTop: Spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.md,
    padding: Spacing.lg,
    width: '80%',
    maxWidth: 400,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  modalMessage: {
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
});

export default MedicationSummaryNew;

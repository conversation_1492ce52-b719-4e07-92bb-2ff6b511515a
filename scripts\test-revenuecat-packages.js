/**
 * Test script to check if RevenueCat can fetch packages
 * This tests the "No subscription packages available" fix
 * 
 * Run this in your React Native app's debug console or add it temporarily to your app
 */

// Add this to your app temporarily to test package fetching
export const testRevenueCatPackages = async () => {
  console.log('🧪 Testing RevenueCat package fetching...');
  
  try {
    // Import your IAP service
    const { initializeRevenueCat, getProducts, IAP_PRODUCT_IDS } = require('../services/appleIAPService');
    
    console.log('1. Testing RevenueCat initialization...');
    const initResult = await initializeRevenueCat();
    console.log('   Initialization result:', initResult);
    
    if (!initResult) {
      console.error('❌ RevenueCat initialization failed');
      return false;
    }
    
    console.log('2. Testing package fetching...');
    const packages = await getProducts();
    console.log('   Packages found:', packages.length);
    
    if (packages.length === 0) {
      console.error('❌ No packages found - this is the issue Apple reported');
      console.log('   Expected product IDs:', Object.values(IAP_PRODUCT_IDS));
      return false;
    }
    
    console.log('3. Checking package details...');
    packages.forEach((pkg, index) => {
      console.log(`   Package ${index + 1}:`);
      console.log(`     ID: ${pkg.product.identifier}`);
      console.log(`     Price: ${pkg.product.priceString}`);
      console.log(`     Title: ${pkg.product.title}`);
    });
    
    console.log('4. Verifying expected products...');
    const expectedProducts = Object.values(IAP_PRODUCT_IDS);
    const foundProducts = packages.map(p => p.product.identifier);
    
    expectedProducts.forEach(expectedId => {
      if (foundProducts.includes(expectedId)) {
        console.log(`   ✅ Found: ${expectedId}`);
      } else {
        console.log(`   ❌ Missing: ${expectedId}`);
      }
    });
    
    const allFound = expectedProducts.every(id => foundProducts.includes(id));
    
    if (allFound) {
      console.log('🎉 SUCCESS: All expected packages found!');
      console.log('   The "No subscription packages available" issue should be fixed');
      return true;
    } else {
      console.log('⚠️  WARNING: Some expected packages are missing');
      console.log('   This could still cause the "No subscription packages available" error');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('   Error details:', error.message);
    return false;
  }
};

// Instructions for testing
console.log(`
🧪 HOW TO TEST REVENUECAT PACKAGES:

1. Add this function to your app temporarily
2. Call it from a button or component
3. Check the console output
4. Look for "SUCCESS" or error messages

Example usage in a React Native component:

import { testRevenueCatPackages } from './scripts/test-revenuecat-packages';

// In your component
const handleTestPackages = async () => {
  const result = await testRevenueCatPackages();
  Alert.alert(
    'Test Result', 
    result ? 'Packages loaded successfully!' : 'Package loading failed - check console'
  );
};

<Button title="Test RevenueCat Packages" onPress={handleTestPackages} />
`);

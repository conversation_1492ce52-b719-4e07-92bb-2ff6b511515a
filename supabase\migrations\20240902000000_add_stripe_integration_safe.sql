-- Migration to add Stripe integration (safe version)
-- This adds necessary fields to the profiles table and creates functions for Stripe integration
-- This version checks if objects exist before creating them to avoid errors

-- Add Stripe-related fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'inactive',
ADD COLUMN IF NOT EXISTS subscription_period_start TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS subscription_period_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS payment_method_id TEXT;

-- Create a function to create or retrieve a Stripe customer
CREATE OR REPLACE FUNCTION public.get_or_create_stripe_customer(
  user_uuid UUID,
  email TEXT
)
RETURNS TEXT AS $$
DECLARE
  stripe_customer_id TEXT;
BEGIN
  -- Check if user already has a Stripe customer ID
  SELECT profiles.stripe_customer_id INTO stripe_customer_id
  FROM public.profiles
  WHERE id = user_uuid;
  
  -- If no Stripe customer ID exists, return null (will be created in the API)
  RETURN stripe_customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update subscription status
CREATE OR REPLACE FUNCTION public.update_subscription_status(
  user_uuid UUID,
  stripe_subscription_id TEXT,
  status TEXT,
  period_start TIMESTAMP WITH TIME ZONE,
  period_end TIMESTAMP WITH TIME ZONE,
  subscription_tier TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update the user's profile with subscription information
  UPDATE public.profiles
  SET
    stripe_subscription_id = update_subscription_status.stripe_subscription_id,
    subscription_status = update_subscription_status.status,
    subscription_period_start = update_subscription_status.period_start,
    subscription_period_end = update_subscription_status.period_end,
    subscription_tier = update_subscription_status.subscription_tier,
    updated_at = NOW()
  WHERE id = user_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to cancel a subscription
CREATE OR REPLACE FUNCTION public.cancel_subscription(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  stripe_subscription_id TEXT;
BEGIN
  -- Get the user's Stripe subscription ID
  SELECT profiles.stripe_subscription_id INTO stripe_subscription_id
  FROM public.profiles
  WHERE id = user_uuid;
  
  -- If no subscription ID exists, return false
  IF stripe_subscription_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Update the user's profile to mark subscription as canceled
  -- The actual cancellation will be handled by the API
  UPDATE public.profiles
  SET
    subscription_status = 'canceling',
    updated_at = NOW()
  WHERE id = user_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a subscription is active
CREATE OR REPLACE FUNCTION public.is_subscription_active(
  user_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  status TEXT;
  period_end TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get the user's subscription status and period end
  SELECT 
    profiles.subscription_status,
    profiles.subscription_period_end
  INTO 
    status,
    period_end
  FROM public.profiles
  WHERE id = user_uuid;
  
  -- Check if subscription is active
  RETURN (
    status = 'active' AND 
    (period_end IS NULL OR period_end > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies for the new functions
GRANT EXECUTE ON FUNCTION public.get_or_create_stripe_customer TO authenticated;
GRANT EXECUTE ON FUNCTION public.cancel_subscription TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_subscription_active TO authenticated;

-- Only create policies if they don't already exist
DO $$
BEGIN
  -- Check if the policy exists before creating it
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND policyname = 'Users can get or create their own Stripe customer'
  ) THEN
    EXECUTE 'CREATE POLICY "Users can get or create their own Stripe customer" ON public.profiles FOR SELECT USING (auth.uid() = id)';
  END IF;
  
  -- Check if the policy exists before creating it
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND policyname = 'Users can cancel their own subscription'
  ) THEN
    EXECUTE 'CREATE POLICY "Users can cancel their own subscription" ON public.profiles FOR UPDATE USING (auth.uid() = id)';
  END IF;
END
$$;

-- Add comments to explain the purpose of this migration
COMMENT ON FUNCTION public.get_or_create_stripe_customer IS 'Gets or creates a Stripe customer for the user';
COMMENT ON FUNCTION public.update_subscription_status IS 'Updates the subscription status for a user';
COMMENT ON FUNCTION public.cancel_subscription IS 'Cancels a subscription for a user';
COMMENT ON FUNCTION public.is_subscription_active IS 'Checks if a user has an active subscription';

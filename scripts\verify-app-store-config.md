# 🔍 App Store Connect Configuration Verification

## ✅ Checklist to Verify Your Setup

### **1. In-App Purchase Products**
Go to App Store Connect → Your App → Features → In-App Purchases

**Check that these products exist and are approved:**
- `com.pilllogic.app.pro.monthly.v2` (Pro Monthly)
- `com.pilllogic.app.premium.monthly` (Premium Monthly)

**Verify each product:**
- [ ] Status is "Ready to Submit" or "Approved"
- [ ] Price is set correctly ($4.99 and $5.99)
- [ ] Product ID matches exactly (case-sensitive)
- [ ] Localizations are complete

### **2. RevenueCat Configuration**
Go to RevenueCat Dashboard → Your Project

**Check Offerings:**
- [ ] Default offering exists
- [ ] Products are added to the offering
- [ ] Product IDs match App Store Connect exactly
- [ ] API keys are configured for iOS

**Check Integrations:**
- [ ] App Store Connect integration is set up
- [ ] Shared secret is configured (if using)

### **3. App Configuration**
In your app code, verify:

**Product IDs match:**
```javascript
export const IAP_PRODUCT_IDS = {
  PRO_MONTHLY: 'com.pilllogic.app.pro.monthly.v2',
  PREMIUM_MONTHLY: 'com.pilllogic.app.premium.monthly',
};
```

**RevenueCat API Keys:**
```javascript
const REVENUECAT_API_KEY = {
  ios: 'appl_YOUR_IOS_API_KEY',
  android: 'goog_YOUR_ANDROID_API_KEY'
};
```

### **4. Test What You Can**

**A. Run Package Test (in your app):**
```bash
# Add the test function to your app temporarily
# Call testRevenueCatPackages() from a button
# Check console for results
```

**B. Run Server Tests:**
```bash
# Test receipt validation function
node scripts/test-receipt-validation.js

# Check monitoring dashboard
open scripts/validation-dashboard.html
```

**C. Check Logs:**
- Supabase Dashboard → Functions → Logs
- Look for any errors in validate-apple-receipt function

## 🚨 Common Issues and Fixes

### **"No subscription packages available"**
**Possible causes:**
1. Product IDs don't match between App Store Connect and RevenueCat
2. Products not approved in App Store Connect
3. RevenueCat offering not configured
4. API keys incorrect

**How to verify:**
1. Double-check product IDs (case-sensitive)
2. Ensure products are "Ready to Submit" in App Store Connect
3. Check RevenueCat dashboard for offerings
4. Test package fetching with the test script

### **Receipt validation fails**
**Possible causes:**
1. Apple Shared Secret not configured
2. Network connectivity issues
3. Invalid receipt format

**How to verify:**
1. Check Supabase environment variables
2. Test validation function accessibility
3. Monitor validation logs

## 🎯 What Apple Will Test

**Apple's testing process:**
1. Install your app on test devices
2. Try to purchase subscriptions using sandbox test accounts
3. Verify receipts are validated correctly
4. Check that subscriptions activate properly

**What you'll see in monitoring:**
- Validation attempts with `sandbox` environment
- High success rate (>95%)
- No "No subscription packages available" errors

## 📊 Success Indicators

**Before submitting to Apple:**
- [ ] Package test returns all expected products
- [ ] Receipt validation function is accessible
- [ ] Monitoring dashboard loads without errors

**During Apple review:**
- [ ] Validation attempts appear in dashboard
- [ ] Both production and sandbox environments used
- [ ] Success rate above 95%
- [ ] No package availability errors

**After Apple approval:**
- [ ] Real users can purchase subscriptions
- [ ] Validation continues to work in production
- [ ] Monitoring shows healthy metrics

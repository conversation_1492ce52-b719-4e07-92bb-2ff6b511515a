#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check Apple receipt validation status
 * Run this to monitor if the fixes are working in production
 */

const SUPABASE_URL = 'https://lckiptkbxurjxddeubew.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxja2lwdGtieHVyanhkZGV1YmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA5MDI5NzQsImV4cCI6MjAzNjQ3ODk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with your actual anon key

async function checkValidationStatus() {
  try {
    console.log('🔍 Checking Apple receipt validation status...\n');

    const response = await fetch(`${SUPABASE_URL}/functions/v1/validation-monitoring`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Display results
    console.log('📊 VALIDATION STATUS REPORT');
    console.log('=' .repeat(50));
    console.log(`Period: ${data.period}`);
    console.log(`Last Updated: ${new Date(data.timestamp).toLocaleString()}`);
    console.log(`Overall Status: ${getStatusEmoji(data.status)} ${data.status}\n`);

    console.log('📈 SUMMARY STATISTICS');
    console.log('-'.repeat(30));
    console.log(`Total Attempts: ${data.summary.totalAttempts}`);
    console.log(`Successful: ${data.summary.successfulAttempts}`);
    console.log(`Failed: ${data.summary.failedAttempts}`);
    console.log(`Success Rate: ${data.summary.successRate}\n`);

    console.log('🌍 ENVIRONMENT BREAKDOWN');
    console.log('-'.repeat(30));
    console.log(`Production: ${data.environmentBreakdown.production}`);
    console.log(`Sandbox: ${data.environmentBreakdown.sandbox}`);
    console.log(`Unknown: ${data.environmentBreakdown.unknown}\n`);

    if (data.recentFailures && data.recentFailures.length > 0) {
      console.log('❌ RECENT FAILURES');
      console.log('-'.repeat(30));
      data.recentFailures.slice(0, 5).forEach((failure, index) => {
        console.log(`${index + 1}. ${failure.productId}`);
        console.log(`   Error: ${failure.error}`);
        console.log(`   Environment: ${failure.environment}`);
        console.log(`   Time: ${new Date(failure.timestamp).toLocaleString()}\n`);
      });
    }

    // Recommendations
    console.log('💡 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    if (data.status === 'NO_DATA') {
      console.log('⚠️  No validation attempts detected. This could mean:');
      console.log('   - No users are trying to purchase subscriptions');
      console.log('   - The app is not reaching the validation endpoint');
      console.log('   - There may be a configuration issue');
    } else if (data.status === 'CRITICAL') {
      console.log('🚨 CRITICAL: Success rate is below 80%');
      console.log('   - Check recent failures for common error patterns');
      console.log('   - Verify Apple Shared Secret is configured');
      console.log('   - Check network connectivity to Apple servers');
    } else if (data.status === 'WARNING') {
      console.log('⚠️  WARNING: Success rate is below 95%');
      console.log('   - Monitor for increasing failure patterns');
      console.log('   - Review recent failures for issues');
    } else if (data.status === 'GOOD') {
      console.log('✅ GOOD: Success rate is above 95%');
      console.log('   - System is performing well');
      console.log('   - Continue monitoring for any changes');
    } else if (data.status === 'EXCELLENT') {
      console.log('🎉 EXCELLENT: 100% success rate');
      console.log('   - All validations are working perfectly');
      console.log('   - The Apple receipt validation fix is working!');
    }

    console.log('\n📱 TO TEST THE FIX:');
    console.log('-'.repeat(30));
    console.log('1. Submit your app to Apple for review');
    console.log('2. Apple will test with both production and sandbox receipts');
    console.log('3. Monitor this dashboard during Apple review');
    console.log('4. Look for validation attempts with both environments');
    console.log('5. Ensure success rate remains high (>95%)');

  } catch (error) {
    console.error('❌ Error checking validation status:', error.message);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('1. Verify the Supabase URL and API key are correct');
    console.log('2. Check that the validation-monitoring function is deployed');
    console.log('3. Ensure you have internet connectivity');
  }
}

function getStatusEmoji(status) {
  switch (status) {
    case 'EXCELLENT': return '🎉';
    case 'GOOD': return '✅';
    case 'WARNING': return '⚠️';
    case 'CRITICAL': return '🚨';
    case 'NO_DATA': return '📭';
    default: return '❓';
  }
}

// Run the check
checkValidationStatus();

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Spacing, BorderRadius } from '../constants/DocAidDesign';
import { useLanguage } from '../contexts/LanguageContext';

interface PillCountDisplayProps {
  count: number | null;
  isEstimate: boolean;
}

export default function PillCountDisplay({ count, isEstimate }: PillCountDisplayProps) {
  const { t } = useLanguage();

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{t('totalCount')}:</Text>
      <Text style={styles.count}>{count !== null ? count.toString() : '0'}</Text>

      {isEstimate && (
        <View style={styles.estimateContainer}>
          <Text style={styles.estimateText}>{t('estimate')}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.docBlue.light,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    width: '100%',
    minHeight: 120,
  },
  label: {
    fontSize: 24,
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
    fontWeight: '500',
  },
  count: {
    fontSize: 72,
    fontWeight: '800',
    color: Colors.docPurple.DEFAULT,
    textAlign: 'center',
    marginVertical: Spacing.xs,
  },
  estimateContainer: {
    backgroundColor: Colors.warning,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.sm,
  },
  estimateText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '700',
    textTransform: 'uppercase',
  },

});

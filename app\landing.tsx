import React from 'react';
import { StyleSheet, View, ScrollView, Image, TouchableOpacity } from 'react-native';
import { Text, Card, Container, Header, Button, Divider } from '../components/ui';
import { Colors, Spacing } from '../constants/DesignSystem';
import { useLanguage } from '../contexts/LanguageContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

export default function LandingScreen() {
  const { t } = useLanguage();
  const router = useRouter();

  // Navigate to subscription page
  const goToSubscription = () => {
    router.push('/subscription');
  };

  // Navigate to login page
  const goToLogin = () => {
    router.push('/login');
  };

  // Navigate to home page
  const goToHome = () => {
    router.push('/');
  };

  return (
    <Container>
      <Header title={t('welcomeToPillLogic')} />

      <ScrollView style={styles.scrollContent} contentContainerStyle={styles.contentContainer}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Image
            source={require('../assets/images/icon.png')}
            style={styles.heroImage}
            resizeMode="contain"
          />
          <Text variant="h1" weight="bold" style={styles.heroTitle}>
            {t('pillLogic')}
          </Text>
          <Text variant="subtitle" color="secondary" style={styles.heroSubtitle}>
            {t('yourMedicationAssistant')}
          </Text>
          <Button
            title={t('getStarted')}
            variant="primary"
            size="lg"
            style={styles.heroButton}
            onPress={goToHome}
          />
        </View>

        <Divider style={styles.divider} />

        {/* Key Features Section */}
        <Text variant="h2" weight="bold" style={styles.sectionTitle}>
          {t('keyFeatures')}
        </Text>

        <Card style={styles.featureCard}>
          <View style={styles.featureIconContainer}>
            <Ionicons name="document-text-outline" size={32} color={Colors.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text variant="h3" weight="semibold" style={styles.featureTitle}>
              {t('noteAnalysis')}
            </Text>
            <Text variant="body" color="secondary" style={styles.featureDescription}>
              {t('noteAnalysisDescription')}
            </Text>
          </View>
        </Card>

        <Card style={styles.featureCard}>
          <View style={styles.featureIconContainer}>
            <Ionicons name="calculator-outline" size={32} color={Colors.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text variant="h3" weight="semibold" style={styles.featureTitle}>
              Pill Count
            </Text>
            <Text variant="body" color="secondary" style={styles.featureDescription}>
              {t('pillScanDescription')}
            </Text>
          </View>
        </Card>

        <Card style={styles.featureCard}>
          <View style={styles.featureIconContainer}>
            <Ionicons name="videocam-outline" size={32} color={Colors.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text variant="h3" weight="semibold" style={styles.featureTitle}>
              Live Pill Count
            </Text>
            <Text variant="body" color="secondary" style={styles.featureDescription}>
              {t('livePillScanDescription')}
            </Text>
          </View>
        </Card>

        <Card style={styles.featureCard}>
          <View style={styles.featureIconContainer}>
            <Ionicons name="language-outline" size={32} color={Colors.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text variant="h3" weight="semibold" style={styles.featureTitle}>
              {t('multiLanguageSupport')}
            </Text>
            <Text variant="body" color="secondary" style={styles.featureDescription}>
              {t('translateToFourLanguages')}
            </Text>
            <View style={styles.languageFlags}>
              <Text variant="caption" style={styles.languageTag}>🇺🇸 English</Text>
              <Text variant="caption" style={styles.languageTag}>🇻🇳 Vietnamese</Text>
              <Text variant="caption" style={styles.languageTag}>🇮🇳 Hindi</Text>
              <Text variant="caption" style={styles.languageTag}>🇨🇳 Chinese</Text>
            </View>
          </View>
        </Card>

        <Divider style={styles.divider} />

        {/* Subscription Plans */}
        <Text variant="h2" weight="bold" style={styles.sectionTitle}>
          {t('subscriptionPlans')}
        </Text>

        <View style={styles.plansContainer}>
          <Card style={[styles.planCard, styles.freePlanCard]}>
            <Text variant="h3" weight="semibold" style={styles.planTitle}>
              {t('freeTier')}
            </Text>
            <Text variant="h2" weight="bold" style={styles.planPrice}>
              {t('free')}
            </Text>
            <View style={styles.planFeatures}>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">Pill Count - 10/day</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="close-circle" size={20} color={Colors.error} style={styles.planFeatureIcon} />
                <Text variant="body">Live Pill Count</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">{t('noteAnalysis')} - 10/day</Text>
              </View>
            </View>
            <Button
              title={t('currentPlan')}
              variant="outline"
              size="medium"
              style={styles.planButton}
              disabled
            />
          </Card>

          <Card style={[styles.planCard, styles.proPlanCard]}>
            <Text variant="h3" weight="semibold" style={styles.planTitle}>
              {t('proTier')}
            </Text>
            <Text variant="h2" weight="bold" style={styles.planPrice}>
              $4.99
              <Text variant="caption" color="secondary">/mo</Text>
            </Text>
            <View style={styles.planFeatures}>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">{t('unlimited')} Pill Count</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">Live Pill Count - 5/day</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="close-circle" size={20} color={Colors.error} style={styles.planFeatureIcon} />
                <Text variant="body">{t('noteAnalysis')}</Text>
              </View>
            </View>
            <Button
              title={t('upgradeToPro')}
              variant="primary"
              size="medium"
              style={styles.planButton}
              onPress={goToSubscription}
            />
          </Card>

          <Card style={[styles.planCard, styles.premiumPlanCard]}>
            <View style={styles.recommendedBadge}>
              <Text variant="caption" color="white" style={styles.recommendedText}>
                {t('recommended')}
              </Text>
            </View>
            <Text variant="h3" weight="semibold" style={styles.planTitle}>
              {t('premiumTier')}
            </Text>
            <Text variant="h2" weight="bold" style={styles.planPrice}>
              $5.99
              <Text variant="caption" color="secondary">/mo</Text>
            </Text>
            <View style={styles.planFeatures}>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">{t('unlimited')} Pill Count</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">{t('unlimited')} Live Pill Count</Text>
              </View>
              <View style={styles.planFeatureItem}>
                <Ionicons name="checkmark-circle" size={20} color={Colors.success} style={styles.planFeatureIcon} />
                <Text variant="body">{t('unlimited')} {t('noteAnalysis')}</Text>
              </View>
            </View>
            <Button
              title={t('upgradeToPremium')}
              variant="primary"
              size="medium"
              style={[styles.planButton, styles.premiumButton]}
              onPress={goToSubscription}
            />
          </Card>
        </View>

        <TouchableOpacity style={styles.viewAllPlansButton} onPress={goToSubscription}>
          <Text variant="body" color="primary" weight="semibold">
            {t('viewAllPlans')} →
          </Text>
        </TouchableOpacity>

        <Divider style={styles.divider} />

        {/* Call to Action */}
        <View style={styles.ctaContainer}>
          <Text variant="h2" weight="bold" style={styles.ctaTitle}>
            {t('readyToStart')}
          </Text>
          <Text variant="body" color="secondary" style={styles.ctaDescription}>
            {t('readyToStartDescription')}
          </Text>
          <View style={styles.ctaButtons}>
            {/* Login button - Re-enabled for Google Sign-in */}
            <Button
              title={t('login')}
              variant="outline"
              size="lg"
              style={styles.ctaButton}
              onPress={goToLogin}
            />
            <Button
              title={t('getStarted')}
              variant="primary"
              size="lg"
              style={styles.ctaButton}
              onPress={goToHome}
            />
          </View>
        </View>
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: Spacing.xl,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  heroImage: {
    width: 120,
    height: 120,
    marginBottom: Spacing.md,
  },
  heroTitle: {
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  heroSubtitle: {
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  heroButton: {
    minWidth: 200,
  },
  divider: {
    marginVertical: Spacing.lg,
  },
  sectionTitle: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  featureCard: {
    flexDirection: 'row',
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  featureIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.backgroundLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    marginBottom: Spacing.xs,
  },
  featureDescription: {
    marginBottom: Spacing.xs,
  },
  languageFlags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: Spacing.xs,
  },
  languageTag: {
    backgroundColor: Colors.backgroundLight,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: 12,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  plansContainer: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  planCard: {
    padding: Spacing.md,
    marginBottom: Spacing.md,
    position: 'relative',
  },
  freePlanCard: {
    borderColor: Colors.border,
  },
  proPlanCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  premiumPlanCard: {
    borderColor: Colors.accent,
    borderWidth: 2,
  },
  recommendedBadge: {
    position: 'absolute',
    top: -10,
    right: Spacing.md,
    backgroundColor: Colors.accent,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs / 2,
    borderRadius: 12,
  },
  recommendedText: {
    fontSize: 10,
  },
  planTitle: {
    marginBottom: Spacing.xs,
  },
  planPrice: {
    marginBottom: Spacing.md,
  },
  planFeatures: {
    marginBottom: Spacing.md,
  },
  planFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  planFeatureIcon: {
    marginRight: Spacing.xs,
  },
  planButton: {
    width: '100%',
  },
  premiumButton: {
    backgroundColor: Colors.accent,
  },
  viewAllPlansButton: {
    alignSelf: 'center',
    marginBottom: Spacing.md,
  },
  ctaContainer: {
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },
  ctaTitle: {
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  ctaDescription: {
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  ctaButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  ctaButton: {
    minWidth: 140,
    marginHorizontal: Spacing.xs,
  },
});

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Colors, Spacing } from '../../constants/DesignSystem';

interface DividerProps {
  color?: string;
  thickness?: number;
  spacing?: number;
  style?: ViewStyle;
}

const Divider: React.FC<DividerProps> = ({
  color = Colors.border,
  thickness = 1,
  spacing = Spacing.md,
  style,
}) => {
  return (
    <View
      style={[
        styles.divider,
        {
          backgroundColor: color,
          height: thickness,
          marginVertical: spacing,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  divider: {
    width: '100%',
  },
});

export default Divider;

import OpenAI from 'openai';

// Use the same OpenAI API key from the vision service
import { OPENAI_API_KEY } from './openaiVisionService';

// Define supported languages
export enum Language {
  ENGLISH = 'en',
  VIETNAMESE = 'vi',
  HINDI = 'hi',
  CHINESE = 'zh'
}

// Language names for display
export const LanguageNames = {
  [Language.ENGLISH]: 'English',
  [Language.VIETNAMESE]: 'Vietnamese',
  [Language.HINDI]: 'Hindi',
  [Language.CHINESE]: 'Chinese'
};

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

// Simple in-memory cache for translations
interface TranslationCache {
  [key: string]: {
    [language: string]: string;
  };
}

const translationCache: TranslationCache = {};

/**
 * Translates text to the specified language
 * @param text - The text to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated text
 */
export const translateText = async (text: string, targetLanguage: Language): Promise<string> => {
  // If the target language is English, return the original text
  if (targetLanguage === Language.ENGLISH) {
    return text;
  }

  // Create a cache key based on the text and target language
  const cacheKey = text.substring(0, 100); // Use first 100 chars as key to avoid huge keys

  // Check if we have a cached translation
  if (translationCache[cacheKey] && translationCache[cacheKey][targetLanguage]) {
    console.log('Using cached translation');
    return translationCache[cacheKey][targetLanguage];
  }

  try {
    console.log(`Translating text to ${getLanguageName(targetLanguage)}`);

    // Use a more concise prompt to reduce token usage
    const systemPrompt = `Translate to ${getLanguageName(targetLanguage)}. Keep format and medical terms accurate. Translate labels.`;

    // Use OpenAI to translate the text
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: text }
        ],
        max_tokens: 1000,
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Translation API error: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    const translatedText = data.choices[0]?.message?.content || text;

    // Cache the translation
    if (!translationCache[cacheKey]) {
      translationCache[cacheKey] = {};
    }
    translationCache[cacheKey][targetLanguage] = translatedText;

    console.log('Translation result:', translatedText);
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    return text; // Return original text if translation fails
  }
};

/**
 * Translates medication information to the specified language
 * @param medications - The medication objects to translate
 * @param targetLanguage - The language to translate to
 * @returns The translated medication objects
 */
export const translateMedications = async (medications: any[], targetLanguage: Language): Promise<any[]> => {
  // If the target language is English, return the original medications
  if (targetLanguage === Language.ENGLISH || medications.length === 0) {
    return medications;
  }

  console.log('Translating medications to', targetLanguage);
  const translatedMedications = [];

  // Translate each medication individually - more reliable than batch JSON translation
  for (const med of medications) {
    try {
      // Create a simple prompt for each medication
      const medText = `
Medication: ${med.name}
Dosage: ${med.dosage}
Purpose: ${med.purpose}
`;

      console.log('Translating medication:', medText);

      // Translate the medication text
      const translatedText = await translateText(medText, targetLanguage);
      console.log('Translated medication text:', translatedText);

      // Parse the translated text - handle different languages
      // For English
      let nameMatch = translatedText.match(/Medication:?\s*([^\n]+)/);
      let dosageMatch = translatedText.match(/Dosage:?\s*([^\n]+)/);
      let purposeMatch = translatedText.match(/Purpose:?\s*([^\n]+)/);

      // For Vietnamese
      if (!nameMatch) nameMatch = translatedText.match(/Thuốc:?\s*([^\n]+)/);
      if (!dosageMatch) dosageMatch = translatedText.match(/Liều Lượng:?\s*([^\n]+)/);
      if (!purposeMatch) purposeMatch = translatedText.match(/Mục Đích:?\s*([^\n]+)/);

      // For Hindi
      if (!nameMatch) nameMatch = translatedText.match(/दवा:?\s*([^\n]+)/);
      if (!dosageMatch) dosageMatch = translatedText.match(/खुराक:?\s*([^\n]+)/);
      if (!purposeMatch) purposeMatch = translatedText.match(/उद्देश्य:?\s*([^\n]+)/);

      // For Chinese
      if (!nameMatch) nameMatch = translatedText.match(/药物:?\s*([^\n]+)/);
      if (!dosageMatch) dosageMatch = translatedText.match(/剂量:?\s*([^\n]+)/);
      if (!purposeMatch) purposeMatch = translatedText.match(/用途:?\s*([^\n]+)/);

      translatedMedications.push({
        name: nameMatch ? nameMatch[1].trim() : med.name,
        dosage: dosageMatch ? dosageMatch[1].trim() : med.dosage,
        purpose: purposeMatch ? purposeMatch[1].trim() : med.purpose,
        fillDate: med.fillDate, // Keep date format unchanged
      });
    } catch (error) {
      console.error('Error translating medication:', error);
      translatedMedications.push(med); // Use original if translation fails
    }
  }

  console.log('Translated medications:', translatedMedications);
  return translatedMedications;
};

/**
 * Gets the full name of a language
 * @param language - The language code
 * @returns The language name
 */
export const getLanguageName = (language: Language): string => {
  return LanguageNames[language] || 'Unknown';
};

/**
 * Translates UI text based on the language
 * @param key - The text key
 * @param language - The target language
 * @returns The translated text
 */
export const translateUI = (key: string, language: Language): string => {
  const translations: Record<string, Record<Language, string>> = {
    'medicationExtractor': {
      [Language.ENGLISH]: 'Medication Extractor',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षक',
      [Language.CHINESE]: '药物提取器'
    },
    'poweredBy': {
      [Language.ENGLISH]: 'Powered by OpenAI Vision',
      [Language.VIETNAMESE]: 'Được hỗ trợ bởi OpenAI Vision',
      [Language.HINDI]: 'OpenAI Vision द्वारा संचालित',
      [Language.CHINESE]: '由 OpenAI Vision 提供支持'
    },
    'extractedMedications': {
      [Language.ENGLISH]: 'Extracted Medications',
      [Language.VIETNAMESE]: 'Thuốc Đã Trích Xuất',
      [Language.HINDI]: 'निकाली गई दवाएं',
      [Language.CHINESE]: '提取的药物'
    },
    'showRawResponse': {
      [Language.ENGLISH]: 'Show Raw Response',
      [Language.VIETNAMESE]: 'Hiển Thị Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया दिखाएं',
      [Language.CHINESE]: '显示原始响应'
    },
    'hideRawResponse': {
      [Language.ENGLISH]: 'Hide Raw Response',
      [Language.VIETNAMESE]: 'Ẩn Phản Hồi Gốc',
      [Language.HINDI]: 'कच्ची प्रतिक्रिया छिपाएं',
      [Language.CHINESE]: '隐藏原始响应'
    },
    'rawApiResponse': {
      [Language.ENGLISH]: 'Raw API Response:',
      [Language.VIETNAMESE]: 'Phản Hồi API Gốc:',
      [Language.HINDI]: 'कच्ची API प्रतिक्रिया:',
      [Language.CHINESE]: '原始 API 响应:'
    },
    'noMedicationsFound': {
      [Language.ENGLISH]: 'No medications found in the image.',
      [Language.VIETNAMESE]: 'Không tìm thấy thuốc trong hình ảnh.',
      [Language.HINDI]: 'छवि में कोई दवा नहीं मिली।',
      [Language.CHINESE]: '图像中未找到药物。'
    },
    'note': {
      [Language.ENGLISH]: 'Note: This app extracts medication information from doctor notes. If you encounter any errors, please wait a few moments before trying again.',
      [Language.VIETNAMESE]: 'Lưu ý: Ứng dụng này trích xuất thông tin thuốc từ ghi chú của bác sĩ. Nếu bạn gặp lỗi, vui lòng đợi một lát trước khi thử lại.',
      [Language.HINDI]: 'नोट: यह ऐप डॉक्टर के नोट्स से दवा की जानकारी निकालती है। यदि आपको कोई त्रुटियां मिलती हैं, तो कृपया फिर से प्रयास करने से पहले कुछ क्षण प्रतीक्षा करें।',
      [Language.CHINESE]: '注意：此应用程序从医生笔记中提取药物信息。如果遇到任何错误，请稍等片刻再重试。'
    },
    'takeAnotherPhoto': {
      [Language.ENGLISH]: 'Take Another Photo',
      [Language.VIETNAMESE]: 'Chụp Ảnh Khác',
      [Language.HINDI]: 'एक और फोटो लें',
      [Language.CHINESE]: '拍摄另一张照片'
    },
    'analyzing': {
      [Language.ENGLISH]: 'Analyzing image...',
      [Language.VIETNAMESE]: 'Đang phân tích hình ảnh...',
      [Language.HINDI]: 'छवि का विश्लेषण कर रहा है...',
      [Language.CHINESE]: '正在分析图像...'
    },
    'medication': {
      [Language.ENGLISH]: 'Medication',
      [Language.VIETNAMESE]: 'Thuốc',
      [Language.HINDI]: 'दवा',
      [Language.CHINESE]: '药物'
    },
    'dosage': {
      [Language.ENGLISH]: 'Dosage',
      [Language.VIETNAMESE]: 'Liều Lượng',
      [Language.HINDI]: 'खुराक',
      [Language.CHINESE]: '剂量'
    },
    'purpose': {
      [Language.ENGLISH]: 'Purpose',
      [Language.VIETNAMESE]: 'Mục Đích',
      [Language.HINDI]: 'उद्देश्य',
      [Language.CHINESE]: '用途'
    },
    'fillDate': {
      [Language.ENGLISH]: 'Fill Date',
      [Language.VIETNAMESE]: 'Ngày Cấp',
      [Language.HINDI]: 'भरने की तारीख',
      [Language.CHINESE]: '配药日期'
    },
    'noMedicationInfo': {
      [Language.ENGLISH]: 'No medication information found.',
      [Language.VIETNAMESE]: 'Không tìm thấy thông tin thuốc.',
      [Language.HINDI]: 'कोई दवा जानकारी नहीं मिली।',
      [Language.CHINESE]: '未找到药物信息。'
    },
    'selectLanguage': {
      [Language.ENGLISH]: 'Select Language',
      [Language.VIETNAMESE]: 'Chọn Ngôn Ngữ',
      [Language.HINDI]: 'भाषा चुनें',
      [Language.CHINESE]: '选择语言'
    },
    'features': {
      [Language.ENGLISH]: 'Features',
      [Language.VIETNAMESE]: 'Tính Năng',
      [Language.HINDI]: 'विशेषताएँ',
      [Language.CHINESE]: '功能'
    },
    'cameraCapture': {
      [Language.ENGLISH]: 'Camera Capture',
      [Language.VIETNAMESE]: 'Chụp Ảnh',
      [Language.HINDI]: 'कैमरा कैप्चर',
      [Language.CHINESE]: '相机拍摄'
    },
    'takePhotos': {
      [Language.ENGLISH]: 'Take photos directly from the app',
      [Language.VIETNAMESE]: 'Chụp ảnh trực tiếp từ ứng dụng',
      [Language.HINDI]: 'ऐप से सीधे फोटो लें',
      [Language.CHINESE]: '直接从应用拍照'
    },
    'gallerySelection': {
      [Language.ENGLISH]: 'Gallery Selection',
      [Language.VIETNAMESE]: 'Chọn Từ Thư Viện',
      [Language.HINDI]: 'गैलरी चयन',
      [Language.CHINESE]: '图库选择'
    },
    'selectImages': {
      [Language.ENGLISH]: 'Select images from your gallery',
      [Language.VIETNAMESE]: 'Chọn hình ảnh từ thư viện của bạn',
      [Language.HINDI]: 'अपनी गैलरी से छवियां चुनें',
      [Language.CHINESE]: '从图库中选择图像'
    },
    'medicationExtraction': {
      [Language.ENGLISH]: 'Medication Extraction',
      [Language.VIETNAMESE]: 'Trích Xuất Thuốc',
      [Language.HINDI]: 'दवा निष्कर्षण',
      [Language.CHINESE]: '药物提取'
    },
    'extractDetails': {
      [Language.ENGLISH]: 'Extract medication details from doctor notes',
      [Language.VIETNAMESE]: 'Trích xuất chi tiết thuốc từ ghi chú của bác sĩ',
      [Language.HINDI]: 'डॉक्टर के नोट्स से दवा के विवरण निकालें',
      [Language.CHINESE]: '从医生笔记中提取药物详情'
    },
    'startUsingCamera': {
      [Language.ENGLISH]: 'Start Using Camera',
      [Language.VIETNAMESE]: 'Bắt Đầu Sử Dụng Máy Ảnh',
      [Language.HINDI]: 'कैमरा का उपयोग शुरू करें',
      [Language.CHINESE]: '开始使用相机'
    }
  };

  return translations[key]?.[language] || key;
};

// Export the API key for use in other services
export { OPENAI_API_KEY };

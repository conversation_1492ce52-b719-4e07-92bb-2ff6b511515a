import { Session } from '@supabase/supabase-js';
import { UserData } from './supabase';

/**
 * Authentication provider types
 */
export enum AuthProvider {
  EMAIL = 'email',
  GOOGLE = 'google',
  APPLE = 'apple',
  ANONYMOUS = 'anonymous',
  UNKNOWN = 'unknown'
}

/**
 * Detect the authentication provider used by the user
 * @param user - User data from Supabase
 * @param session - Session data from Supabase
 * @returns AuthProvider enum value
 */
export function getAuthProvider(user: UserData | null, session: Session | null): AuthProvider {
  if (!user || !session) {
    return AuthProvider.UNKNOWN;
  }

  // Check app_metadata.provider first (most reliable)
  const provider = session.user?.app_metadata?.provider;
  
  if (provider === 'google') {
    return AuthProvider.GOOGLE;
  }
  
  if (provider === 'apple') {
    return AuthProvider.APPLE;
  }
  
  if (provider === 'anonymous') {
    return AuthProvider.ANONYMOUS;
  }
  
  if (provider === 'email') {
    return AuthProvider.EMAIL;
  }

  // Fallback: check user_metadata for OAuth providers
  const userMetadata = session.user?.user_metadata;
  
  // Google users typically have these fields
  if (userMetadata?.provider === 'google' || 
      userMetadata?.iss === 'https://accounts.google.com' ||
      userMetadata?.email_verified === true && userMetadata?.provider_id) {
    return AuthProvider.GOOGLE;
  }
  
  // Apple users typically have these fields
  if (userMetadata?.provider === 'apple' || 
      userMetadata?.iss === 'https://appleid.apple.com' ||
      session.user?.identities?.some(identity => identity.provider === 'apple')) {
    return AuthProvider.APPLE;
  }

  // Check identities array as another fallback
  const identities = session.user?.identities;
  if (identities && identities.length > 0) {
    const primaryIdentity = identities[0];
    
    if (primaryIdentity.provider === 'google') {
      return AuthProvider.GOOGLE;
    }
    
    if (primaryIdentity.provider === 'apple') {
      return AuthProvider.APPLE;
    }
    
    if (primaryIdentity.provider === 'email') {
      return AuthProvider.EMAIL;
    }
  }

  // If we have an email and no OAuth indicators, assume email/password
  if (user.email && user.email !== '<EMAIL>') {
    return AuthProvider.EMAIL;
  }

  return AuthProvider.UNKNOWN;
}

/**
 * Check if user can reset password (only email/password users can)
 * @param user - User data from Supabase
 * @param session - Session data from Supabase
 * @returns boolean indicating if password reset is available
 */
export function canResetPassword(user: UserData | null, session: Session | null): boolean {
  const provider = getAuthProvider(user, session);
  return provider === AuthProvider.EMAIL;
}

/**
 * Check if user is using OAuth authentication (Google or Apple)
 * @param user - User data from Supabase
 * @param session - Session data from Supabase
 * @returns boolean indicating if user is using OAuth
 */
export function isOAuthUser(user: UserData | null, session: Session | null): boolean {
  const provider = getAuthProvider(user, session);
  return provider === AuthProvider.GOOGLE || provider === AuthProvider.APPLE;
}

/**
 * Get a human-readable provider name
 * @param provider - AuthProvider enum value
 * @returns string representation of the provider
 */
export function getProviderDisplayName(provider: AuthProvider): string {
  switch (provider) {
    case AuthProvider.EMAIL:
      return 'Email & Password';
    case AuthProvider.GOOGLE:
      return 'Google';
    case AuthProvider.APPLE:
      return 'Apple';
    case AuthProvider.ANONYMOUS:
      return 'Anonymous';
    default:
      return 'Unknown';
  }
}

/**
 * Debug function to log authentication provider information
 * @param user - User data from Supabase
 * @param session - Session data from Supabase
 */
export function debugAuthProvider(user: UserData | null, session: Session | null): void {
  if (!user || !session) {
    console.log('Auth Debug: No user or session');
    return;
  }

  const provider = getAuthProvider(user, session);
  
  console.log('Auth Debug:', {
    detectedProvider: provider,
    displayName: getProviderDisplayName(provider),
    canResetPassword: canResetPassword(user, session),
    isOAuth: isOAuthUser(user, session),
    appMetadataProvider: session.user?.app_metadata?.provider,
    userMetadataProvider: session.user?.user_metadata?.provider,
    identities: session.user?.identities?.map(i => ({ provider: i.provider, id: i.id })),
    email: user.email
  });
}

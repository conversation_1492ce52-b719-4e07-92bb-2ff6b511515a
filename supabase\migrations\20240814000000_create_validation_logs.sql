-- Create validation logs table for monitoring receipt validation
CREATE TABLE IF NOT EXISTS public.validation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id TEXT NOT NULL,
  transaction_id TEXT NOT NULL,
  success BOOLEAN NOT NULL,
  error_message TEXT,
  environment TEXT, -- 'production', 'sandbox', or 'unknown'
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_validation_logs_user_id ON public.validation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_validation_logs_created_at ON public.validation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_validation_logs_success ON public.validation_logs(success);
CREATE INDEX IF NOT EXISTS idx_validation_logs_environment ON public.validation_logs(environment);

-- Enable RLS
ALTER TABLE public.validation_logs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own validation logs" ON public.validation_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Service role can insert validation logs
CREATE POLICY "Service role can insert validation logs" ON public.validation_logs
  FOR INSERT WITH CHECK (true);

-- Service role can view all validation logs for monitoring
CREATE POLICY "Service role can view all validation logs" ON public.validation_logs
  FOR SELECT USING (auth.role() = 'service_role');

-- Grant permissions
GRANT SELECT, INSERT ON public.validation_logs TO authenticated;
GRANT ALL ON public.validation_logs TO service_role;

-- Function to get hourly validation statistics
CREATE OR REPLACE FUNCTION get_hourly_validation_stats(hours_back INTEGER DEFAULT 24)
RETURNS TABLE (
  hour_start TIMESTAMPTZ,
  total_attempts BIGINT,
  successful_attempts BIGINT,
  failed_attempts BIGINT,
  success_rate NUMERIC,
  production_attempts BIGINT,
  sandbox_attempts BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    date_trunc('hour', vl.created_at) as hour_start,
    COUNT(*) as total_attempts,
    COUNT(*) FILTER (WHERE vl.success = true) as successful_attempts,
    COUNT(*) FILTER (WHERE vl.success = false) as failed_attempts,
    ROUND(
      (COUNT(*) FILTER (WHERE vl.success = true)::NUMERIC / COUNT(*)::NUMERIC) * 100,
      2
    ) as success_rate,
    COUNT(*) FILTER (WHERE vl.environment = 'production') as production_attempts,
    COUNT(*) FILTER (WHERE vl.environment = 'sandbox') as sandbox_attempts
  FROM public.validation_logs vl
  WHERE vl.created_at >= NOW() - (hours_back || ' hours')::INTERVAL
  GROUP BY date_trunc('hour', vl.created_at)
  ORDER BY hour_start DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

/**
 * <PERSON><PERSON><PERSON> to test the usage limits functionality
 * 
 * Usage:
 * 1. Make sure you're logged in to your app
 * 2. Run: node scripts/test-usage-limits.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for user input
const prompt = (question) => new Promise((resolve) => rl.question(question, resolve));

// Main test function
async function testUsageLimits() {
  try {
    console.log('🔍 Testing usage limits functionality...');
    
    // Check if user is logged in
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.error('❌ You need to be logged in to test usage limits');
      console.log('Please log in to the app first and then run this script again');
      return;
    }
    
    const userId = session.user.id;
    console.log(`✅ Logged in as user: ${userId}`);
    
    // Get user profile to check subscription tier
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();
      
    if (profileError) {
      console.error('❌ Error fetching user profile:', profileError.message);
      return;
    }
    
    console.log(`📊 Subscription tier: ${profile.subscription_tier || 'free'}`);
    
    // Test can_use_feature function
    const featureType = await prompt('Enter feature type to test (pill_scan or live_pill_scan): ');
    
    const { data: canUse, error: canUseError } = await supabase.rpc('can_use_feature', {
      user_uuid: userId,
      feature: featureType
    });
    
    if (canUseError) {
      console.error(`❌ Error checking if user can use feature ${featureType}:`, canUseError.message);
      return;
    }
    
    console.log(`✅ Can use ${featureType}: ${canUse}`);
    
    // Get current usage
    const { data: usageData, error: usageError } = await supabase
      .from('daily_feature_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('feature_type', featureType)
      .eq('usage_date', new Date().toISOString().split('T')[0]);
      
    if (usageError) {
      console.error('❌ Error fetching usage data:', usageError.message);
    } else {
      console.log(`📊 Current usage for ${featureType}:`, usageData.length > 0 ? usageData[0].usage_count : 0);
    }
    
    // Ask if user wants to track usage
    const trackUsage = await prompt('Do you want to track usage for this feature? (yes/no): ');
    
    if (trackUsage.toLowerCase() === 'yes') {
      const { data: tracked, error: trackError } = await supabase.rpc('track_daily_feature_usage', {
        user_uuid: userId,
        feature: featureType
      });
      
      if (trackError) {
        console.error(`❌ Error tracking feature usage for ${featureType}:`, trackError.message);
        return;
      }
      
      console.log(`✅ Usage tracked successfully. Result: ${tracked}`);
      
      // Get updated usage
      const { data: updatedUsage, error: updatedUsageError } = await supabase
        .from('daily_feature_usage')
        .select('*')
        .eq('user_id', userId)
        .eq('feature_type', featureType)
        .eq('usage_date', new Date().toISOString().split('T')[0]);
        
      if (updatedUsageError) {
        console.error('❌ Error fetching updated usage data:', updatedUsageError.message);
      } else {
        console.log(`📊 Updated usage for ${featureType}:`, updatedUsage.length > 0 ? updatedUsage[0].usage_count : 0);
      }
    }
    
    // Test live session if selected
    if (featureType === 'live_pill_scan') {
      const testLiveSession = await prompt('Do you want to test live session functionality? (yes/no): ');
      
      if (testLiveSession.toLowerCase() === 'yes') {
        // Start a live session
        const { data: sessionId, error: sessionError } = await supabase.rpc('start_live_session', {
          user_uuid: userId
        });
        
        if (sessionError) {
          console.error('❌ Error starting live session:', sessionError.message);
          return;
        }
        
        if (!sessionId) {
          console.log('⚠️ Could not start live session. You may have reached your daily limit.');
          return;
        }
        
        console.log(`✅ Live session started with ID: ${sessionId}`);
        
        // Get active sessions
        const { data: activeSessions, error: activeSessionsError } = await supabase
          .from('live_session_tracking')
          .select('*')
          .eq('user_id', userId)
          .eq('is_active', true);
          
        if (activeSessionsError) {
          console.error('❌ Error fetching active sessions:', activeSessionsError.message);
        } else {
          console.log(`📊 Active sessions: ${activeSessions.length}`);
          console.log(activeSessions);
        }
        
        // Ask if user wants to end the session
        const endSession = await prompt('Do you want to end the live session? (yes/no): ');
        
        if (endSession.toLowerCase() === 'yes') {
          const { data: ended, error: endError } = await supabase.rpc('end_live_session', {
            session_uuid: sessionId
          });
          
          if (endError) {
            console.error('❌ Error ending live session:', endError.message);
            return;
          }
          
          console.log(`✅ Live session ended. Result: ${ended}`);
          
          // Get session details
          const { data: sessionDetails, error: sessionDetailsError } = await supabase
            .from('live_session_tracking')
            .select('*')
            .eq('id', sessionId)
            .single();
            
          if (sessionDetailsError) {
            console.error('❌ Error fetching session details:', sessionDetailsError.message);
          } else {
            console.log('📊 Session details:');
            console.log(`- Duration: ${sessionDetails.duration_seconds} seconds`);
            console.log(`- Start: ${new Date(sessionDetails.session_start).toLocaleString()}`);
            console.log(`- End: ${new Date(sessionDetails.session_end).toLocaleString()}`);
          }
        }
      }
    }
    
    console.log('✅ Test completed successfully!');
  } catch (error) {
    console.error('❌ An error occurred during testing:', error);
  } finally {
    rl.close();
  }
}

// Run the test
testUsageLimits();

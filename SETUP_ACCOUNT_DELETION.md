# Setup Account Deletion - Quick Fix

The account deletion feature is implemented but needs the database migration to be applied. Here's how to fix it:

## Option 1: Apply Migration via Supabase Dashboard (Recommended)

1. **Go to your Supabase Dashboard**
   - Open https://supabase.com/dashboard
   - Select your PillLogic project

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Run the Migration**
   - Copy the entire content from `MANUAL_ACCOUNT_DELETION_SETUP.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute

4. **Verify Installation**
   - The last query in the script will show if the function was created successfully
   - You should see one row returned with `delete_user_account` function details

## Option 2: Install Supabase CLI and Apply Migration

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project (you'll need your project reference)
supabase link --project-ref YOUR_PROJECT_REF

# Apply the migration
supabase db push
```

## Option 3: Manual Database Setup (If above don't work)

If you can't access the SQL Editor, you can manually create just the essential function:

```sql
CREATE OR REPLACE FUNCTION public.delete_user_account(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Delete user data in correct order
  DELETE FROM public.live_session_tracking WHERE user_id = user_uuid;
  DELETE FROM public.daily_feature_usage WHERE user_id = user_uuid;
  DELETE FROM public.feature_usage WHERE user_id = user_uuid;
  DELETE FROM public.profiles WHERE id = user_uuid;
  DELETE FROM auth.users WHERE id = user_uuid;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION public.delete_user_account(UUID) TO authenticated;
```

## Testing

After applying the migration:

1. **Test the function exists**:
   ```sql
   SELECT routine_name FROM information_schema.routines 
   WHERE routine_schema = 'public' AND routine_name = 'delete_user_account';
   ```

2. **Test account deletion in app**:
   - Create a test account
   - Go to Account Settings
   - Try deleting the account
   - Should work without the PGRST202 error

## Current Status

- ✅ UI is implemented and working
- ✅ Client-side code is ready
- ❌ Database migration needs to be applied
- ⏳ Apple token revocation function (optional, can be added later)

## Quick Fix Summary

**The main issue**: The database function `delete_user_account` doesn't exist yet.

**The solution**: Run the SQL script in `MANUAL_ACCOUNT_DELETION_SETUP.sql` in your Supabase SQL Editor.

**Time needed**: 2-3 minutes to copy/paste and run the SQL script.

Once you apply the migration, the account deletion feature will work immediately!

import React from 'react';
import { Platform } from 'react-native';
import CameraView from './CameraView';
import CameraViewWeb from './CameraViewWeb';

type CameraMode = 'prescription' | 'pills';

interface PlatformCameraProps {
  onImageCaptured: (imageUri: string) => void;
  cameraMode?: CameraMode;
}

export default function PlatformCamera({ onImageCaptured, cameraMode = 'prescription' }: PlatformCameraProps) {
  // Use the web version on web platform, and the native version on mobile platforms
  if (Platform.OS === 'web') {
    return <CameraViewWeb onImageCaptured={onImageCaptured} cameraMode={cameraMode} />;
  }
  
  return <CameraView onImageCaptured={onImageCaptured} cameraMode={cameraMode} />;
}

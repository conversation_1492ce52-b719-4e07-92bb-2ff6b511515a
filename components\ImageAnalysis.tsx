import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Image, Text, TouchableOpacity, ActivityIndicator, ScrollView, TextInput, Modal, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { analyzeImage } from '../services/openaiVisionService';
import MedicationTable, { Medication } from './MedicationTable';
import { parseMedicationInfo, getSimulatedMedications } from '../utils/medicationParser';
import { useLanguage } from '../contexts/LanguageContext';
import { translateText, translateMedications } from '../services/googleTranslateService';
import { Language } from '../services/languageTypes';
import LanguageSelector from './LanguageSelector';
import { saveMedicationAnalysis } from '../services/storageService';
import { useRouter } from 'expo-router';
import { useUsageLimits } from '../hooks/useUsageLimits';
import { FeatureType } from '../lib/supabase';

interface ImageAnalysisProps {
  imageUri: string;
  onRetake: () => void;
}

export default function ImageAnalysis({ imageUri, onRetake }: ImageAnalysisProps) {
  const { language, t } = useLanguage();
  const router = useRouter();
  const { checkFeatureAccess, trackUsage } = useUsageLimits();
  const [analysis, setAnalysis] = useState<string>('');
  const [translatedAnalysis, setTranslatedAnalysis] = useState<string>('');
  const [medications, setMedications] = useState<Medication[]>([]);
  const [translatedMedications, setTranslatedMedications] = useState<Medication[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showRawResponse, setShowRawResponse] = useState<boolean>(false);
  const [saved, setSaved] = useState<boolean>(false);
  const [showNameModal, setShowNameModal] = useState<boolean>(false);
  const [analysisName, setAnalysisName] = useState<string>('');

  // Handle saving the medication analysis
  const handleSave = async () => {
    if (saved) {
      // If already saved, navigate to summary
      router.push('/summary');
      return;
    }

    // Show the naming modal
    setShowNameModal(true);
  };

  // Handle the actual saving after naming
  const handleConfirmSave = async () => {
    setShowNameModal(false);
    setIsSaving(true);
    try {
      // Generate a default name if none provided
      const name = analysisName.trim() ||
        (medications.length > 0 ?
          `${medications[0].name} + ${medications.length - 1} more` :
          `Analysis ${new Date().toLocaleDateString()}`);

      // Save the medications to storage with the name
      await saveMedicationAnalysis(medications, name, imageUri);
      setSaved(true);
      // Reset the name for next time
      setAnalysisName('');
    } catch (error) {
      console.error('Error saving medication analysis:', error);
      setError(`Failed to save analysis: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle toggling raw response visibility
  const handleToggleRawResponse = async () => {
    // If we're about to show the raw response and we're not in English
    if (!showRawResponse && language !== Language.ENGLISH && translatedAnalysis === analysis) {
      // We need to translate the raw response first
      setIsTranslating(true);
      try {
        const translated = await translateText(analysis, language);
        setTranslatedAnalysis(translated);
      } catch (error) {
        console.error('Error translating raw response:', error);
      } finally {
        setIsTranslating(false);
      }
    }
    // Toggle the visibility
    setShowRawResponse(!showRawResponse);
  };

  useEffect(() => {
    const performAnalysis = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if user can use the note analysis feature
        const canUse = await checkFeatureAccess(FeatureType.NOTE_ANALYSIS);
        if (!canUse) {
          setIsLoading(false);
          return;
        }

        // Track usage of the note analysis feature
        await trackUsage(FeatureType.NOTE_ANALYSIS);

        // Call the OpenAI Vision API to analyze the image
        const result = await analyzeImage(imageUri);
        setAnalysis(result);

        // Parse the medication information from the response
        const parsedMedications = parseMedicationInfo(result);
        setMedications(parsedMedications);

        // Set content based on language
        if (language === Language.ENGLISH) {
          // For English, use the original content
          setTranslatedAnalysis(result);
          setTranslatedMedications(parsedMedications);
        } else {
          // For other languages, translate immediately
          console.log(`Initial translation to ${language}...`);
          setIsTranslating(true);
          try {
            // Only translate what's needed initially
            // Don't translate the raw analysis text yet unless it's visible
            if (showRawResponse) {
              const translated = await translateText(result, language);
              setTranslatedAnalysis(translated);
            } else {
              // Just set it to the original for now, will translate if user shows raw response
              setTranslatedAnalysis(result);
            }

            // Always translate the medications for the table
            const translatedMeds = await translateMedications(parsedMedications, language);
            console.log('Initial translation complete');
            setTranslatedMedications(translatedMeds);
          } catch (translationError) {
            console.error('Initial translation error:', translationError);
            // Fallback to English if translation fails
            setTranslatedAnalysis(result);
            setTranslatedMedications(parsedMedications);
          } finally {
            setIsTranslating(false);
          }
        }
      } catch (err) {
        console.error('Error during image analysis:', err);
        setError(`Failed to analyze image: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setIsLoading(false);
      }
    };

    if (imageUri) {
      performAnalysis();
    }
  }, [imageUri]);

  // Update translations when language changes
  useEffect(() => {
    const updateTranslations = async () => {
      if (!analysis) return;

      console.log('Language changed to', language, '- translating content');

      // For English, use the original content
      if (language === Language.ENGLISH) {
        setTranslatedAnalysis(analysis);
        setTranslatedMedications(medications);
        return;
      }

      // For other languages, translate immediately
      setIsTranslating(true);
      try {
        // Only translate what's visible to the user
        // If raw response is not shown, don't translate it yet
        let translatedText = analysis;
        if (showRawResponse) {
          console.log(`Translating analysis to ${language}...`);
          translatedText = await translateText(analysis, language);
        }
        setTranslatedAnalysis(translatedText);

        // Always translate the medications for the table
        console.log(`Translating medications to ${language}...`);
        const translatedMeds = await translateMedications(medications, language);
        console.log('Translation complete:', translatedMeds);
        setTranslatedMedications(translatedMeds);
      } catch (translationError) {
        console.error('Translation error:', translationError);
        // Fallback to English if translation fails
        setTranslatedAnalysis(analysis);
        setTranslatedMedications(medications);
      } finally {
        setIsTranslating(false);
      }
    };

    // Run translation immediately when language changes
    updateTranslations();
  }, [language, analysis, medications, showRawResponse]);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Naming Modal */}
      <Modal
        visible={showNameModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowNameModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{t('nameYourAnalysis')}</Text>
            <TextInput
              style={styles.nameInput}
              placeholder={t('enterAnalysisName')}
              value={analysisName}
              onChangeText={setAnalysisName}
              autoFocus
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowNameModal(false)}
              >
                <Text style={styles.modalButtonText}>{t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.saveButton]}
                onPress={handleConfirmSave}
              >
                <Text style={[styles.modalButtonText, styles.saveButtonText]}>{t('save')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} resizeMode="contain" />
      </View>

      <View style={styles.analysisContainer}>
        <Text style={styles.title}>{t('medicationExtractor')}</Text>
        <Text style={styles.subtitle}>{t('poweredBy')}</Text>

        <LanguageSelector style={styles.languageSelector} />

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2f95dc" />
            <Text style={styles.loadingText}>{t('analyzing')}</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <FontAwesome name="exclamation-circle" size={24} color="red" />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : (
          <View style={styles.resultContainer}>
            {isTranslating ? (
              <View style={styles.translatingContainer}>
                <ActivityIndicator size="large" color="#2f95dc" />
                <Text style={styles.translatingText}>{t('translating')}</Text>
              </View>
            ) : translatedMedications.length > 0 ? (
              <>
                <Text style={styles.sectionTitle}>{t('extractedMedications')}</Text>
                <MedicationTable medications={translatedMedications} />
                <TouchableOpacity
                  style={styles.toggleButton}
                  onPress={handleToggleRawResponse}
                >
                  <Text style={styles.toggleButtonText}>
                    {showRawResponse ? t('hideRawResponse') : t('showRawResponse')}
                  </Text>
                </TouchableOpacity>
                {showRawResponse && (
                  <View style={styles.rawResponseContainer}>
                    <Text style={styles.rawResponseTitle}>{t('rawApiResponse')}</Text>
                    <Text style={styles.analysisText}>{translatedAnalysis}</Text>
                  </View>
                )}
              </>
            ) : (
              <>
                <Text style={styles.noMedicationsText}>{t('noMedicationsFound')}</Text>
                <Text style={styles.analysisText}>{translatedAnalysis}</Text>
              </>
            )}
            <View style={styles.noteContainer}>
              <Text style={styles.noteText}>
                {t('note')}
              </Text>
            </View>
          </View>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={onRetake}>
          <FontAwesome name="camera" size={20} color="white" style={styles.buttonIcon} />
          <Text style={styles.buttonText}>{t('takeAnotherPhoto')}</Text>
        </TouchableOpacity>

        {!isLoading && !error && medications.length > 0 && (
          <TouchableOpacity
            style={[styles.saveButton, saved && styles.savedButton]}
            onPress={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <FontAwesome
                  name={saved ? "check" : "save"}
                  size={20}
                  color="white"
                  style={styles.buttonIcon}
                />
                <Text style={styles.buttonText}>
                  {saved ? t('viewInSummary') : t('saveAnalysis')}
                </Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  contentContainer: {
    padding: 20,
  },
  imageContainer: {
    width: '100%',
    height: 300,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#e0e0e0',
    marginBottom: 20,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  analysisContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    textAlign: 'center',
  },
  languageSelector: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  translatingContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginVertical: 10,
  },
  translatingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#2f95dc',
    fontWeight: '600',
  },
  errorContainer: {
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
  },
  resultContainer: {
    padding: 10,
  },
  analysisText: {
    fontSize: 14,
    lineHeight: 22,
    color: '#333',
    marginBottom: 20,
  },
  noMedicationsText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  toggleButton: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginVertical: 10,
  },
  toggleButtonText: {
    color: '#2f95dc',
    fontSize: 14,
    fontWeight: '600',
  },
  rawResponseContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#eee',
  },
  rawResponseTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#666',
  },
  noteContainer: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    borderLeftWidth: 3,
    borderLeftColor: '#2f95dc',
    marginTop: 15,
  },
  noteText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  button: {
    flexDirection: 'row',
    backgroundColor: '#2f95dc',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flexDirection: 'row',
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  savedButton: {
    backgroundColor: '#8BC34A',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  nameInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 20,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2f95dc',
  },
  saveButtonText: {
    color: 'white',
  },
});

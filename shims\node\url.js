// Minimal implementation of the Node.js url module for React Native

function parse(urlStr, parseQueryString, slashesDenoteHost) {
  try {
    const url = new URL(urlStr);
    const result = {
      protocol: url.protocol,
      slashes: true,
      auth: null,
      host: url.host,
      port: url.port || null,
      hostname: url.hostname,
      hash: url.hash || null,
      search: url.search || null,
      query: url.search ? url.search.substring(1) : null,
      pathname: url.pathname,
      path: url.pathname + (url.search || ''),
      href: url.href
    };
    
    if (parseQueryString && result.query) {
      result.query = result.query.split('&').reduce((acc, pair) => {
        const [key, value] = pair.split('=');
        acc[key] = value || '';
        return acc;
      }, {});
    }
    
    return result;
  } catch (e) {
    return {};
  }
}

function format(urlObj) {
  if (typeof urlObj === 'string') return urlObj;
  
  let result = '';
  
  if (urlObj.protocol) {
    result += urlObj.protocol;
    if (result.substr(-1) !== ':') result += ':';
    result += '//';
  }
  
  if (urlObj.auth) {
    result += urlObj.auth + '@';
  }
  
  if (urlObj.host) {
    result += urlObj.host;
  } else {
    if (urlObj.hostname) {
      result += urlObj.hostname;
    }
    if (urlObj.port) {
      result += ':' + urlObj.port;
    }
  }
  
  if (urlObj.pathname) {
    result += urlObj.pathname;
  }
  
  if (urlObj.search) {
    result += urlObj.search;
  } else if (urlObj.query) {
    if (typeof urlObj.query === 'object') {
      const queryStr = Object.keys(urlObj.query)
        .map(key => `${key}=${urlObj.query[key]}`)
        .join('&');
      if (queryStr) {
        result += '?' + queryStr;
      }
    } else {
      result += '?' + urlObj.query;
    }
  }
  
  if (urlObj.hash) {
    result += urlObj.hash;
  }
  
  return result;
}

function resolve(from, to) {
  try {
    return new URL(to, from).href;
  } catch (e) {
    return to;
  }
}

class URLSearchParams {
  constructor(init) {
    this._params = new Map();
    
    if (typeof init === 'string') {
      init.split('&').forEach(pair => {
        if (pair.length === 0) return;
        const [key, value] = pair.split('=');
        this.append(key, value || '');
      });
    } else if (init instanceof URLSearchParams) {
      init.forEach((value, key) => {
        this.append(key, value);
      });
    } else if (init && typeof init === 'object') {
      Object.keys(init).forEach(key => {
        this.append(key, init[key]);
      });
    }
  }
  
  append(name, value) {
    if (!this._params.has(name)) {
      this._params.set(name, []);
    }
    this._params.get(name).push(String(value));
  }
  
  delete(name) {
    this._params.delete(name);
  }
  
  get(name) {
    return this._params.has(name) ? this._params.get(name)[0] : null;
  }
  
  getAll(name) {
    return this._params.has(name) ? [...this._params.get(name)] : [];
  }
  
  has(name) {
    return this._params.has(name);
  }
  
  set(name, value) {
    this._params.set(name, [String(value)]);
  }
  
  sort() {
    const entries = Array.from(this._params.entries());
    entries.sort((a, b) => a[0].localeCompare(b[0]));
    this._params = new Map(entries);
  }
  
  toString() {
    let result = '';
    this._params.forEach((values, name) => {
      values.forEach(value => {
        if (result.length > 0) result += '&';
        result += `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
      });
    });
    return result;
  }
  
  forEach(callback, thisArg) {
    this._params.forEach((values, name) => {
      values.forEach(value => {
        callback.call(thisArg, value, name, this);
      });
    });
  }
}

class URL {
  constructor(url, base) {
    let fullUrl;
    if (base) {
      fullUrl = resolve(base, url);
    } else {
      fullUrl = url;
    }
    
    const parsed = parse(fullUrl);
    
    this.href = parsed.href || '';
    this.protocol = parsed.protocol || '';
    this.host = parsed.host || '';
    this.hostname = parsed.hostname || '';
    this.port = parsed.port || '';
    this.pathname = parsed.pathname || '';
    this.search = parsed.search || '';
    this.hash = parsed.hash || '';
    this.origin = this.protocol + '//' + this.host;
    this.searchParams = new URLSearchParams(this.search.substring(1));
  }
  
  toString() {
    return this.href;
  }
  
  toJSON() {
    return this.href;
  }
}

module.exports = {
  parse,
  format,
  resolve,
  URL,
  URLSearchParams,
  // Legacy API
  Url: function() {},
  domainToASCII: function(domain) { return domain; },
  domainToUnicode: function(domain) { return domain; },
  fileURLToPath: function(url) { return url.replace(/^file:\/\//, ''); },
  pathToFileURL: function(path) { return 'file://' + path; }
};

# Price Change Checklist: Pro Plan $2.99 → $4.99

## ✅ **Code Changes (COMPLETED)**

### 1. **services/appleIAPService.ts**
- ✅ Updated `IAP_PRODUCTS` price from `'$2.99/month'` to `'$4.99/month'`

### 2. **app/subscription.tsx**
- ✅ Updated fallback price display from `'$2.99/month'` to `'$4.99/month'`

### 3. **Documentation Files**
- ✅ Updated `REVENUECAT_SETUP_GUIDE.md`
- ✅ Updated `APPLE_IAP_SETUP_GUIDE.md`

### 4. **app/landing.tsx**
- ✅ Already shows `$4.99` (no change needed)

---

## 🚨 **CRITICAL: External Platform Changes Required**

### **A. Apple App Store Connect** (REQUIRED)
1. **Go to App Store Connect** → Your App → Features → In-App Purchases
2. **Find Product**: `com.pilllogic.app.pro.monthly.v2` (Updated Product ID)
3. **Update Price**: Change from $2.99 to $4.99 USD
4. **Save Changes** and wait for Apple approval (can take 24-48 hours)

**⚠️ IMPORTANT**: Price changes in App Store Connect require Apple review and approval.

### **B. RevenueCat Dashboard** (REQUIRED)
1. **Go to RevenueCat Dashboard** → Products
2. **Find**: `com.pilllogic.app.pro.monthly.v2` (Updated Product ID)
3. **Update Price**: Change to $4.99
4. **Save Changes**

**Note**: RevenueCat automatically syncs with App Store Connect, but manual update ensures consistency.

### **C. Supabase Database** (NO ACTION NEEDED)
- ✅ Database stores subscription status, not prices
- ✅ Prices are fetched dynamically from App Store/RevenueCat
- ✅ No database changes required

---

## 📋 **Implementation Timeline**

### **Phase 1: Code Changes (COMPLETED)**
- ✅ Update all hardcoded price references
- ✅ Update documentation

### **Phase 2: Platform Updates (REQUIRED BEFORE DEPLOYMENT)**
1. **Update App Store Connect pricing** (24-48 hour approval time)
2. **Update RevenueCat dashboard**
3. **Wait for Apple approval**

### **Phase 3: Deployment**
1. **Build and deploy** to TestFlight/App Store
2. **Test subscription purchase** with new price
3. **Verify price display** in app

---

## 🔍 **Testing Checklist**

### **Before Deployment:**
- [ ] Verify App Store Connect shows $4.99 for Pro plan
- [ ] Verify RevenueCat dashboard shows $4.99 for Pro plan
- [ ] Confirm Apple has approved the price change

### **After Deployment:**
- [ ] Test subscription purchase shows $4.99 price
- [ ] Verify fallback price displays correctly if App Store unavailable
- [ ] Check that existing Pro subscribers are not affected
- [ ] Confirm new subscribers are charged $4.99

---

## ⚠️ **Important Notes**

### **Existing Subscribers:**
- Existing Pro subscribers will continue paying $2.99 until they cancel and resubscribe
- Price changes only affect NEW subscriptions
- Apple handles grandfathering automatically

### **Price Change Approval:**
- Apple must approve price increases
- Approval typically takes 24-48 hours
- Price changes are effective immediately after approval

### **Rollback Plan:**
- If issues occur, you can revert the price in App Store Connect
- Code changes can be reverted and redeployed
- RevenueCat changes are immediate

---

## 🚀 **Next Steps**

1. **IMMEDIATELY**: Update App Store Connect pricing to $4.99
2. **IMMEDIATELY**: Update RevenueCat dashboard pricing
3. **WAIT**: For Apple approval (24-48 hours)
4. **THEN**: Deploy the code changes
5. **TEST**: Subscription purchase flow with new pricing

**⚠️ DO NOT DEPLOY CODE CHANGES UNTIL APPLE APPROVES THE PRICE CHANGE**

Otherwise, the app will show $4.99 but Apple will charge $2.99, creating customer confusion.

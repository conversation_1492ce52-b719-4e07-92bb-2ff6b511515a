-- Fix the ambiguous column reference in end_live_session function
CREATE OR REPLACE FUNCTION public.end_live_session(
  session_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  session_start_time TIMESTAMP WITH TIME ZONE;
  user_id UUID;
  is_admin BOOLEAN;
  max_duration_seconds INTEGER := 300; -- 5 minutes (300 seconds)
  current_duration_seconds INTEGER;
BEGIN
  -- Get session start time and user ID
  SELECT lst.session_start, lst.user_id INTO session_start_time, user_id
  FROM public.live_session_tracking lst
  WHERE lst.id = session_uuid;
  
  -- Check if user is admin
  SELECT (subscription_tier = 'admin') INTO is_admin
  FROM public.profiles
  WHERE id = user_id;
  
  -- Calculate current duration
  current_duration_seconds := EXTRACT(EPOCH FROM (NOW() - session_start_time));
  
  -- If session exceeds max duration and user is not admin, cap the duration
  IF current_duration_seconds > max_duration_seconds AND NOT is_admin THEN
    current_duration_seconds := max_duration_seconds;
  END IF;

  -- Update session
  UPDATE public.live_session_tracking
  SET
    session_end = NOW(),
    is_active = FALSE,
    duration_seconds = current_duration_seconds
  WHERE id = session_uuid;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add a comment to explain the purpose of this migration
COMMENT ON FUNCTION public.end_live_session IS 'Ends a live pill count session and enforces the 5-minute time limit for non-admin users. Fixed ambiguous column reference.';

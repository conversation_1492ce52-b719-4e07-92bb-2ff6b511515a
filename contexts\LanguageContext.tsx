import React, { createContext, useState, useContext, ReactNode } from 'react';
import { translateUI } from '../services/googleTranslateService';
import { Language } from '../services/languageTypes';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

const LanguageContext = createContext<LanguageContextType>({
  language: Language.ENGLISH,
  setLanguage: () => {},
  t: (key: string, params?: Record<string, any>) => key,
});

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(Language.ENGLISH);

  // Translation function
  const t = (key: string, params?: Record<string, any>): string => {
    return translateUI(key, language, params);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PillLogic - Apple Receipt Validation Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f7;
            color: #1d1d1f;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-excellent { border-left: 5px solid #34c759; }
        .status-good { border-left: 5px solid #30d158; }
        .status-warning { border-left: 5px solid #ff9500; }
        .status-critical { border-left: 5px solid #ff3b30; }
        .status-no-data { border-left: 5px solid #8e8e93; }
        .metric {
            display: inline-block;
            margin: 10px 20px 10px 0;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        .metric-label {
            font-size: 0.9em;
            color: #6e6e73;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .refresh-btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #0056cc;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .error {
            background: #ff3b30;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .failure-item {
            background: #f8f8f8;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            border-left: 3px solid #ff3b30;
        }
        .timestamp {
            font-size: 0.8em;
            color: #6e6e73;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Apple Receipt Validation Dashboard</h1>
            <p>Monitor the status of Apple receipt validation fixes</p>
            <button class="refresh-btn" onclick="loadData()">🔄 Refresh Data</button>
        </div>

        <div id="loading" class="loading">
            <p>Loading validation data...</p>
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="dashboard" style="display: none;">
            <div class="status-card" id="status-card">
                <h2 id="status-title">System Status</h2>
                <p id="status-description"></p>
                <p class="timestamp" id="last-updated"></p>
            </div>

            <div class="grid">
                <div class="status-card">
                    <h3>📊 Summary Statistics</h3>
                    <div class="metric">
                        <span class="metric-value" id="total-attempts">-</span>
                        <span class="metric-label">Total Attempts</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="success-rate">-</span>
                        <span class="metric-label">Success Rate</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="failed-attempts">-</span>
                        <span class="metric-label">Failed Attempts</span>
                    </div>
                </div>

                <div class="status-card">
                    <h3>🌍 Environment Breakdown</h3>
                    <div class="metric">
                        <span class="metric-value" id="production-attempts">-</span>
                        <span class="metric-label">Production</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="sandbox-attempts">-</span>
                        <span class="metric-label">Sandbox</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="unknown-attempts">-</span>
                        <span class="metric-label">Unknown</span>
                    </div>
                </div>
            </div>

            <div class="status-card" id="failures-card" style="display: none;">
                <h3>❌ Recent Failures</h3>
                <div id="failures-list"></div>
            </div>

            <div class="status-card">
                <h3>💡 What This Means</h3>
                <div id="recommendations"></div>
            </div>
        </div>
    </div>

    <script>
        const SUPABASE_URL = 'https://lckiptkbxurjxddeubew.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxja2lwdGtieHVyanhkZGV1YmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA5MDI5NzQsImV4cCI6MjAzNjQ3ODk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with your actual anon key

        async function loadData() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('error').style.display = 'none';

            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/validation-monitoring`, {
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayData(data);

            } catch (error) {
                showError(`Failed to load data: ${error.message}`);
            }
        }

        function displayData(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';

            // Status card
            const statusCard = document.getElementById('status-card');
            statusCard.className = `status-card status-${data.status.toLowerCase().replace('_', '-')}`;
            
            document.getElementById('status-title').textContent = `${getStatusEmoji(data.status)} ${data.status}`;
            document.getElementById('status-description').textContent = getStatusDescription(data.status);
            document.getElementById('last-updated').textContent = `Last updated: ${new Date(data.timestamp).toLocaleString()}`;

            // Summary statistics
            document.getElementById('total-attempts').textContent = data.summary.totalAttempts;
            document.getElementById('success-rate').textContent = data.summary.successRate;
            document.getElementById('failed-attempts').textContent = data.summary.failedAttempts;

            // Environment breakdown
            document.getElementById('production-attempts').textContent = data.environmentBreakdown.production;
            document.getElementById('sandbox-attempts').textContent = data.environmentBreakdown.sandbox;
            document.getElementById('unknown-attempts').textContent = data.environmentBreakdown.unknown;

            // Recent failures
            if (data.recentFailures && data.recentFailures.length > 0) {
                document.getElementById('failures-card').style.display = 'block';
                const failuresList = document.getElementById('failures-list');
                failuresList.innerHTML = data.recentFailures.slice(0, 5).map(failure => `
                    <div class="failure-item">
                        <strong>${failure.productId}</strong><br>
                        <small>Error: ${failure.error}</small><br>
                        <small>Environment: ${failure.environment}</small><br>
                        <small class="timestamp">${new Date(failure.timestamp).toLocaleString()}</small>
                    </div>
                `).join('');
            }

            // Recommendations
            document.getElementById('recommendations').innerHTML = getRecommendations(data.status);
        }

        function getStatusEmoji(status) {
            const emojis = {
                'EXCELLENT': '🎉',
                'GOOD': '✅',
                'WARNING': '⚠️',
                'CRITICAL': '🚨',
                'NO_DATA': '📭'
            };
            return emojis[status] || '❓';
        }

        function getStatusDescription(status) {
            const descriptions = {
                'EXCELLENT': 'All receipt validations are working perfectly!',
                'GOOD': 'Receipt validation is working well with >95% success rate',
                'WARNING': 'Some validation issues detected, success rate 80-95%',
                'CRITICAL': 'Significant validation problems, success rate <80%',
                'NO_DATA': 'No validation attempts detected in the last 24 hours'
            };
            return descriptions[status] || 'Unknown status';
        }

        function getRecommendations(status) {
            const recommendations = {
                'EXCELLENT': '<p>🎉 Perfect! The Apple receipt validation fix is working correctly. Apple should approve your app.</p>',
                'GOOD': '<p>✅ System is performing well. Continue monitoring during Apple review.</p>',
                'WARNING': '<p>⚠️ Monitor for increasing failure patterns. Check recent failures for common issues.</p>',
                'CRITICAL': '<p>🚨 Check Apple Shared Secret configuration and network connectivity to Apple servers.</p>',
                'NO_DATA': '<p>📭 No validation attempts detected. This could mean no users are purchasing or there\'s a configuration issue.</p>'
            };
            return recommendations[status] || '<p>Monitor the system and check for any issues.</p>';
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').textContent = message;
        }

        // Load data on page load
        loadData();

        // Auto-refresh every 5 minutes
        setInterval(loadData, 5 * 60 * 1000);
    </script>
</body>
</html>

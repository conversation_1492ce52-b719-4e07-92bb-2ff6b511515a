import Constants from 'expo-constants';

/**
 * Get the hCaptcha site key from app config
 */
export const getHCaptchaSiteKey = (): string => {
  return Constants.expoConfig?.extra?.hcaptchaSiteKey || 'f3a0d298-4616-4274-a5fc-45ae6796f7da';
};

/**
 * Validate a hCaptcha token
 * @param token The token to validate
 * @returns True if the token is valid, false otherwise
 */
export const validateHCaptchaToken = (token: string | null | undefined): boolean => {
  if (!token) {
    return false;
  }

  // Check if the token is a non-empty string
  if (typeof token !== 'string' || token.trim() === '') {
    return false;
  }

  // We no longer use simulated tokens as we have a real implementation for all platforms

  // For real tokens, we check if they have a reasonable length
  // Real hCaptcha tokens are typically long strings
  return token.length > 20;
};

/**
 * Check if hCaptcha is available on the current platform
 * @returns True if hCaptcha is available, false otherwise
 */
export const isHCaptchaAvailable = (): boolean => {
  // hCaptcha is now available on all platforms through our implementation
  return true;
};

import { StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

import { Text, View } from '@/components/Themed';
import Colors from '@/constants/Colors';
import { useColorScheme } from '@/components/useColorScheme';
import { useLanguage } from '../../contexts/LanguageContext';
import LanguageSelector from '../../components/LanguageSelector';

export default function MoreScreen() {
  const colorScheme = useColorScheme();
  const tintColor = Colors[colorScheme ?? 'light'].tint;
  const { t } = useLanguage();

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>More Features</Text>
        <Text style={styles.subtitle}>Coming Soon</Text>

        <LanguageSelector style={styles.languageSelector} />
      </View>

      <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

      <View style={styles.featureContainer}>
        <Text style={styles.sectionTitle}>Future Features</Text>

        <View style={styles.featureItem}>
          <FontAwesome name="history" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>History</Text>
            <Text style={styles.featureDescription}>View your past medication extractions</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <FontAwesome name="sliders" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Export to CSV</Text>
            <Text style={styles.featureDescription}>Export medication data to CSV format</Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <FontAwesome name="share-alt" size={24} color={tintColor} style={styles.featureIcon} />
          <View style={styles.featureTextContainer}>
            <Text style={styles.featureTitle}>Share Results</Text>
            <Text style={styles.featureDescription}>Share medication information with others</Text>
          </View>
        </View>
      </View>

      <View style={styles.separator} lightColor="#eee" darkColor="rgba(255,255,255,0.1)" />

      <View style={styles.aboutContainer}>
        <Text style={styles.sectionTitle}>About</Text>
        <Text style={styles.aboutText}>
          This app extracts medication information from doctor notes. The current version allows you to take photos
          of doctor notes or select images from your gallery and get AI-powered medication extraction.
        </Text>
        <Text style={styles.aboutText}>
          The app extracts medication names, dosages, purposes, and fill dates in a structured format that can be easily parsed into a table.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 10,
  },
  languageSelector: {
    marginBottom: 15,
  },
  separator: {
    marginVertical: 30,
    height: 1,
    width: '100%',
  },
  featureContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  featureIcon: {
    marginRight: 15,
    width: 30,
    textAlign: 'center',
  },
  featureTextContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  featureDescription: {
    fontSize: 16,
    color: '#666',
  },
  aboutContainer: {
    marginBottom: 30,
  },
  aboutText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
    color: '#444',
  },
});

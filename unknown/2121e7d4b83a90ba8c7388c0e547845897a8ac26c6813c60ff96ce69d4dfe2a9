// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add resolver for Node.js modules
config.resolver.extraNodeModules = {
  // Node.js core modules
  buffer: path.join(__dirname, 'shims/node/buffer.js'),
  crypto: path.join(__dirname, 'shims/node/crypto.js'),
  dgram: path.join(__dirname, 'shims/node/dgram.js'),
  dns: path.join(__dirname, 'shims/node/dns.js'),
  events: path.join(__dirname, 'shims/node/events.js'),
  fs: path.join(__dirname, 'shims/node/fs.js'),
  http: path.join(__dirname, 'shims/node/http.js'),
  https: path.join(__dirname, 'shims/node/https.js'),
  net: path.join(__dirname, 'shims/node/net.js'),
  os: path.join(__dirname, 'shims/node/os.js'),
  path: path.join(__dirname, 'shims/node/path.js'),
  process: path.join(__dirname, 'shims/node/process.js'),
  querystring: path.join(__dirname, 'shims/node/querystring.js'),
  stream: path.join(__dirname, 'shims/node/stream.js'),
  'node:stream': path.join(__dirname, 'shims/node/node-stream.js'),
  tls: path.join(__dirname, 'shims/node/tls.js'),
  url: path.join(__dirname, 'shims/node/url.js'),
  util: path.join(__dirname, 'shims/node/util.js'),
  zlib: path.join(__dirname, 'shims/node/zlib.js'),

  // Add any other Node.js modules your app might need
  child_process: path.join(__dirname, 'shims/node/empty.js'),
  cluster: path.join(__dirname, 'shims/node/empty.js'),
  constants: path.join(__dirname, 'shims/node/empty.js'),
  domain: path.join(__dirname, 'shims/node/empty.js'),
  punycode: path.join(__dirname, 'shims/node/empty.js'),
  readline: path.join(__dirname, 'shims/node/empty.js'),
  repl: path.join(__dirname, 'shims/node/empty.js'),
  string_decoder: path.join(__dirname, 'shims/node/empty.js'),
  sys: path.join(__dirname, 'shims/node/empty.js'),
  timers: path.join(__dirname, 'shims/node/empty.js'),
  tty: path.join(__dirname, 'shims/node/empty.js'),
  vm: path.join(__dirname, 'shims/node/empty.js'),
};

module.exports = config;

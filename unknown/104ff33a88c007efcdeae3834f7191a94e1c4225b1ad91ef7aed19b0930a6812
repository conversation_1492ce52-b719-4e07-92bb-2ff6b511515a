import Constants from 'expo-constants';

// Initialize with API key from environment variables
const resendApiKey = Constants.expoConfig?.extra?.resendApiKey || process.env.EXPO_PUBLIC_RESEND_API_KEY;

if (!resendApiKey) {
  console.warn('Resend API key is not set. Email functionality will not work.');
}

// This is a simplified version of the Resend client that works in React Native
// It avoids using the @react-email/render package which has Node.js dependencies
export class ResendClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string = resendApiKey || '', baseUrl: string = 'https://api.resend.com') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async sendEmail(options: {
    from: string;
    to: string | string[];
    subject: string;
    html: string;
    reply_to?: string;
    cc?: string | string[];
    bcc?: string | string[];
    text?: string;
    tags?: Array<{ name: string; value: string }>;
    headers?: Record<string, string>;
    attachments?: Array<{ filename: string; content: string }>;
  }) {
    try {
      if (!this.apiKey) {
        throw new Error('Resend API key is not set');
      }

      // Convert arrays to comma-separated strings if needed
      const to = Array.isArray(options.to) ? options.to.join(',') : options.to;
      const cc = Array.isArray(options.cc) ? options.cc.join(',') : options.cc;
      const bcc = Array.isArray(options.bcc) ? options.bcc.join(',') : options.bcc;

      const response = await fetch(`${this.baseUrl}/emails`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          from: options.from,
          to,
          subject: options.subject,
          html: options.html,
          text: options.text,
          reply_to: options.reply_to,
          cc,
          bcc,
          tags: options.tags,
          headers: options.headers,
          attachments: options.attachments
        })
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data };
      }

      return { data };
    } catch (error) {
      console.error('Error sending email:', error);
      return { error };
    }
  }
}

// Create a client instance with the structure similar to the official Resend SDK
export const resend = {
  emails: {
    send: async (options: any) => {
      const client = new ResendClient();
      return client.sendEmail(options);
    }
  }
};

-- Migration to add a 5-minute maximum time limit for live pill count sessions
-- This applies to all users except admins to prevent system abuse

-- First, let's create a function to automatically end sessions that exceed the time limit
CREATE OR REPLACE FUNCTION public.auto_end_expired_sessions()
RETURNS void AS $$
DECLARE
  max_duration_seconds INTEGER := 300; -- 5 minutes (300 seconds)
BEGIN
  -- End all active sessions that have exceeded the time limit
  -- Skip sessions for admin users
  UPDATE public.live_session_tracking lst
  SET
    session_end = NOW(),
    is_active = FALSE,
    duration_seconds = EXTRACT(EPOCH FROM (NOW() - lst.session_start))
  WHERE
    lst.is_active = TRUE
    AND EXTRACT(EPOCH FROM (NOW() - lst.session_start)) > max_duration_seconds
    AND NOT EXISTS (
      SELECT 1 FROM public.profiles p
      WHERE p.id = lst.user_id AND p.subscription_tier = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically end sessions that exceed the time limit
-- This will run every minute via a cron job (set up separately)

-- Update the end_live_session function to enforce the time limit
CREATE OR REPLACE FUNCTION public.end_live_session(
  session_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  session_start TIMESTAMP WITH TIME ZONE;
  user_id UUID;
  is_admin BOOLEAN;
  max_duration_seconds INTEGER := 300; -- 5 minutes (300 seconds)
  current_duration_seconds INTEGER;
BEGIN
  -- Get session start time and user ID
  SELECT lst.session_start, lst.user_id INTO session_start, user_id
  FROM public.live_session_tracking lst
  WHERE lst.id = session_uuid;
  
  -- Check if user is admin
  SELECT (subscription_tier = 'admin') INTO is_admin
  FROM public.profiles
  WHERE id = user_id;
  
  -- Calculate current duration
  current_duration_seconds := EXTRACT(EPOCH FROM (NOW() - session_start));
  
  -- If session exceeds max duration and user is not admin, cap the duration
  IF current_duration_seconds > max_duration_seconds AND NOT is_admin THEN
    current_duration_seconds := max_duration_seconds;
  END IF;

  -- Update session
  UPDATE public.live_session_tracking
  SET
    session_end = NOW(),
    is_active = FALSE,
    duration_seconds = current_duration_seconds
  WHERE id = session_uuid;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the start_live_session function to check for and end any existing active sessions
CREATE OR REPLACE FUNCTION public.start_live_session(
  user_uuid UUID
) 
RETURNS UUID AS $$
DECLARE
  user_tier TEXT;
  session_count INTEGER;
  max_sessions INTEGER;
  new_session_id UUID;
  is_admin BOOLEAN;
BEGIN
  -- Get user's subscription tier
  SELECT COALESCE(subscription_tier, 'free') INTO user_tier
  FROM public.profiles 
  WHERE id = user_uuid;
  
  -- Check if user is admin
  is_admin := (user_tier = 'admin');
  
  -- Set max sessions based on subscription tier
  IF user_tier = 'free' OR user_tier IS NULL THEN
    max_sessions := 0; -- Free users: not available
  ELSIF user_tier = 'pro' THEN
    max_sessions := 5; -- Pro: 5 per day
  ELSIF user_tier = 'premium' OR user_tier = 'admin' THEN
    max_sessions := 2147483647; -- Premium/Admin: unlimited
  ELSE
    max_sessions := 0; -- Default
  END IF;
  
  -- If tier doesn't allow live sessions, return NULL
  IF max_sessions = 0 THEN
    RETURN NULL;
  END IF;
  
  -- Check daily session count
  SELECT COUNT(*) INTO session_count
  FROM public.daily_feature_usage
  WHERE user_id = user_uuid 
    AND feature_type = 'live_pill_scan' 
    AND usage_date = CURRENT_DATE;
    
  -- If limit reached, return NULL
  IF session_count >= max_sessions AND NOT is_admin THEN
    RETURN NULL;
  END IF;
  
  -- End any existing active sessions for this user
  UPDATE public.live_session_tracking
  SET
    session_end = NOW(),
    is_active = FALSE,
    duration_seconds = EXTRACT(EPOCH FROM (NOW() - session_start))
  WHERE
    user_id = user_uuid
    AND is_active = TRUE;
  
  -- Create new session
  INSERT INTO public.live_session_tracking (user_id, session_start, is_active)
  VALUES (user_uuid, NOW(), TRUE)
  RETURNING id INTO new_session_id;
  
  -- Track usage (skip for admin users)
  IF NOT is_admin THEN
    PERFORM public.track_daily_feature_usage(user_uuid, 'live_pill_scan');
  END IF;
  
  RETURN new_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add a comment to explain the purpose of this migration
COMMENT ON FUNCTION public.auto_end_expired_sessions IS 'Automatically ends live pill count sessions that exceed the 5-minute time limit. Does not apply to admin users.';
COMMENT ON FUNCTION public.end_live_session IS 'Ends a live pill count session and enforces the 5-minute time limit for non-admin users.';
COMMENT ON FUNCTION public.start_live_session IS 'Starts a new live pill count session, ends any existing active sessions, and tracks usage for non-admin users.';

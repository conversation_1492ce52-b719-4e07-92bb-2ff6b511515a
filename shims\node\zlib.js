// Minimal implementation of the Node.js zlib module for React Native
const { Buffer } = require('./buffer');
const Stream = require('./stream');

// Stub implementations that don't actually compress/decompress
class Zlib extends Stream {
  constructor(options) {
    super();
    this.options = options || {};
    this.bytesWritten = 0;
    this.buffer = [];
  }

  _transform(chunk, encoding, callback) {
    if (chunk) {
      if (typeof chunk === 'string') {
        chunk = Buffer.from(chunk, encoding);
      }
      this.bytesWritten += chunk.length;
      this.buffer.push(chunk);
    }
    callback();
  }

  _flush(callback) {
    const result = Buffer.concat(this.buffer);
    this.buffer = [];
    this.push(result);
    callback();
  }

  flush(kind, callback) {
    if (typeof kind === 'function') {
      callback = kind;
      kind = undefined;
    }
    this._flush((err) => {
      if (callback) callback(err);
    });
    return this;
  }

  close(callback) {
    this.end();
    if (callback) process.nextTick(callback);
    return this;
  }
}

class Deflate extends Zlib {}
class Inflate extends Zlib {}
class Gzip extends Zlib {}
class Gunzip extends Zlib {}
class DeflateRaw extends Zlib {}
class InflateRaw extends Zlib {}
class Unzip extends Zlib {}

function createDeflate(options) {
  return new Deflate(options);
}

function createInflate(options) {
  return new Inflate(options);
}

function createGzip(options) {
  return new Gzip(options);
}

function createGunzip(options) {
  return new Gunzip(options);
}

function createDeflateRaw(options) {
  return new DeflateRaw(options);
}

function createInflateRaw(options) {
  return new InflateRaw(options);
}

function createUnzip(options) {
  return new Unzip(options);
}

// Sync versions just return the input
function deflateSync(buffer, options) {
  return buffer;
}

function inflateSync(buffer, options) {
  return buffer;
}

function gzipSync(buffer, options) {
  return buffer;
}

function gunzipSync(buffer, options) {
  return buffer;
}

function deflateRawSync(buffer, options) {
  return buffer;
}

function inflateRawSync(buffer, options) {
  return buffer;
}

function unzipSync(buffer, options) {
  return buffer;
}

module.exports = {
  Deflate,
  Inflate,
  Gzip,
  Gunzip,
  DeflateRaw,
  InflateRaw,
  Unzip,
  createDeflate,
  createInflate,
  createGzip,
  createGunzip,
  createDeflateRaw,
  createInflateRaw,
  createUnzip,
  deflateSync,
  inflateSync,
  gzipSync,
  gunzipSync,
  deflateRawSync,
  inflateRawSync,
  unzipSync,
  constants: {
    Z_NO_FLUSH: 0,
    Z_PARTIAL_FLUSH: 1,
    Z_SYNC_FLUSH: 2,
    Z_FULL_FLUSH: 3,
    Z_FINISH: 4,
    Z_BLOCK: 5,
    Z_OK: 0,
    Z_STREAM_END: 1,
    Z_NEED_DICT: 2,
    Z_ERRNO: -1,
    Z_STREAM_ERROR: -2,
    Z_DATA_ERROR: -3,
    Z_MEM_ERROR: -4,
    Z_BUF_ERROR: -5,
    Z_VERSION_ERROR: -6,
    Z_NO_COMPRESSION: 0,
    Z_BEST_SPEED: 1,
    Z_BEST_COMPRESSION: 9,
    Z_DEFAULT_COMPRESSION: -1,
    Z_FILTERED: 1,
    Z_HUFFMAN_ONLY: 2,
    Z_RLE: 3,
    Z_FIXED: 4,
    Z_DEFAULT_STRATEGY: 0,
    Z_DEFAULT_WINDOWBITS: 15,
    Z_MIN_WINDOWBITS: 8,
    Z_MAX_WINDOWBITS: 15,
    Z_MIN_CHUNK: 64,
    Z_MAX_CHUNK: Infinity,
    Z_DEFAULT_CHUNK: 16384,
    Z_MIN_MEMLEVEL: 1,
    Z_MAX_MEMLEVEL: 9,
    Z_DEFAULT_MEMLEVEL: 8
  }
};
